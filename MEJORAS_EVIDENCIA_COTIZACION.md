# Evidencia Obligatoria en Cotizaciones

## Implementación Completada ✅

La funcionalidad de evidencia obligatoria está implementada y funcionando:

- ✅ **Evidencia obligatoria** por cada servicio
- ✅ **Validación estricta** - no se puede enviar sin evidencia
- ✅ **Feedback visual** - errores claros cuando falta evidencia
- ✅ **Vista previa de archivos** con gestión completa
- ✅ **Validación de tipos y tamaños** de archivo
- ✅ **Integración con API** usando FormData siempre

## Mejoras Futuras Sugeridas

### 1. **Vista Previa de Imágenes**
```typescript
// Agregar preview de imágenes en miniatura
const ImagePreview = ({ file }: { file: File }) => {
  const [preview, setPreview] = useState<string>('');

  useEffect(() => {
    const url = URL.createObjectURL(file);
    setPreview(url);
    return () => URL.revokeObjectURL(url);
  }, [file]);

  return <img src={preview} className="w-16 h-16 object-cover rounded" />;
};
```

### 2. **Drag & Drop Mejorado**
```typescript
// Implementar zona de drag & drop más intuitiva
const onDrop = useCallback((acceptedFiles: File[]) => {
  handleFileChange(serviceId, { target: { files: acceptedFiles } });
}, [serviceId]);

const { getRootProps, getInputProps, isDragActive } = useDropzone({
  onDrop,
  accept: { 'image/*': [], 'video/*': [] },
  maxSize: 10 * 1024 * 1024 // 10MB
});
```

### 3. **Compresión de Imágenes**
```typescript
// Comprimir imágenes antes de subir
import imageCompression from 'browser-image-compression';

const compressImage = async (file: File) => {
  const options = {
    maxSizeMB: 2,
    maxWidthOrHeight: 1920,
    useWebWorker: true
  };
  return await imageCompression(file, options);
};
```

### 4. **Progreso de Subida**
```typescript
// Mostrar progreso de subida
const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});

// En axios config:
onUploadProgress: (progressEvent) => {
  const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
  setUploadProgress(prev => ({ ...prev, [serviceId]: progress }));
}
```

### 5. **Validaciones Adicionales**
```typescript
// Validar dimensiones de imagen
const validateImageDimensions = (file: File): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      resolve(img.width <= 4000 && img.height <= 4000);
    };
    img.src = URL.createObjectURL(file);
  });
};
```

### 6. **Metadatos de Archivos**
```typescript
// Capturar metadatos adicionales
interface EvidenceFile extends File {
  description?: string;
  category?: 'before' | 'during' | 'after';
  timestamp?: string;
}
```

### 7. **Almacenamiento Temporal**
```typescript
// Guardar archivos en localStorage temporalmente
const saveToLocalStorage = (serviceId: string, files: File[]) => {
  // Convertir a base64 para almacenamiento temporal
  // Útil si el usuario cierra el modal accidentalmente
};
```

## Consideraciones de Backend

### API Endpoints Sugeridos:
- `POST /quotations/{id}/evidence` - Subir evidencia
- `GET /quotations/{id}/evidence` - Obtener evidencia
- `DELETE /quotations/{id}/evidence/{fileId}` - Eliminar archivo

### Estructura de Respuesta:
```json
{
  "success": true,
  "data": {
    "quotationId": "...",
    "evidence": [
      {
        "id": "...",
        "serviceId": "...",
        "filename": "...",
        "url": "...",
        "type": "image",
        "size": 1024000,
        "uploadedAt": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

## Testing

### Casos de Prueba:
1. ✅ Subir imagen válida
2. ✅ Subir video válido
3. ✅ Archivo muy grande (>10MB)
4. ✅ Tipo de archivo inválido
5. ✅ Múltiples archivos por servicio
6. ✅ Eliminar archivo individual
7. ✅ Cerrar modal sin perder datos
8. ✅ Enviar cotización con evidencia
9. ✅ Enviar cotización sin evidencia

## Próximos Pasos

1. **Probar la funcionalidad** en el entorno de desarrollo
2. **Verificar integración** con el backend
3. **Implementar mejoras** según necesidades del usuario
4. **Documentar** el flujo para otros desarrolladores
