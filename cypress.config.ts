/* eslint-disable @typescript-eslint/default-param-last */
/* eslint-disable import/no-extraneous-dependencies */
import { defineConfig } from 'cypress';

export default defineConfig({
  // setupNodeEvents can be defined in either
  // the e2e or component configuration
  env: {
    email: '<EMAIL>',
    password: 'xl3',
  },
  e2e: {
    baseUrl: 'http://localhost:8080',
    setupNodeEvents(on) {
      on(
        'before:browser:launch',
        (
          browser = {
            name: '',
            family: 'chromium',
            channel: '',
            displayName: '',
            version: '',
            majorVersion: '',
            path: '',
            isHeaded: false,
            isHeadless: false,
          },
          launchOptions
        ) => {
          // `args` is an array of all the arguments that will
          // be passed to browsers when it launches
          console.log(launchOptions.args); // print all current args

          if (browser.family === 'chromium' && browser.name !== 'electron') {
            // auto open devtools
          }

          if (browser.family === 'firefox') {
            // auto open devtools
            launchOptions.args.push('-devtools');
          }

          if (browser.name === 'electron') {
            // auto open devtools
            launchOptions.preferences.devTools = true;
          }

          // whatever you return here becomes the launchOptions
          return launchOptions;
        }
      );
    },
  },
});
