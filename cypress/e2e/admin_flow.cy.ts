import { randomVIM } from '../utils';
import { generateRandomDate, generateRandom<PERSON>erson } from '../utils/faker';

describe('It should create a new vehicle', () => {
  let carNumber: string = '1049';
  context('Vehicle created context', () => {
    it('Should be on flotilla/stock', () => {
      cy.login(Cypress.env('email'), Cypress.env('password'));
      cy.visit('dashboard/flotilla/stock');

      cy.get('[data-cy="add"]').should('be.visible').click();
      cy.get('[data-cy="region-selector"]').should('be.visible');
      cy.get('[data-cy="color-selector"]').should('be.visible');

      cy.setSelectOption({ selector: '.react-select__control', name: '.vehicleState', eq: 0 });
      cy.setSelectOption({ selector: '.react-select__control', name: '.color', eq: 0 });

      cy.get('input[name="brand"]').type('Sedan');
      cy.get('input[name="model"]').type('Modelo');
      cy.get('input[name="version"]').type('Test');
      cy.get('input[name="year"]').type('2023');

      cy.get('input[name="owner"]').type('OCN');
      cy.get('input[name="vin"]').type(randomVIM());
      cy.get('input[name="km"]').type('30');
      cy.get('input[type=file]').selectFile('cypress/assets/sampleCatorce.pdf', {
        action: 'drag-drop',
        force: true,
      });
      cy.get('input[name="billAmount"]').type('1000000{enter}');
      cy.contains('Vehiculo agregado a stock correctamente', { timeout: 5000 });
      // cy.wait(30000);
      // cy.location('pathname', { timeout: 10000 }).should('include', '/nueva-ruta-esperada');
      cy.location('pathname', { timeout: 10000 }).should(
        'match',
        /^\/dashboard\/flotilla\/stock\/[0-9a-fA-F]+$/
      );
      cy.get('[data-cy="cy-carNumber"]', { timeout: 10000 }).should('be.visible');
      cy.get('[data-cy="cy-carNumber"]')
        .invoke('text')
        .then((value) => {
          if (value) {
            carNumber = value;
          }
        });
    });

    describe('Go through all the stock flow', () => {
      it('Should search for the created vehicle and enter the detail of it', function () {
        cy.login(Cypress.env('email'), Cypress.env('password'));
        cy.visit('dashboard/flotilla/stock');
        cy.get('nav').contains('Flotilla').click();
        cy.get('button').contains('Stock').click();
        cy.get('#search').type(`${carNumber}{enter}`);

        cy.url({ timeout: 20000 }).should('include', 'search');
        cy.get('[data-cy="stock-card"]', { timeout: 10000 }).click().debug();
        cy.url({ timeout: 40000 }).should('not.include', 'search').debug();
        cy.contains(`${carNumber}`);
        cy.location('pathname').as('stockUrl');
        cy.log(this.stockUrl, 'Stock Url');
      });

      it('Should update the vehicle details', function () {
        cy.login(Cypress.env('email'), Cypress.env('password'));

        cy.visit(`${this.stockUrl}`);
        cy.location('href', { timeout: 10000 });

        cy.contains(`${carNumber}`);

        cy.url().should('include', 'flotilla');
        cy.contains('Datos del vehiculo');
        cy.get('[data-cy="edit-description"]').click({ multiple: true });
        cy.contains('Editar').click();

        cy.get('input[name="brand"]').clear().type(' Modified');
        cy.get('input[name="model"]').clear().type(' Modified');
        cy.get('input[name="version"]').clear().type(' Modified');
        cy.get('input[name="year"]').clear().type('2');
        cy.get('input[name="color"]').clear().type(' Modified');
        cy.get('input[name="owner"]').clear().type(' Modified');
        cy.get('input[name="vin"]').clear().type(randomVIM());
        cy.get('input[name="km"]').clear().type('1');
        cy.get('input[type=file]').selectFile('cypress/assets/sampleCatorce.pdf', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="billAmount"]').type('2{enter}');
      });
      it('Should edit the carPlates', function () {
        cy.login(Cypress.env('email'), Cypress.env('password'));
        cy.visit(`${this.stockUrl}`);

        cy.get('[data-cy="carPlates"]', { timeout: 20000 }).click();
        cy.get('input[name="frontImg"]').selectFile('cypress/assets/test.png', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="backImg"]').selectFile('cypress/assets/test.png', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="platesDocument"]').selectFile('cypress/assets/sampleCatorce.pdf', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('[data-cy="post"]');
        cy.get('input[name="plates"]')
          .clear()
          .type(randomVIM().substring(0, 12) + '{enter}');
        cy.contains('Vehiculo actualizado!', { timeout: 5000 });
      });

      it('Should edit the circulationCard', function () {
        cy.login(Cypress.env('email'), Cypress.env('password'));
        cy.visit(`${this.stockUrl}`);

        cy.get('[data-cy="circulationCard"]', { timeout: 20000 }).click();
        cy.get('input[name="number"]').clear().type('123456');
        cy.get('input[name="frontImg"]').selectFile('cypress/assets/test.png', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="backImg"]').selectFile('cypress/assets/test.png', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="validity"]').as('validity').click();
        cy.get('@validity').type('2025-12-12');
        cy.get('input[name="number"]').type('{enter}');
        cy.contains('Vehiculo actualizado!', { timeout: 5000 });
      });

      // it('Should edit the gps', function () {
      //   cy.login(Cypress.env('email'), Cypress.env('password'));
      //   cy.visit(`${this.stockUrl}`);

      //   cy.get('[data-cy="gps"]', { timeout: 20000 }).click();
      //   cy.get('input[name="gpsNumber"]').clear().type('GPS OCN');
      //   cy.get('input[name="gpsSerie"]').clear().type('123456');
      //   // eslint-disable-next-line cypress/no-unnecessary-waiting
      //   cy.wait(1000);
      //   cy.get('[data-cy="post"]').click();
      //   cy.contains('Vehiculo actualizado!', { timeout: 5000 });
      // });

      it('Should add the policy', function () {
        cy.login(Cypress.env('email'), Cypress.env('password'));
        cy.visit(`${this.stockUrl}`);

        cy.get('[data-cy="policy"]', { timeout: 20000 }).click();
        cy.get('input[name="insurer"]', { timeout: 3000 }).type('Qualitas');
        // This is not needed, the field can't be edited
        cy.get('input[name="validity"]').as('validity').click();
        cy.get('@validity').type('2025-12-12');
        cy.get('input[name="broker"]').type('OCN');
        cy.get('input[type=file]').selectFile('cypress/assets/sampleCatorce.pdf', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('[data-cy="post"]').should('be.enabled').click();
        cy.get('input[name="policyNumber"]').type('1234567{enter}');
        cy.contains('Vehiculo actualizado!', { timeout: 5000 });
      });

      it('Should add the tenancy', function () {
        if (carNumber.startsWith('1') && carNumber.length === 4) {
          cy.login(Cypress.env('email'), Cypress.env('password'));
          cy.visit(`${this.stockUrl}`);

          cy.get('[data-cy="tenancy"]', { timeout: 20000 }).click();
          cy.get('input[type=file]').selectFile('cypress/assets/sampleCatorce.pdf', {
            action: 'drag-drop',
            force: true,
          });
          cy.get('input[name="validity"]').as('validity').click();
          cy.get('@validity').type('2028-04-03');
          cy.get('[data-cy="post"]').click();
          cy.get('input[name="payment').type('1026{enter}');
          cy.contains('Vehiculo actualizado!', { timeout: 5000 });
        } else {
          // Si la variable de entorno indica que el test no debe ejecutarse, se omite
          cy.log('Skipping the tenancy test because the runTenancyTest environment variable is not set.');
        }
      });

      it('Should fill the associate modal', function () {
        cy.login(Cypress.env('email'), Cypress.env('password'));
        cy.visit(`${this.stockUrl}`, {
          timeout: 20000,
        });

        cy.get('[data-cy="driverAssign"]', { timeout: 10000 }).click();
        cy.get('input[name="firstName"]').type(generateRandomPerson().firstName);
        cy.get('input[name="lastName"]').type(generateRandomPerson().lastName);
        cy.get('input[name="phone"]').type(generateRandomPerson().phone);
        cy.get('input[name="birthDay"]').as('birthDay').click();
        cy.get('@birthDay').type(generateRandomDate());
        // const uniqueEmail = `test${Cypress._.random(0, 9999)}@example.com`;
        const uniqueEmail = generateRandomPerson().email;

        cy.get('input[name="email"]').type(uniqueEmail);
        cy.get('input[name="curp"]').type('PAPE030598HTCTDNA7');
        cy.get('input[name="rfc"]').type('XAXX010101000');
        cy.get('[data-cy="next"]', { timeout: 5000 }).should('be.enabled').click();

        cy.get('input[name="addressStreet"]', { timeout: 50000 }).type('Calle test imaginaria');
        cy.get('input[name="exterior"]').type('12A');
        cy.get('input[name="interior"]').type('203');
        cy.get('input[name="postalCode"]').type('12345');
        cy.get('input[name="colony"]').type('Local');
        cy.get('input[name="delegation"]').type('Benito Juarez');
        // cy.get('#state').should('be.visible');
        cy.get('#state > .css-13cymwt-control').click();
        cy.get('#state > .css-13cymwt-control').click();
        cy.contains('Jalisco').click();

        cy.get('#city > .css-13cymwt-control').click();
        cy.get('.css-t3ipsp-control > .css-1fdsijx-ValueContainer > .css-qbdosj-Input')
          .click()
          .type('Guadalajara{enter}');
        cy.get('[data-cy="next"]', { timeout: 5000 }).should('be.enabled').click();

        cy.get('input[name="ineFront"]').selectFile('cypress/assets/test.png', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="ineBack"]').selectFile('cypress/assets/test.png', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="curpDoc"]').selectFile('cypress/assets/sampleCatorce.pdf', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="taxStatus"]').selectFile('cypress/assets/sampleCatorce.pdf', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="addressVerification"]').selectFile('cypress/assets/sampleCatorce.pdf', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="bankStatementsOne"]').selectFile('cypress/assets/sampleCatorce.pdf', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="bankStatementsTwo"]').selectFile('cypress/assets/sampleCatorce.pdf', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="bankStatementsThree"]').selectFile('cypress/assets/sampleCatorce.pdf', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="bankStatementsFour"]').selectFile('cypress/assets/sampleCatorce.pdf', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="bankStatementsFive"]').selectFile('cypress/assets/sampleCatorce.pdf', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="bankStatementsSix"]').selectFile('cypress/assets/sampleCatorce.pdf', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="driverLicenseFront"]').selectFile('cypress/assets/test.png', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="driverLicenseBack"]').selectFile('cypress/assets/test.png', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="garage"]').selectFile('cypress/assets/test.png', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('[data-cy="next"]', { timeout: 5000 }).should('be.enabled').click();

        cy.get('.swal2-confirm', { timeout: 5000 }).click();

        cy.contains('Conductor asignado', { timeout: 10000 });
        // eslint-disable-next-line cypress/no-unnecessary-waiting
        cy.wait(10000);
      });

      it('Should add the contract', function () {
        cy.login(Cypress.env('email'), Cypress.env('password'));
        cy.visit(`${this.stockUrl}`, {
          timeout: 25000,
        });
        cy.get('[data-cy="contract"]', { timeout: 10000 }).click();
        // cy.get('#city > .css-13cymwt-control').click();
        // cy.get('#react-select-5-option-0', { timeout: 5000 }).click();
        // cy.setSelectOption({ selector: '.react-select__control', name: '.city', eq: 0 });
        cy.get('[data-cy="next"]', { timeout: 5000 }).should('be.enabled').click();
        cy.get('[data-cy="next"]', { timeout: 5000 }).should('be.enabled').click();
        cy.get('[data-cy="next"]', { timeout: 5000 }).should('be.enabled').click();
        cy.get('[data-cy="next"]', { timeout: 5000 }).should('be.enabled').click();
        cy.get('input[name="weeklyRent"]', { timeout: 5000 }).type('4500');
        cy.get('input[name="finalPrice"]', { timeout: 5000 }).type('15000000');

        // cy.get('#insurance > .css-13cymwt-control').click();
        // cy.get('#insurance > .css-13cymwt-control').click();
        // cy.get('#react-select-39-option-0').click();
        cy.get('[data-cy="next"]', { timeout: 5000 }).should('be.enabled').click();
        cy.get('input[name="deliverDate"]').as('deliverDate').click();
        cy.get('@deliverDate').type('2025-12-12T11:11');
        cy.get('[data-cy="next"]', { timeout: 5000 }).should('be.enabled').click();
        cy.contains('Contrato generado exitosamente', { timeout: 10000 });
      });

      it('Should add the contract documents', function () {
        cy.login(Cypress.env('email'), Cypress.env('password'));
        cy.visit(`${this.stockUrl}`, {
          timeout: 25000,
        });
        cy.get('[data-cy="docsContract"]', { timeout: 10000 }).click();

        cy.get('input[name="contract"]').selectFile('cypress/assets/sampleCatorce.pdf', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="promissoryNote"]').selectFile('cypress/assets/sampleCatorce.pdf', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="deliveryReceipt"]').selectFile('cypress/assets/sampleCatorce.pdf', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="warranty"]').selectFile('cypress/assets/sampleCatorce.pdf', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="invoice"]').selectFile('cypress/assets/sampleCatorce.pdf', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="privacy"]').selectFile('cypress/assets/sampleCatorce.pdf', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('input[name="contactInfo"]').selectFile('cypress/assets/sampleCatorce.pdf', {
          action: 'drag-drop',
          force: true,
        });
        cy.get('[data-cy="post"]', { timeout: 5000 }).should('be.enabled').click();
        // eslint-disable-next-line cypress/no-unnecessary-waiting
        cy.wait(10000);
      });
    });
  });
});
