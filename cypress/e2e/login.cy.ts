describe('Login', () => {
  it('login with wrong email', () => {
    cy.visit('/');
    cy.get('input[name="email"]').type(Cypress.env('email'));
    cy.get('input[name="password"]').type(Cypress.env('password'));
    cy.get('button[type="submit"]').click();
    cy.on('window:alert', (str) => {
      expect(str).to.equal('Usuario no encontrado');
    });
  });

  it('login with wrong password', () => {
    cy.visit('/');
    cy.get('input[name="email"]').type(Cypress.env('email'));
    cy.get('input[name="password"]').type('PassWord!Fake332');
    cy.get('button[type="submit"]').click();
    cy.on('window:alert', (str) => {
      expect(str).to.equal('Usuario o contraseña incorrecta');
    });
  });

  it('login with correct credentials', () => {
    cy.visit('/');
    cy.get('input[name="email"]').type(Cypress.env('email'));
    cy.get('input[name="password"]').type(Cypress.env('password'));
    cy.get('button[type="submit"]').click();
    cy.url({ timeout: 30000 }).should('include', '/dashboard');
  });
});
