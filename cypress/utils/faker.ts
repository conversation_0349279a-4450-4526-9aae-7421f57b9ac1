import { faker } from '@faker-js/faker';

// Función para generar una dirección aleatoria separada por cada campo
export const generateRandomAddress = () => {
  const streetName = faker.location.street();
  const streetNumber = faker.number.int({ min: 1, max: 9999 });
  const city = faker.location.city();
  const state = faker.location.state();
  const postalCode = faker.location.zipCode('#####'); // '#####' genera un código postal de 5 caracteres
  const country = 'México';
  return {
    streetName,
    streetNumber,
    city,
    state,
    postalCode,
    country,
  };
};

// Función para generar una fecha aleatoria a partir de hoy
export const generateRandomDate = () => {
  const today = new Date();
  const randomNumberOfDays = faker.number.int({ min: 1, max: 365 });
  const randomDate = new Date(today.getTime() + randomNumberOfDays * 24 * 60 * 60 * 1000);
  return randomDate.toISOString().split('T')[0]; // Formato 'YYYY-MM-DD'
};

// Función para generar un nombre aleatorio
export const generateRandomPerson = () => {
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();
  const phone = faker.phone.number().replace(/-/g, '').substring(0, 10); // Elimina los guiones
  const email = faker.internet.email();
  return {
    firstName,
    lastName,
    phone,
    email,
  };
};

// export const generateRandomCar = () => {
//   const brand = faker.vehicle.manufacturer();
//   const model = faker.vehicle.model();
//   const carNumber = faker.vehicle.vrm();

//   return {
//     brand,
//     model,
//     carNumber,
//   };
// };
