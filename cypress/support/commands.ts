/* eslint-disable cypress/no-force */
/// <reference types="cypress" />

Cypress.Commands.add('login', (email, password) => {
  cy.session([email, password], () => {
    cy.visit('/', { timeout: 50000 });
    cy.get('input[name="email"]').type(email);
    cy.get('input[name="password"]').type(password);
    cy.get('button[type="submit"]').click();
    cy.url({ timeout: 30000 }).should('include', '/dashboard');
  });
});

Cypress.Commands.add(
  'setSelectOption',
  ({ selector, name, eq }: { selector: string; name: string; eq: number }) => {
    cy.get(name + selector)
      .eq(eq)
      .click({ force: true }) // click to open dropdown
      .get(`${name}.react-select__menu`)
      .find(`${name}.react-select__option`) // find all options
      // .first()
      .then((options) => {
        // Selecciona una opción aleatoria
        const randomIndex = Math.floor(Math.random() * options.length);
        cy.wrap(options[randomIndex], { timeout: 5000 }).click({ force: true });
      });
    // .then(() => {
    //   // Espera 1 segundo para darle tiempo a la página para que se estabilice
    //   // cy.wait(1000);
    // });
  }
);

// En desarrollo!!!!!
// Cypress.Commands.add('resetStock', () => {
//   cy.request({
//     method: 'GET',
//     url: 'https://dev-api.onecarnow.com/stock/delete',
//     headers: {
//       Authorization: '<<AQUI EL TOKEN>>',
//     },
//   });
// });
