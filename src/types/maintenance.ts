export enum MaintenanceType {
  PREVENTIVE = "preventive",
  CORRECTIVE = "corrective",
}

export enum FailureType {
  KNOWN = "known",
  UNKNOWN = "unknown",
}

export enum VehicleCondition {
  CAN_DRIVE = "can_drive",
  NEEDS_TOW = "needs_tow",
}

export enum ServiceStatus {
  NOT_STARTED = "not_started",
  IN_PROGRESS = "in_progress",
  COMPLETED = "completed",
  PENDING_PARTS = "pending_parts",
  CANCELLED = "cancelled",
}

export enum OrderStatus {
  ACTIVE = "active",
  PENDING_APPROVAL = "pending_approval",
  PENDING_PARTS = "pending_parts",
  IN_REPAIR = "in_repair",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
  PENDING_FINANCING = "pending_financing",
}

export enum ApprovalStatus {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected",
  PARTIAL = "partial",
}

export interface MaintenanceRequest {
  id: string;
  vehicleId: string;
  clientId: string;
  workshopId: string;
  type: MaintenanceType;
  failureType?: FailureType;
  vehicleCondition?: VehicleCondition;
  description: string;
  scheduledDate: string;
  status: OrderStatus;
  createdAt: string;
  updatedAt: string;
  services: MaintenanceService[];
  quotation?: Quotation;
  approval?: Approval;
  totalCost: number;
  totalEstimatedTime: number;
  actualCompletionTime?: number;
}

export interface MaintenanceService {
  id: string;
  orderId: string;
  name: string;
  description: string;
  estimatedTime: number; // en horas
  actualTime?: number;
  cost: number;
  status: ServiceStatus;
  requiredParts: RequiredPart[];
  evidence: Evidence[];
  slaDeadline: string;
  startedAt?: string;
  completedAt?: string;
  canRunInParallel: boolean;
  dependencies: string[]; // IDs de servicios que deben completarse antes
}

export interface RequiredPart {
  id: string;
  name: string;
  partNumber: string;
  quantity: number;
  unitCost: number;
  totalCost: number;
  supplier: string;
  estimatedArrival: string; // ETA
  actualArrival?: string;
  status: "pending" | "ordered" | "in_transit" | "arrived" | "installed";
}

export interface Evidence {
  id: string;
  type: "photo" | "video" | "document";
  url: string;
  description: string;
  uploadedAt: string;
  uploadedBy: string;
}

export interface Quotation {
  id: string;
  orderId: string;
  services: QuotationService[];
  totalCost: number;
  totalEstimatedTime: number;
  estimatedCompletionDate: string;
  validUntil: string;
  createdAt: string;
  createdBy: string;
  notes?: string;
}

export interface QuotationService {
  serviceId: string;
  name: string;
  description: string;
  cost: number;
  estimatedTime: number;
  requiredParts: RequiredPart[];
  slaDeadline: string;
  canBeRejected: boolean;
}

export interface Approval {
  id: string;
  quotationId: string;
  approverType: "client" | "fleet" | "vendor";
  approverId: string;
  status: ApprovalStatus;
  approvedServices: string[]; // IDs de servicios aprobados
  rejectedServices: string[]; // IDs de servicios rechazados
  totalApprovedCost: number;
  approvalDate?: string;
  rejectionReason?: string;
  notes?: string;
}

export interface DiagnosticReport {
  id: string;
  orderId: string;
  findings: DiagnosticFinding[];
  recommendedServices: string[];
  urgencyLevel: "low" | "medium" | "high" | "critical";
  evidence: Evidence[];
  createdAt: string;
  createdBy: string;
}

export interface DiagnosticFinding {
  id: string;
  component: string;
  issue: string;
  severity: "minor" | "moderate" | "severe" | "critical";
  description: string;
  evidence: Evidence[];
}

export interface MaintenanceHistory {
  vehicleId: string;
  orders: MaintenanceRequest[];
  totalMaintenanceTime: number;
  totalCost: number;
  lastMaintenanceDate: string;
  upcomingMaintenance?: {
    type: MaintenanceType;
    estimatedDate: string;
    description: string;
  };
}

export interface SLAMetrics {
  orderId: string;
  serviceId?: string;
  estimatedTime: number;
  actualTime?: number;
  slaCompliance: boolean;
  delayReason?: string;
  delayTime?: number;
}

export interface NotificationPreferences {
  userId: string;
  emailNotifications: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
  notificationTypes: {
    quotationReady: boolean;
    approvalRequired: boolean;
    serviceStarted: boolean;
    serviceCompleted: boolean;
    partsDelayed: boolean;
    vehicleReady: boolean;
  };
}
