import { StockService } from '@/actions/getStockServices';
import { create } from 'zustand';

interface Modals {
  isOpen: boolean;
  onOpen: () => void;
  onClose: () => void;
}

export const createModalState = () =>
  create<Modals>((set) => ({
    isOpen: false,
    onOpen: () => set(() => ({ isOpen: true })),
    onClose: () => set(() => ({ isOpen: false })),
  }));

export const useOpenDetailDischargeModal = createModalState();

export const useOpenDischargeModal = createModalState();

export const useOpenChangeStatusModal = createModalState();

export const useOpenOverHaulingModal = createModalState();
export const useOpenFinishOverHaulingModal = createModalState();

export const useOpenFinishTaller = createModalState();

export const useAdendumGenerator = createModalState();

export const useReturnStepModal = createModalState();

export const useReturnChangeRegion = createModalState();

export const useCirculationCardTijModal = createModalState();

export const useAppointmentModal = createModalState();

interface ServiceModalProps extends Modals {
  serviceData: StockService;
  setServiceData: (data: StockService) => void;
}

export const useOpenServiceDetail = create<ServiceModalProps>((set) => ({
  isOpen: false,
  serviceData: {} as StockService,
  setServiceData: (data: StockService) => set(() => ({ serviceData: data })),
  onOpen: () => set(() => ({ isOpen: true })),
  onClose: () => set(() => ({ isOpen: false })),
}));
