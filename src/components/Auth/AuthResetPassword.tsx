'use client';

import ResetPasswordForm from '@/components/Form/ResetPasswordForm';
import { useRouter } from 'next/navigation';
import { Formik, FormikState, FormikValues } from 'formik';
import { resetPasswordValidation } from '../Form/YupValidator';
import { IconContext } from 'react-icons/lib';
import { useEffect, useMemo, useState } from 'react';
import Spinner from '../Loading/Spinner';
import { URL_API } from '@/constants';
import sendResetPassword from '@/actions/sendResetPasswordEmail';

type HandleSubmitType = (
  values: FormikValues,
  {
    resetForm,
  }: {
    resetForm: (nextState?: Partial<FormikState<FormikValues>> | undefined) => void;
  }
) => Promise<void>;

export default function AuthResetPassword() {
  const router = useRouter();

  const [isInitialValid, setIsInitialValid] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const defaultValues = useMemo(() => {
    return {
      email: '',
    };
  }, []);

  useEffect(() => {
    resetPasswordValidation
      .validate(defaultValues)
      .then(() => setIsInitialValid(true))
      .catch(() => setIsInitialValid(false));
  }, [defaultValues, isInitialValid]);

  const handleSubmit: HandleSubmitType = async (values, { resetForm }) => {
    setIsLoading(true);
    const result = await sendResetPassword({
      email: values.email,
    })
      .catch((err) => {
        console.error(err);
      })
      .finally(() => setIsLoading(false));
    if (result?.stack?.includes('Error')) {
      setIsLoading(false);
      resetForm();
      // setFieldError('email', result.error);
      return alert(result.message);
    } else {
      // Redirige a la página de dashboard después del inicio de sesión exitoso
      resetForm();
      const referrer = document.referrer;
      if (referrer && referrer !== '' && referrer.includes(URL_API)) {
        return router.back(); // Redirige a la página anterior si existe una ruta anterior
      } else {
        return router.push('/');
        // Lógica adicional si no hay ruta anterior
      }
    }
  };
  if (isLoading) {
    return <Spinner />;
  }

  return (
    <IconContext.Provider value={{ style: { strokeWidth: '1px' } }}>
      <Formik
        initialValues={defaultValues}
        validationSchema={resetPasswordValidation}
        onSubmit={handleSubmit}
        validateOnMount={true}
      >
        {({ isValid, dirty }) => <ResetPasswordForm valid={isValid} dirty={dirty} />}
      </Formik>
    </IconContext.Provider>
  );
}
