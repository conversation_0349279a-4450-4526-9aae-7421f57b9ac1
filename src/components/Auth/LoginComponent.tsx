"use client";
import { signIn, useSession } from "next-auth/react";
import LoginForm from "@/components/Form/LoginForm";
import { useRouter } from "next/navigation";
import { Formik, FormikState, FormikValues } from "formik";
import { loginValidation } from "../Form/YupValidator";
import { IconContext } from "react-icons/lib";
import { useEffect, useState } from "react";
import { setCookie } from "cookies-next";
import Spinner from "../Loading/Spinner";
import { URL_API } from "@/constants";
import Image from "next/image";
import { useI18n } from "@/i18n/client";

type HandleSubmitType = (
  values: FormikValues,
  {
    resetForm,
  }: {
    resetForm: (
      nextState?: Partial<FormikState<FormikValues>> | undefined
    ) => void;
  }
) => Promise<void>;

const defaultValues = {
  email: "",
  password: "",
}

export default function Login() {
  const t = useI18n();
  const router = useRouter();
  const { data: session, status } = useSession();

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (session) {
      setCookie("cookieKey", session.user.accessToken);
      router.push("/dashboard");
    }
  }, [session, status, router]);

  const handleSubmit: HandleSubmitType = async (values, { resetForm }) => {
    setIsLoading(true);
    let result;
    try {
      result = await signIn("credentials", {
        email: values.email,
        password: values.password,
        redirect: false,
      })
      setIsLoading(false);
      resetForm();
      const referrer = document.referrer;
      if (referrer && referrer !== "" && referrer.includes(URL_API)) {
        router.back();
      } else {
        router.push("/dashboard");
      }
    } catch (err) {
      setIsLoading(false);
      resetForm();
      alert(result?.error || t("ErrorOccuredWhileLogin"));
    }
  };

  if (status === "loading" || isLoading) {
    return <Spinner />;
  }

  return (
    <IconContext.Provider value={{ style: { strokeWidth: "1px" } }}>
      <section className="w-screen h-screen grid grid-cols-1 md:grid-cols-[45%_55%] grid-rows-[64px_1fr]">
        <div className="col-span-2 flex items-center px-4">
          <LoginTopBarImage />
        </div>
        <div className="flex flex-col justify-center px-6 lg:px-10 xl:ps-16 lg:pe-32">
          <div className="py-8">
            <h1 className="text-3xl font-bold  text-primaryHeading ">
              {t("WelcomeToOCN")}
            </h1>
            <h1 className="text-3xl font-bold text-primaryHeading">
              {t("VendorPlatform")}
            </h1>
          </div>
          <Formik
            initialValues={defaultValues}
            validationSchema={loginValidation}
            onSubmit={handleSubmit}
            validateOnMount={true}
          >
            {({ isValid, dirty }) => (
              <LoginForm valid={isValid} dirty={dirty} />
            )}
          </Formik>
        </div>
        <div
          className="h-full bg-cover bg-center bg-no-repeat rounded-s-3xl hidden md:block"
          style={{ backgroundImage: "url('/images/mechanic.png')" }}
        />
      </section>
    </IconContext.Provider>
  );
}

const LoginTopBarImage = () => {
  return (
    <Image
      alt="logo"
      width="100"
      height="100"
      priority
      src="/images/logo.png"
    />
  );
};
