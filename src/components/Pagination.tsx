'use client';

import Link from 'next/link';
import { usePathname, useSearchParams } from 'next/navigation';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';

import { cn } from '@/utils/styling';

const PAGES_COUNT = 5;

interface PaginationProps {
  currentPage: number;
  hasMorePages: boolean;
  pagesCount: number;
}

export function Pagination(props: PaginationProps) {
  const { currentPage = 1, hasMorePages = true, pagesCount } = props;

  const hasPrevious = currentPage > 1;
  const numbers = getPageNumbers(currentPage, hasMorePages, pagesCount);

  const pathname = usePathname();
  const searchParams = useSearchParams();

  function getPaginatedHref(page: number) {
    const params = new URLSearchParams(searchParams);

    if (page > 1) {
      params.set('page', page.toString());
    } else if (params.has('page')) {
      params.delete('page');
    }

    return `${pathname}?${params.toString()}`;
  }

  if (!hasMorePages && currentPage === 1) {
    return null;
  }

  return (
    <div className="flex justify-center lg:justify-end">
      <nav className="flex justify-center -space-x-px rounded-md ">
        <Link
          aria-disabled={!hasPrevious}
          href={getPaginatedHref(currentPage - 1)}
          className={cn(
            'relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-100 hover:text-gray-600 focus:z-20 focus:outline-offset-0',
            'aria-disabled:pointer-events-none aria-disabled:opacity-30'
          )}
        >
          <span className="sr-only">Anterior</span>
          <FaChevronLeft className="h-3 w-3 text-gray-600 mx-1" aria-hidden="true" />
        </Link>
        {currentPage > (PAGES_COUNT - 1) / 2 + 1 && (
          <>
            <Link
              href={getPaginatedHref(1)}
              className="relative hidden items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-100 hover:text-gray-600 focus:z-20 focus:outline-offset-0 sm:inline-flex"
            >
              1
            </Link>
            <div className="relative hidden items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 focus:z-20 focus:outline-offset-0 sm:inline-flex">
              ...
            </div>
          </>
        )}
        {numbers.map((number) => (
          <Link
            key={`page_${number}`}
            href={getPaginatedHref(number)}
            aria-current={currentPage === number ? 'page' : undefined}
            className={cn(
              'relative inline-flex w-12 items-center justify-center px-4 py-2 text-sm font-semibold tabular-nums text-gray-900 ring-1 ring-inset ring-gray-300 focus:z-20 focus:outline-offset-0',
              currentPage === number ? 'bg-gray-100 text-[#5800F7]' : 'hover:bg-gray-100 hover:text-gray-600'
            )}
          >
            {number}
          </Link>
        ))}
        <Link
          aria-disabled={!hasMorePages}
          href={getPaginatedHref(currentPage + 1)}
          className={cn(
            'relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-100 hover:text-gray-600 focus:z-20 focus:outline-offset-0',
            'aria-disabled:pointer-events-none aria-disabled:opacity-30'
          )}
        >
          <span className="sr-only">Siguiente</span>
          <FaChevronRight className="h-3 w-3 text-gray-600 mx-1" aria-hidden="true" />
        </Link>
      </nav>
    </div>
  );
}

function getPageNumbers(currentPage: number, hasMorePages: boolean, pagesCount: number) {
  const numbers: number[] = [];
  const hasPrevious = currentPage > 1;

  if (hasPrevious) {
    numbers.push(currentPage - 1);
  }

  numbers.push(currentPage);

  if (hasMorePages) {
    numbers.push(currentPage + 1);
  }

  if (pagesCount && pagesCount > PAGES_COUNT) {
    const lastPage = pagesCount;
    const firstPage = 1;

    if (currentPage > (PAGES_COUNT - 1) / 2 + 1) {
      numbers.unshift(firstPage);
      numbers.unshift(firstPage + 1);
    }

    if (currentPage < lastPage - (PAGES_COUNT - 1) / 2 - 1) {
      numbers.push(lastPage - 1);
      numbers.push(lastPage);
    }
  }

  return numbers;
}
