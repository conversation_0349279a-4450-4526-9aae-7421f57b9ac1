'use client';
import { useSearchParams, usePathname } from 'next/navigation';
import Link from 'next/link';
import { useEffect } from 'react';

type PaginationProps = {
  totalRecords: number;
  limit?: number;
};

// const Pagination: React.FC<PaginationProps> = ({ totalRecords, limit = 10 }) => {
export default function PaginationV2({ totalRecords, limit = 10 }: PaginationProps) {
  const searchParams = useSearchParams();
  const pathname = usePathname();

  const currentPage = parseInt(searchParams.get('page') || '1', 10);
  const totalPages = Math.ceil(totalRecords / limit);

  const generatePageNumbers = () => {
    const pages: (number | string)[] = [];

    const maxVisiblePages = 5;
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (startPage > 1) {
      pages.push(1);
      if (startPage > 2) pages.push('...');
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) pages.push('...');
      pages.push(totalPages);
    }

    return pages;
  };

  const pageNumbers = generatePageNumbers();

  const createQueryString = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', page.toString());
    return `${pathname}?${params.toString()}`;
  };

  useEffect(() => {
    // if page is 1, remove it from the query string

    if (currentPage === 1) {
      const params = new URLSearchParams(searchParams.toString());
      params.delete('page');
      window.history.replaceState({}, '', `${pathname}?${params.toString()}`);
    }

  }, [currentPage]);

  return (
    <div className="flex items-center space-x-2">
      <Link
        href={createQueryString(1)}
        className={`px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded ${currentPage === 1 ? 'pointer-events-none opacity-50' : ''}`}
      >
        «
      </Link>
      <Link
        href={createQueryString(Math.max(1, currentPage - 1))}
        className={`px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded ${currentPage === 1 ? 'pointer-events-none opacity-50' : ''}`}
      >
        ‹
      </Link>

      {pageNumbers.map((page, index) => (
        typeof page === 'number' ? (
          <Link
            key={index}
            href={createQueryString(page)}
            className={`px-3 py-1 rounded ${page === currentPage
              ? 'bg-blue-500 text-white'
              : 'bg-gray-200 hover:bg-gray-300'
              }`}
          >
            {page}
          </Link>
        ) : (
          <span key={index} className="px-3 py-1 text-gray-500">{page}</span>
        )
      ))}

      <Link
        href={createQueryString(Math.min(totalPages, currentPage + 1))}
        className={`px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded ${currentPage === totalPages ? 'pointer-events-none opacity-50' : ''}`}
      >
        ›
      </Link>
      <Link
        href={createQueryString(totalPages)}
        className={`px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded ${currentPage === totalPages ? 'pointer-events-none opacity-50' : ''}`}
      >
        »
      </Link>

      <p className="ml-4 text-sm text-gray-600">
        Mostrando página {currentPage} de {totalPages} ({totalRecords} resultados)
      </p>
    </div>
  );
};

