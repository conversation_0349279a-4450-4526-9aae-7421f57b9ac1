'use client';

import { cn } from '@/lib/utils';

interface PrimaryButtonProps {
  children: React.ReactNode;
  type?: 'button' | 'submit';
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
}

export default function PrimaryButton({
  children,
  type = 'button',
  onClick,
  disabled,
  className,
}: PrimaryButtonProps) {
  const color = disabled ? 'bg-gray-400' : 'bg-primaryBtn';
  return (
    <button
      className={cn(
        'w-[max-content] h-[40px] rounded px-3 py-2 text-white flex items-center',
        color,
        className
      )}
      type={type || undefined}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
}
