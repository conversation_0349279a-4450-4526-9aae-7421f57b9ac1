'use client';
import { useCurrentUser } from '@/Providers/CurrentUserProvider';
import VehicleSVGV2 from '@/svgsComponents/VehicleSVG_V2';
// import Image from 'next/image';
import { allStatus, svgColors } from '@/constants';

interface CardProps {
  model: string;
  brand: string;
  contract: string;
  newCar: boolean;
  status: string;
  step: {
    stepName: string;
    stepNumber: number;
  };
  color: string;
  extensionCarNumber: string | undefined;
  dischargedReason?: string;
  isBlocked: boolean;
  index?: number;
  total?: number;
  // vehiclePhoto: string;
}

export const colorAndTextOnStatus: {
  [key: string]: { color: string; text: string; borderColor: string; bgColor: string };
} = {
  'in-service': {
    color: 'text-yellow-500',
    text: 'En taller',
    borderColor: 'border-yellow-500',
    bgColor: 'bg-yellow-500',
  },
  'awaiting-insurance': {
    color: 'text-[#115e59]',
    text: 'Seguro',
    bgColor: 'bg-[#115e59]',
    borderColor: 'border-[#115e59]',
  },
  'legal-process': {
    color: 'gray',
    text: 'Legal',
    borderColor: 'border-gray-500',
    bgColor: 'bg-gray-500',
  },
};

export const availableStatus = Object.keys(colorAndTextOnStatus);
const Card = ({
  model,
  brand,
  contract,
  newCar,
  step,
  color,
  extensionCarNumber,
  status,
  isBlocked,
  dischargedReason,
  index,
  total,
}: CardProps) => {
  const { user } = useCurrentUser();
  const role = user?.role;

  return (
    <div
      className={`
        relative flex
        items-center
        py-2 px-4
        bg-white border-2 rounded-md w-80 max-w-xm h-28
        ${isBlocked ? 'bg-red-200 border-red-300' : 'border-validationLight'}
        overflow-hidden
        ${total && index && index === total - 1 ? 'mb-4' : 'mb-0'}
      `}
      data-cy="stock-card"
    >
      <div className="flex  gap-x-2.5  w-full justify-aroundml-3.5 overflow-hidden">
        <div
          className={`flex items-center min-w-[100px] min-h-[88px]
          `}
          // ${
          //   color.toUpperCase() === 'BLANCO'
          //     ? `
          //     bg-[#3c3c3c]
          //     rounded-md shadow-lg
          //     shadow-[#3c3c3c]
          //   `
          //     : 'bg-[transparent]'
          // }
        >
          {/* <Image
            src="/images/vehicleImg/car_placeholder_lg.svg"
            alt="carIMG"
            className="h-full w-[100px] min-h-[88px] flex items-center mt-1 "
            width={1000}
            priority
            height={1000}
          /> */}
          <VehicleSVGV2 color={svgColors[color.toUpperCase()] || 'white'} />
        </div>
        <div className="flex flex-col justify-center gap-y-2.5 ml-2">
          <h3 className="text-sm font-semibold text-primaryPurple font-inter">
            #{contract}
            {extensionCarNumber ? ` - ${extensionCarNumber}` : ''}
          </h3>
          <p className="text-base font-semibold font-inter whitespace-nowrap">
            {brand} {model}
          </p>
          {status !== allStatus.discharged && status !== allStatus.overhauling && (
            <p
              className={`text-xs font-medium font-inter ${
                isBlocked ? 'text-red-600' : 'text-validationGreen'
              }`}
            >
              {isBlocked ? 'Bloqueado' : step.stepName}
            </p>
          )}
          {status === 'discharged' && <p className="text-xs font-medium text-red-600">{dischargedReason}</p>}
          {status === allStatus.overhauling && (
            <p className={`text-xs font-medium font-inter text-validationGreen`}>Revisión</p>
          )}
        </div>
      </div>
      {status !== 'discharged' ? (
        newCar ? (
          <div className="absolute top-2.5 right-0 flex items-center justify-center border-y-2 border-l-2 border-primaryPurple rounded-l-lg w-12 h-1/4">
            <p className="text-xs font-normal text-primaryPurple font-inter">Nuevo</p>
          </div>
        ) : (
          <div className="absolute top-2.5 right-0 flex items-center justify-center border-y-2 border-l-2 border-otherAqua rounded-l-lg w-20 h-1/5">
            <p className="text-xs font-normal text-otherAqua font-inter">Semi-nuevo</p>
          </div>
        )
      ) : null}
      {role !== 'auditor' && availableStatus.includes(status) && (
        <div
          className={`absolute bottom-2.5 right-0 flex items-center justify-end border-y-2 border-l-2
                     ${colorAndTextOnStatus[status].borderColor} rounded-l-lg w-12 h-1/4`}
        >
          <p className={`text-xs font-normal ${colorAndTextOnStatus[status].color} font-inter mb-[2px]`}>
            {colorAndTextOnStatus[status].text}
          </p>
        </div>
      )}
      {/* {status === 'in-service' && (
        <div className="absolute bottom-2.5 right-0 flex items-center justify-end border-y-2 border-l-2 border-yellow-500 rounded-l-lg w-12 h-1/4">
          <p className="text-xs font-normal text-yellow-500 font-inter">En taller</p>
        </div>
      )}
      {status === 'legal-process' && (
        <div className="absolute bottom-2.5 right-0 flex items-center justify-end border-y-2 border-l-2 border-gray-500 rounded-l-lg w-12 h-1/4">
          <p className="text-xs font-normal text-gray-500 font-inter">Proceso legal</p>
        </div>
      )}
      {status === 'awaiting-insurance' && (
        <div className="absolute bottom-2.5 right-0 flex items-center justify-end border-y-2 border-l-2 border-blue-500 rounded-l-lg w-12 h-1/4">
          <p className="text-xs font-normal text-blue-500 font-inter">En taller</p>
        </div>
      )} */}
    </div>
  );
};

export default Card;
