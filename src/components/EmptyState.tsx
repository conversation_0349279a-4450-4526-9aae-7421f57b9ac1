'use client';

import { Box, Center, Stack, Text } from '@chakra-ui/react';

interface EmptyStateProps {
  title: string;
  description?: string;
  minH?: number;
  action?: React.ReactNode;
}

export default function EmptyState(props: EmptyStateProps) {
  const { title, description, minH, action } = props;
  return (
    <Center h="full" minH={minH ? minH : 30}>
      <Box textAlign="center">
        <Stack spacing={1}>
          <Text fontSize="lg" fontWeight="medium" color="gray.600">
            {title}
          </Text>
          {description && (
            <Text fontSize="sm" color="gray.500">
              {description}
            </Text>
          )}
          {action && (
            <Stack mt="5" direction="row" justifyContent="center">
              {action}
            </Stack>
          )}
        </Stack>
      </Box>
    </Center>
  );
}
