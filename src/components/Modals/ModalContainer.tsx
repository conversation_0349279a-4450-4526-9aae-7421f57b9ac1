'use client';
import { ReactNode, useEffect } from 'react';
import { IoClose } from 'react-icons/io5';

export default function ModalContainer({
  children,
  title,
  onClose,
  className,
  classAnimation,
  removeScrollBar = true,
  width,
}: {
  children: ReactNode;
  title: string;
  onClose: () => void;
  className?: string;
  classAnimation?: string;
  removeScrollBar?: boolean;
  width?: string;
}) {
  const position = (window.scrollY + 70).toString() + 'px';
  useEffect(() => {
    if (removeScrollBar) {
      document.body.classList.add('overflow-hidden');
    }
    return () => {
      document.body.classList.remove('overflow-hidden');
    };
  }, [removeScrollBar]);
  return (
    <div
      id="modal-container"
      className={`
            w-full h-[100%]
            absolute top-0 left-0 z-[20] 
            bg-black 
            pt-[70px]
            bg-opacity-50 
            flex 
            justify-center
            transition-opacity
            duration-500
            ease-in-out
          `}
    >
      <div
        className={`
          ${width ? width : 'w-[540px]'}
          rounded-[10px] 
          absolute
          h-[max-content] min-h-[200px] 
          bg-white 
          p-[20px]
          flex flex-col 
          gap-[30px]
          ${className ? className : ''}
          animate__animated  animate__faster
          ${classAnimation ? classAnimation : 'animate__fadeInDown'}
        `}
        style={{ top: `${position}` }}
      >
        <header className="flex gap-3 justify-between items-center">
          <p className="text-[18px] font-[600] text-modalText  ">{title}</p>
          <IoClose className="cursor-pointer" size={24} onClick={onClose} />
        </header>
        {children}
      </div>
    </div>
  );
}
