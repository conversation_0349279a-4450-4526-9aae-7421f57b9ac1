import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>lose<PERSON><PERSON>on,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  ModalOverlay,
  ResponsiveValue,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { Form as FormikForm, Formik, FormikValues, FormikHelpers } from 'formik';
import { AiOutlinePlus } from 'react-icons/ai';
import { IoOptionsOutline } from 'react-icons/io5';
import * as Yup from 'yup';
import { CiEdit } from 'react-icons/ci';
import React from 'react';
import { SlOptions } from 'react-icons/sl';
import { useCurrentUser } from '@/Providers/CurrentUserProvider';

export type OnSubmitCustomModal<T> = (
  values: T,
  formikHelpers: FormikHelpers<T>,
  onClose: () => void
) => Promise<any>;

interface CustomModalProps<T extends FormikValues> {
  header: string;
  testId?: string;
  openButtonText: string;
  confirmButtonText: string;
  validatorSchema?: Yup.ObjectSchema<any>;
  initialValues: T;
  onSubmit: OnSubmitCustomModal<T>;
  customSubmit?: boolean;
  onCloseModal: (onClose?: () => void) => any;
  icon?: string;
  body: React.ReactElement;
  footer?: React.ReactElement;
  isUpdate?: boolean;
  isPrimaryButton?: boolean;
  updateIconColor?: string;
  reloadWindow?: boolean;
  size?:
    | ResponsiveValue<
        (string & {}) | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'xs' | '3xl' | '4xl' | '5xl' | '6xl' | 'full'
      >
    | undefined;
  plusButton?: boolean;
  isEditUser?: boolean;
  cancelButtonText?: string;
}

interface WithDropDownBtn {
  dropDownBtn?: true;
  dropDownOptions: {
    label: string;
    onClick: () => void;
  }[];
}

interface WithoutDropDownBtn {
  dropDownBtn?: false;
  dropDownOptions?: {
    label: string;
    onClick: () => void;
  }[];
}

type ExtendedProps<T extends FormikValues> = CustomModalProps<T> & (WithDropDownBtn | WithoutDropDownBtn);

export default function CustomModal<T extends FormikValues>({
  header,
  openButtonText,
  confirmButtonText,
  validatorSchema,
  initialValues,
  onSubmit,
  onCloseModal,
  body,
  isUpdate,
  isPrimaryButton,
  size,
  updateIconColor,
  plusButton = true,
  isEditUser,
  testId,
  footer,
  dropDownOptions,
  dropDownBtn,
  customSubmit,
  reloadWindow = true,
  cancelButtonText = 'Cancelar',
}: ExtendedProps<T>) {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();

  const { user } = useCurrentUser();

  if (user.role === 'auditor') return null;

  return (
    <>
      {isUpdate ? (
        dropDownBtn ? (
          <Menu>
            {({ isOpen: open }) => (
              <>
                <MenuButton data-cy={testId} isActive={open} as={Button}>
                  {/* {isOpen ? 'Close' : 'Open'} */}
                  <SlOptions color="#5800F7" />
                </MenuButton>
                <MenuList>
                  <MenuItem onClick={onOpen}>Editar</MenuItem>
                  {
                    dropDownOptions?.map(
                      (op, i) =>
                        op.label && (
                          <MenuItem key={i} onClick={op.onClick}>
                            {op.label}
                          </MenuItem>
                        )
                    ) // TODO: Add key
                  }
                </MenuList>
              </>
            )}
          </Menu>
        ) : (
          <IconButton
            icon={<CiEdit size={30} strokeWidth="1px" color={updateIconColor} />}
            p={0}
            data-cy={testId}
            bgColor="transparent"
            aria-label="ciedit"
            onClick={onOpen}
          />
        )
      ) : (
        <div className="relative">
          {isPrimaryButton ? (
            <>
              <button
                className={` whitespace-nowrap
                  ${isEditUser ? 'border-[2px] border-[#5800F7]' : 'bg-[#5800F7]'}
                   text-start 
                  ${isEditUser ? 'text-[#5800F7] font-bold' : 'text-white'}
                  ${plusButton ? 'pl-8' : 'pl-3'}  
                  pr-3 h-[40px] rounded w-auto
                `}
                onClick={onOpen}
                data-cy={testId}
              >
                {openButtonText}
              </button>
              {plusButton && (
                <div
                  className="absolute top-0 left-[8px] text-white flex items-center h-full cursor-pointer whitespace-nowrap"
                  onClick={onOpen}
                >
                  <AiOutlinePlus size={22} />
                </div>
              )}
            </>
          ) : (
            <div className="border border-[#5800F7] rounded" data-cy={testId} onClick={onOpen}>
              <div className="absolute top-0 right-[10px]  flex items-center h-full cursor-pointer">
                <IoOptionsOutline size={22} color="#5800F7" />
              </div>
              <button className="text-[#5800F7] px-4 text-start h-[38px] w-[105px]">{openButtonText}</button>
            </div>
          )}
        </div>
      )}

      <Modal closeOnOverlayClick={false} isOpen={isOpen} size={size || 'lg'} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{header}</ModalHeader>
          <ModalCloseButton onClick={() => onCloseModal()} />
          {/* <Box px={6} pb={6}> */}
          <Formik
            initialValues={initialValues}
            onSubmit={async (values, formikHelpers) => {
              if (customSubmit) {
                await onSubmit(values, formikHelpers, onClose);
              } else {
                try {
                  const res = await onSubmit(values, formikHelpers, onClose);
                  onClose();
                  toast({
                    title: res.data.message,
                    description: 'Actualizando pagina...',
                    duration: 3000,
                    status: 'success',
                    position: 'top',
                  });
                  onCloseModal();
                  if (reloadWindow) {
                    setTimeout(() => {
                      window.location.reload();
                    }, 3050);
                  }
                } catch (error: any) {
                  onClose();
                  onCloseModal();
                  toast({
                    title: error.response.data.message,
                    duration: 6000,
                    status: 'error',
                    position: 'top',
                  });
                }
              }
            }}
            validationSchema={validatorSchema}
            validateOnMount={true}
          >
            {({ isValid, dirty, isSubmitting }) => {
              const validate = dirty && isValid;
              return (
                <FormikForm>
                  <ModalBody pb={6}>{body}</ModalBody>
                  <ModalFooter gap={3}>
                    {footer ? (
                      <>{footer}</>
                    ) : (
                      <>
                        <Button
                          sx={{
                            color: '#5800F7',
                            borderColor: '#5800F7 !important',
                            border: '2px',
                            h: '40px',
                          }}
                          onClick={() => {
                            onCloseModal();
                            onClose();
                          }}
                        >
                          {cancelButtonText}
                        </Button>
                        <Button
                          sx={{
                            // bg: '#5800F7 !important',
                            color: 'white',
                            h: '40px',
                          }}
                          className={`
                      ${!validate ? 'bg-[#9CA3AF]' : 'bg-[#5800F7]'}
                      text-white rounded-md  h-[40px] cursor-pointer`}
                          type="submit"
                          data-cy="post"
                          disabled={!validate || isSubmitting}
                        >
                          {confirmButtonText}
                        </Button>
                      </>
                    )}
                  </ModalFooter>
                </FormikForm>
              );
            }}
          </Formik>
        </ModalContent>
      </Modal>
    </>
  );
}
