// components/SignatureInput.tsx

'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Control, useController, FieldValues, Path } from 'react-hook-form';
import { FormField, FormItem, FormLabel } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Undo2, Redo2 } from 'lucide-react';

interface SignatureInputProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
  label: string;
  // Dimensiones fijas del canvas
  width?: number;
  height?: number;
}

export default function SignatureInput<T extends Record<string, any>>({
  control,
  name,
  label,
  width = 230,
  height = 250,
}: SignatureInputProps<T>) {

  const {
    field: { value, onChange },
    fieldState: { error },
  } = useController({ name, control });

  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const ctxRef = useRef<CanvasRenderingContext2D | null>(null);

  // Refs para gestionar el estado interno sin causar re-render
  const isDrawingRef = useRef<boolean>(false);
  const pathsRef = useRef<Array<Array<{ x: number; y: number }>>>([]);
  const undoStackRef = useRef<Array<Array<{ x: number; y: number }>>>([]);
  const redoStackRef = useRef<Array<Array<{ x: number; y: number }>>>([]);

  // Estado para controlar la habilitación de botones
  const [canUndo, setCanUndo] = useState<boolean>(false);
  const [canRedo, setCanRedo] = useState<boolean>(false);

  // Inicializar el canvas y el contexto una sola vez
  useEffect(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      const scale = window.devicePixelRatio || 1;

      // Establecer dimensiones físicas del canvas
      canvas.width = width * scale;
      canvas.height = height * scale;

      // Establecer dimensiones CSS del canvas
      canvas.style.width = `${width}px`;
      canvas.style.height = `${height}px`;

      const context = canvas.getContext('2d');
      if (context) {
        // Resetear cualquier transformación previa
        context.setTransform(1, 0, 0, 1, 0, 0);
        // Escalar el contexto para alta resolución
        context.scale(scale, scale);
        // Configurar estilos de trazo
        context.lineWidth = 2;
        context.strokeStyle = '#000000';
        context.lineCap = 'round';
        context.lineJoin = 'round';
        ctxRef.current = context;

        // Dibujar la firma existente si hay alguna
        if (value) {
          const img = new Image();
          img.src = value;
          img.onload = () => {
            context.clearRect(0, 0, width, height);
            context.drawImage(img, 0, 0, width, height);
          };
        } else {
          // Limpiar el canvas si no hay firma
          context.clearRect(0, 0, width, height);
        }
      }
    }
  }, [width, height]); // Ejecutar solo una vez al montar

  // Cargar y dibujar la firma existente cuando el valor cambia
  useEffect(() => {
    if (!ctxRef.current || !canvasRef.current) return;

    const context = ctxRef.current;
    const canvas = canvasRef.current;

    if (value) {
      const img = new Image();
      img.src = value;
      img.onload = () => {
        context.clearRect(0, 0, width, height);
        context.drawImage(img, 0, 0, width, height);
      };
    } else {
      // Limpiar el canvas si no hay firma
      context.clearRect(0, 0, width, height);
    }
  }, [value, width, height]);

  // Función para obtener las coordenadas relativas al canvas
  const getCoordinates = (e: React.MouseEvent | React.TouchEvent) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    let x = 0;
    let y = 0;

    if ('touches' in e && e.touches.length > 0) {
      x = e.touches[0].clientX - rect.left;
      y = e.touches[0].clientY - rect.top;
    } else if ('clientX' in e) {
      x = e.clientX - rect.left;
      y = e.clientY - rect.top;
    }

    return { x, y };
  };

  // Iniciar el dibujo
  const startDrawing = (e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault();
    if (!ctxRef.current) return;

    const { x, y } = getCoordinates(e);
    isDrawingRef.current = true;

    // Iniciar una nueva trayectoria
    pathsRef.current.push([{ x, y }]);
    undoStackRef.current.push([{ x, y }]);
    redoStackRef.current = []; // Limpiar redo stack

    setCanUndo(undoStackRef.current.length > 0);
    setCanRedo(redoStackRef.current.length > 0);

    ctxRef.current.beginPath();
    ctxRef.current.moveTo(x, y);
  };

  // Dibujar en el canvas
  const draw = (e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault();
    if (!isDrawingRef.current || !ctxRef.current) return;

    const { x, y } = getCoordinates(e);
    ctxRef.current.lineTo(x, y);
    ctxRef.current.stroke();

    // Agregar el punto a la trayectoria actual
    const currentPath = pathsRef.current[pathsRef.current.length - 1];
    currentPath.push({ x, y });
  };

  // Finalizar el dibujo
  const stopDrawing = () => {
    if (!isDrawingRef.current || !ctxRef.current) return;

    ctxRef.current.closePath();
    isDrawingRef.current = false;

    // Actualizar el valor en React Hook Form
    const canvas = canvasRef.current;
    if (canvas) {
      const signatureData = canvas.toDataURL('image/png');
      onChange(signatureData);
    }
  };

  // Redibujar todas las trayectorias
  const redrawCanvas = () => {
    if (!ctxRef.current) return;
    const context = ctxRef.current;

    // Limpiar el canvas
    context.clearRect(0, 0, width, height);

    // Dibujar cada trayectoria
    pathsRef.current.forEach((path) => {
      if (path.length > 0) {
        context.beginPath();
        context.moveTo(path[0].x, path[0].y);
        path.forEach((point) => {
          context.lineTo(point.x, point.y);
        });
        context.stroke();
        context.closePath();
      }
    });
  };

  // Función de Undo
  const undo = () => {
    if (undoStackRef.current.length === 0) return;

    const lastPath = undoStackRef.current.pop();
    if (lastPath) {
      redoStackRef.current.push(lastPath);
      pathsRef.current.pop(); // Eliminar la última trayectoria
      redrawCanvas();

      setCanUndo(undoStackRef.current.length > 0);
      setCanRedo(redoStackRef.current.length > 0);

      // Actualizar el valor en React Hook Form
      const canvas = canvasRef.current;
      if (canvas) {
        const signatureData = canvas.toDataURL('image/png');
        onChange(signatureData);
      }
    }
  };

  // Función de Redo
  const redo = () => {
    if (redoStackRef.current.length === 0 || !ctxRef.current) return;

    const pathToRedo = redoStackRef.current.pop();
    if (pathToRedo) {
      undoStackRef.current.push(pathToRedo);
      pathsRef.current.push(pathToRedo);
      redrawCanvas();

      setCanUndo(undoStackRef.current.length > 0);
      setCanRedo(redoStackRef.current.length > 0);

      // Actualizar el valor en React Hook Form
      const canvas = canvasRef.current;
      if (canvas) {
        const signatureData = canvas.toDataURL('image/png');
        onChange(signatureData);
      }
    }
  };

  // Función para limpiar el canvas
  const clearSignature = () => {
    if (!ctxRef.current) return;

    // Limpiar el canvas
    ctxRef.current.clearRect(0, 0, width, height);

    // Resetear las trayectorias y pilas
    pathsRef.current = [];
    undoStackRef.current = [];
    redoStackRef.current = [];

    setCanUndo(false);
    setCanRedo(false);

    // Actualizar el valor en React Hook Form
    onChange('');
  };

  return (
    <FormField
      name={name}
      control={control}
      render={() => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <div
            className="border rounded-lg p-2 bg-white"
            style={{
              width: `${width}px`, // Ancho fijo
              height: `${height}px`, // Alto fijo
              boxSizing: 'border-box', // Incluir padding y border en el ancho total
              overflow: 'hidden', // Prevenir desbordamiento interno
            }}
          >
            <canvas
              ref={canvasRef}
              className="w-full h-full touch-none"
              style={{ display: 'block', cursor: 'crosshair' }}
              onMouseDown={startDrawing}
              onMouseMove={draw}
              onMouseUp={stopDrawing}
              onMouseLeave={stopDrawing}
              onTouchStart={startDrawing}
              onTouchMove={draw}
              onTouchEnd={stopDrawing}
            />
          </div>
          <div className="flex gap-2 mt-2">
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={undo}
              disabled={!canUndo}
              aria-label="Deshacer"
            >
              <Undo2 className="h-4 w-4" />
            </Button>
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={redo}
              disabled={!canRedo}
              aria-label="Rehacer"
            >
              <Redo2 className="h-4 w-4" />
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={clearSignature}
              aria-label="Limpiar Firma"
            >
              Limpiar Firma
            </Button>
          </div>
          {error && <p className="text-red-500 text-sm mt-1">{error.message}</p>}
        </FormItem>
      )}
    />
  );
}

