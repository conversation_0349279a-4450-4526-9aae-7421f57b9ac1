import React, { useRef, useState } from "react";
import { Control, useController } from "react-hook-form";
import { FormField, FormItem, FormLabel, FormControl } from "@/components/ui/form";
import { Button } from "@/components/ui/button";

import { Path } from "react-hook-form";
import { FieldValues } from "react-hook-form";

interface FileInputProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
  label: string;
  accept?: "pdf" | "png" | "jpg" | "jpeg" | "gif" | "webp" | "all-images";
}

export default function FileInput<T extends Record<string, any>>({
  control,
  name,
  label,
  accept = "all-images",
}: FileInputProps<T>) {
  const { field } = useController({ name, control });
  // const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>(field.value || []);
  // const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const [previewUrls, setPreviewUrls] = useState<string[]>(() => {
    return uploadedFiles.map((file) => URL.createObjectURL(file));
  });

  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const handleFileChange = (files: FileList | null) => {
    if (!files) return;

    const acceptedFormats = {
      "all-images": ["image/png", "image/jpg", "image/jpeg", "image/gif", "image/webp"],
      pdf: ["application/pdf"],
      png: ["image/png"],
      jpg: ["image/jpg", "image/jpeg"],
      jpeg: ["image/jpeg"],
      gif: ["image/gif"],
      webp: ["image/webp"],
    };

    const allowedFormats = acceptedFormats[accept] || [];
    const validFiles: File[] = Array.from(files).filter((file) =>
      allowedFormats.includes(file.type)
    );

    // Agregar nuevas imágenes a las existentes
    const newUrls = validFiles.map((file) => URL.createObjectURL(file));
    setPreviewUrls((prev) => [...prev, ...newUrls]);
    setUploadedFiles((prev) => [...prev, ...validFiles]);
    field.onChange([...uploadedFiles, ...validFiles]); // Actualizar el estado en react-hook-form

    // set FileList to the field

  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    handleFileChange(e.dataTransfer.files);
  };

  const handleRemoveImage = (index: number) => {
    const updatedUrls = [...previewUrls];
    const updatedFiles = [...uploadedFiles];

    // Eliminar la URL e imagen en la posición especificada
    updatedUrls.splice(index, 1);
    updatedFiles.splice(index, 1);

    setPreviewUrls(updatedUrls);
    setUploadedFiles(updatedFiles);
    field.onChange(updatedFiles); // Actualizar el estado en react-hook-form
  };

  return (
    <FormField
      name={name}
      control={control}

      render={() => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <>
            <div
              className={`relative border-2 ${isDragging ? "border-dashed border-blue-500" : "border-gray-300"
                } rounded-md p-4 text-center flex flex-col items-center justify-center gap-2`}
              onDragOver={(e) => {
                e.preventDefault();
                setIsDragging(true);
              }}
              onDragLeave={() => setIsDragging(false)}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <p className="text-gray-500">
                Arrastra los archivos aquí o haz clic para seleccionarlos
              </p>
              <Button type='button' variant="outline">Seleccionar Archivos</Button>
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept={
                  accept === "all-images"
                    ? "image/png, image/jpg, image/jpeg, image/gif, image/webp"
                    : accept
                }
                className="hidden"
                onChange={(e) => handleFileChange(e.target.files)}
              />
            </div>
            <div className="flex gap-2 mt-4 flex-wrap">
              {previewUrls.map((url, index) => (
                <div
                  key={index}
                  className="relative w-24 h-24 bg-gray-100 rounded  border"
                >
                  <button
                    className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center bg-red-500 text-white rounded-xl p-2 text-xs"
                    onClick={() => handleRemoveImage(index)}
                    type='button'
                  >
                    ✕
                  </button>
                  <img
                    src={url}
                    alt={`preview-${index}`}
                    className="w-full h-full object-cover rounded-md"
                  />
                </div>
              ))}
            </div>
          </>
        </FormItem>
      )}

    />
  );
}
