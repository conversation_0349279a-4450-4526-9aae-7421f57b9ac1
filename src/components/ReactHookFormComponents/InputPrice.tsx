import React from "react";
import { Control, useController } from "react-hook-form";
import { FormField, FormItem, FormLabel, FormControl } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Path, FieldValues } from "react-hook-form";

interface InputPriceProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
  label: string;
  placeholder?: string;
}

export default function InputPrice<T extends Record<string, any>>({
  control,
  name,
  label,
  placeholder = "0.00",
}: InputPriceProps<T>) {
  const { field } = useController({ name, control });

  // Maneja los cambios para permitir solo números y el punto decimal
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    // Permite solo números y un punto decimal
    const validValue = value.replace(/[^0-9.]/g, "");

    // Evita que el usuario ingrese más de un punto decimal
    if ((validValue.match(/\./g) || []).length > 1) return;

    field.onChange(+validValue);
  };

  return (
    <FormField
      control={control}
      name={name}
      render={() => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <div className="relative">
              {/* Icono de precio */}
              <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                $
              </span>
              {/* Input */}
              <Input
                {...field}
                value={field.value || ""}
                onChange={handleChange}
                placeholder={placeholder}
                className="pl-8"
                type="number"
                onKeyDown={(e) => {
                  if (e.key === "e" || e.key === "+" || e.key === "-") {
                    e.preventDefault(); // Evita "e", "+", "-"
                  }
                }}
              />
            </div>
          </FormControl>
        </FormItem>
      )}
    />
  );
}
