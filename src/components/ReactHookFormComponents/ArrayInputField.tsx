import React, { useState } from "react";
import { Control, useController } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { FormField, FormItem, FormLabel } from "@/components/ui/form";
import { FieldValues, Path } from "react-hook-form";

interface ArrayInputFieldProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
  label: string;
  placeholder?: string;
}

export default function ArrayInputField<T extends Record<string, any>>({
  control,
  name,
  label,
  placeholder = "Agregar un valor",
}: ArrayInputFieldProps<T>) {
  const { field } = useController({ name, control });
  // console.log('field', field.name, field.value)
  const [inputValue, setInputValue] = useState("");
  const handleAddItem = () => {
    if (inputValue.trim() !== "") {
      // const updatedArray = [...field.value, inputValue.trim()];
      // console.log('updatedArray', updatedArray)
      let updatedArray: string[] = [];
      if (field.value) {
        updatedArray = [...field.value, inputValue.trim()];
      } else {
        updatedArray = [inputValue.trim()];
      }

      field.onChange(updatedArray); // Actualiza el array en el formulario
      setInputValue(""); // Limpia el input
    }
  };

  const handleRemoveItem = (index: number) => {
    const updatedArray = [...field.value];
    updatedArray.splice(index, 1);
    field.onChange(updatedArray); // Actualiza el array en el formulario
  };

  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      event.preventDefault();
      handleAddItem();
    }
  };

  return (
    <FormField
      name={name}
      control={control}
      render={() => (
        <FormItem>
          <FormLabel className='flex items-center'>
            {label}
            <span className="text-sm text-gray-500 block pl-2 mt-[1px]">
              (Presiona Enter para agregar)
            </span>
          </FormLabel>
          <div className="flex items-center gap-2">
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder={placeholder}
            />
            <Button variant="outline" onClick={handleAddItem} type='button'>
              Agregar
            </Button>
          </div>
          <div className="mt-4 space-y-2">
            {field.value?.map((item: string, index: number) => (
              <div
                key={index}
                className="flex justify-between items-center bg-gray-100/60 p-2 rounded-md"
              >
                <span>{item}</span>
                <Button
                  variant="destructive"
                  size="sm"
                  type='button'
                  onClick={() => handleRemoveItem(index)}
                >
                  Eliminar
                </Button>
              </div>
            ))}
          </div>
        </FormItem>
      )}
    />
  );
}
