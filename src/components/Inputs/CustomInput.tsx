import { ErrorMessage, Field, useField } from 'formik';
import { ChangeEvent } from 'react';

interface InputProps {
  label: string;
  name: string;
  type: 'text' | 'email' | 'number' | 'textarea';
  disabled?: boolean;
  defaultValue?: string;
  onChange?: (value: string) => Promise<any> | any;
  onKeyDown?: (event: KeyboardEvent) => void;
  allowDecimals?: boolean;
  onBlur?: (event: any) => Promise<any>;
}

export default function CustomInput({
  label,
  name,
  type,
  disabled,
  onChange,
  allowDecimals,
  defaultValue /* , onBlur */,
  onBlur,
}: InputProps) {
  const [field, meta, helpers] = useField(name);
  const hasError = meta.touched && meta.error;

  const { setValue } = helpers;

  const handleChange = async (e: ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;
    // Si hay una función onChange, se aplica
    if (onChange) {
      value = (await onChange(value)) || value;
    }

    // Se actualiza el valor en Formik
    setValue(value);
  };

  return (
    <div className="flex flex-col">
      <label className="block text-gray-700 text-[16px] mb-2" htmlFor={name}>
        {label}
      </label>
      <div className="flex">
        <Field
          onBlur={onBlur}
          as={type === 'textarea' ? 'textarea' : 'input'}
          type={type}
          id={name}
          value={defaultValue ? defaultValue : field.value}
          name={name}
          disabled={disabled}
          /* onBlur={async (e: any) => {
            field.onBlur(e);
            if (onBlur && e.target) {
              await onBlur(e.target.value);
            }
          }} */
          onChange={handleChange}
          onKeyDown={(event: any) => {
            if (type === 'number') {
              if (allowDecimals) {
                if (['e', 'E', '+', '-'].includes(event.key)) return event.preventDefault();
                return null;
              }
              if (['e', 'E', '+', '-', '.'].includes(event.key)) return event.preventDefault();
              return null;
            } else return null;
          }}
          className={`
            border ${hasError ? 'border-red-500' : 'border-[#9CA3AF]'}
            border-2
            text-black
            ${disabled ? 'bg-[#EAECEE]' : ''}
            rounded
            px-3
            w-full
            ${type === 'textarea' ? 'h-[100px] pt-[5px] min-h-[40px]' : 'h-[40px]'}
            max-h-[150px]
            outline-none
            `}
        />
      </div>
      {hasError && <ErrorMessage name={name} component="div" className="mt-1 text-sm text-red-500" />}
    </div>
  );
}
