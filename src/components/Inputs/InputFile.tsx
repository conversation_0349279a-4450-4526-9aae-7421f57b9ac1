import { Input, Button } from '@chakra-ui/react';
import { ErrorMessage, Field, FieldInputProps, FormikValues, useField } from 'formik';
import { useRef, useState } from 'react';
import { IconType } from 'react-icons';
import { GrDocumentPdf } from 'react-icons/gr';
import { ImImages } from 'react-icons/im';

interface FieldProps {
  name: string;
  label: string;
  nameFile: string;
  buttonText: string;
  accept: 'pdf' | 'all-images' | 'jpg' | 'png';
  // the function below has to remove the state or states of the name file
  handleSetName?: (name: string, value: string, size: number) => void;
  handleSingleSetName?: (value: string) => void;
  placeholder: string;
  placeHolderDown?: boolean;
  multiple?: boolean;
  onChange?: (event: FileList | null) => void;
}

const acceptTypes: { [key: string]: string } = {
  'all-images': '.jpg, .jpeg, .png, .webp',
  jpg: 'image/jpg',
  png: 'image/png',
  pdf: 'application/pdf',
};

interface IconData {
  icon: IconType;
  size: number;
}

const icons: { [key: string]: IconData } = {
  pdf: {
    icon: GrDocumentPdf,
    size: 20,
  },
  'all-images': {
    icon: ImImages,
    size: 20,
  },
  png: {
    icon: ImImages,
    size: 20,
  },
  jpg: {
    icon: ImImages,
    size: 20,
  },
};

export default function InputFile({
  name,
  label,
  nameFile,
  accept,
  placeholder,
  placeHolderDown,
  buttonText,
  handleSetName,
  handleSingleSetName,
  multiple = false,
  onChange,
}: FieldProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [, meta] = useField(name);
  const [isDragging, setIsDragging] = useState(false);
  const hasError = meta.touched && meta.error;

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const handleDragEnter = () => {
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleFileDrop = (event: React.DragEvent<HTMLButtonElement>, form: any) => {
    event.preventDefault();
    setIsDragging(false);

    const droppedFiles = event.dataTransfer.files;

    if (!droppedFiles) return;
    if (multiple) {
      if (onChange) onChange(droppedFiles);
      form.setFieldValue(name, droppedFiles);
      return;
    }

    const droppedFile = event.dataTransfer.files[0];
    if (!droppedFile) return;

    const fileName = droppedFile.name;
    const fileSize = droppedFile.size;
    if (handleSetName) handleSetName(name, fileName, fileSize);
    form.setFieldValue(name, droppedFile);
  };

  function IconRenderer() {
    const iconData = icons[accept];
    if (!iconData) return null;

    const { icon: IconComponent, size } = iconData;

    return <IconComponent size={size} className="mr-[6px]" />;
  }

  return (
    <Field name={name} id={name}>
      {({ form }: { field: FieldInputProps<any>; form: FormikValues }) => (
        <div className="relative font-normal ">
          <label htmlFor={name}>{label}</label>
          <div
            className={`flex ${
              placeHolderDown ? 'flex-col gap-1' : 'flex-row items-center gap-3'
            } mt-2 w-full overflow-hidden `}
            style={{ gridTemplateColumns: 'max-content 1fr' }}
          >
            <Button
              h="40px"
              minW="138px"
              w="max-content"
              borderColor={isDragging ? '#9CA3AF !important' : '#5800F7 !important'}
              color="#5800F7 !important"
              fontWeight={600}
              border={isDragging ? '2px dashed' : '2px solid'}
              sx={{
                '&::placeholder': {
                  color: '#5800F7',
                },
                _hover: {
                  borderColor: '#5800F7',
                },
              }}
              cursor="pointer"
              onClick={handleButtonClick}
              onDragEnter={handleDragEnter} // Manejadores de eventos de arrastre
              onDragLeave={handleDragLeave} // Manejadores de eventos de arrastre
              onDragOver={(event) => event.preventDefault()} // Evita comportamiento por defecto
              onDrop={(e) => handleFileDrop(e, form)}
            >
              {buttonText}
            </Button>
            <div className={`text-[14px]`}>
              {Array.isArray(nameFile) && nameFile.length > 0 ? (
                <div>{`file count: ${nameFile.length}`}</div>
              ) : nameFile?.length > 0 ? (
                <div className="flex items-center max-w-screen-sm ">
                  <IconRenderer />
                  <p></p>
                  {nameFile.slice(0, 25)}
                </div>
              ) : (
                <p>{placeholder}</p>
              )}
            </div>
          </div>

          <Input
            h="45px"
            display="none"
            name={name}
            ref={fileInputRef}
            cursor="pointer"
            accept={acceptTypes[accept]}
            type="file"
            multiple={multiple}
            onChange={(event) => {
              if (!multiple) {
                const file = event.currentTarget.files ? event.currentTarget.files[0] : undefined;
                if (!file) return;
                form.setFieldValue(name, file);
                if (handleSetName) handleSetName(name, file.name, file.size);
                if (handleSingleSetName) handleSingleSetName(file.name);
                if (onChange) {
                  onChange(event.currentTarget.files);
                }
              } else {
                const files = event.currentTarget.files;
                const arrayArray = Array.from(files || []);
                if (handleSetName) handleSetName(name, arrayArray as unknown as string, arrayArray[0].size);
                form.setFieldValue(name, arrayArray);
                if (onChange) {
                  onChange(files);
                }
              }
            }}
          />

          {hasError && <ErrorMessage name={name} component="div" className="mt-1 text-sm text-red-500" />}
        </div>
      )}
    </Field>
  );
}
