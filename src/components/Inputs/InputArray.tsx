import React, { useState } from 'react';
import { FieldArray, FieldArrayRenderProps, getIn } from 'formik';
import CustomInput from './CustomInput';
import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
} from '@chakra-ui/react';

interface FieldConfig {
  name: string;
  label: string;
  type: 'text' | 'email' | 'number' | 'textarea';
}

interface CustomFieldArrayProps {
  name: string;
  nameItem?: string;
  label: string;
  fields: FieldConfig[] | 'text' | 'email' | 'number' | 'textarea';
  // initialItem: Record<string, any>; // La estructura del objeto que se añadirá
  initialItem: Record<string, any> | string; // La estructura del objeto que se añadirá
}

// eslint-disable-next-line max-params
function getErrors(isStringArray: boolean, form: any, name: string, index: number) {
  if (isStringArray) {
    return getIn(form.errors, `${name}.${index}`);
  }

  return Object.keys(form.values[name][index]).some((fieldName) =>
    getIn(form.errors, `${name}.${index}.${fieldName}`)
  );
}

/**
 * Component to handle arrays of inputs
 * @param {string} name - Name of the field
 * @param {string} label - Label of the field
 * @param {FieldConfig} fields - Fields to display can be an array of objects or a string
 * @param {FieldConfig} field - if is required to display an array of objects, this is the object with the fields to display: {name, label, type}
 * @param {string} field - if is required to display an array of strings, this is the type of the field: 'text' | 'email' | 'number' | 'textarea'
 * @param {Record<string, any> | string} initialItem - Initial item to add
 * @param {string} nameItem - Name of the item
 * @returns {JSX.Element} - Returns the JSX element
 */

export default function InputArray({ name, label, fields, initialItem, nameItem }: CustomFieldArrayProps) {
  const isStringArray = typeof fields === 'string';
  const [expandedIndex, setExpandedIndex] = useState<number | null>(null);

  return (
    <FieldArray name={name}>
      {({ remove, push, form }: FieldArrayRenderProps) => {
        // console.log('values ', form.values[name]);
        // Accede a errores del array y campos
        const arrayErrors = getIn(form.errors, name) as any;
        // console.log('arrayErrors ', arrayErrors);

        return (
          <div>
            <label className="block text-gray-700 text-[16px] mb-2">{label}</label>
            <Accordion
              allowToggle
              index={expandedIndex !== null ? [expandedIndex] : undefined}
              onChange={(index) => {
                setExpandedIndex(Array.isArray(index) ? index[0] : index);
              }}
            >
              {form.values[name].map((item: any, index: number) => {
                const hasErrors = getErrors(isStringArray, form, name, index);

                return (
                  <AccordionItem key={index}>
                    <h2>
                      <AccordionButton>
                        <Box flex="1" textAlign="left">
                          {/* {isStringArray ? `${label} ${index + 1}` : `Item ${index + 1}`} */}
                          {`${nameItem || 'Elemento'} ${index + 1}`}
                          {hasErrors && <span className="text-red-500"> - Falta información</span>}
                        </Box>
                        <AccordionIcon />
                      </AccordionButton>
                    </h2>
                    <AccordionPanel pb={4}>
                      {isStringArray ? (
                        <CustomInput name={`${name}.${index}`} type={fields} label={``} defaultValue={item} />
                      ) : (
                        (fields as FieldConfig[]).map((field) => (
                          <CustomInput
                            key={field.name}
                            name={`${name}.${index}.${field.name}`}
                            type={field.type}
                            label={field.label}
                            defaultValue={item[field.name]}
                          />
                        ))
                      )}
                      <button type="button" onClick={() => remove(index)} className="mt-2 text-red-500">
                        Eliminar
                      </button>
                    </AccordionPanel>
                  </AccordionItem>
                );
              })}
            </Accordion>
            {arrayErrors && typeof arrayErrors === 'string' && (
              <div className="mt-2 text-red-500">{arrayErrors}</div>
            )}
            <button
              type="button"
              // onClick={() => push(initialItem)}
              onClick={() => {
                const newIndex = form.values[name].length;
                push(initialItem);
                setExpandedIndex(newIndex); // Abre el nuevo elemento
              }}
              className="mt-2 p-2 bg-blue-500 text-white rounded"
            >
              Agregar
            </button>
          </div>
        );
      }}
    </FieldArray>
  );
}
/* 

interface ShowFieldsProps {
  fields: FieldConfig[] | 'text' | 'email' | 'number' | 'textarea';
  item: any;
  name: string;
  index: number;
  isStringArray: boolean;
}

function ShowFields({ fields, item, name, index, isStringArray }: ShowFieldsProps) {
  return (
    <>
      {isStringArray ? (
        <CustomInput
          name={`${name}.${index}`}
          type={fields as FieldConfig['type']}
          label={``}
          defaultValue={item}
        />
      ) : (
        (fields as FieldConfig[]).map((field) => {
          const fieldKey = Object.keys(item).find((key) =>
            field.name.toLowerCase().includes(key.toLowerCase())
          )!;

          return (
            <CustomInput
              key={field.name}
              name={`${name}.${index}.${field.name}`}
              type={field.type}
              label={field.label}
              defaultValue={item[fieldKey] || ''}
            />
          );
        })
      )}
    </>
  );
}
*/
