import React, { useState } from 'react';
import { ErrorMessage, useField } from 'formik';

interface RadioInputProps {
  label: string;
  name: string;
  value: number;
  isOneOption?: boolean;
  setSelectedAddress?: any;
  checked?: boolean;
}

export default function InputRadio({
  label,
  name,
  value,
  checked,
  isOneOption,
  setSelectedAddress,
}: RadioInputProps) {
  const [field, meta] = useField(name);
  const [isChecked, setIsChecked] = useState(checked);

  const hasError = meta.touched && meta.error;

  return (
    <div
      className="flex flex-col"
      onClick={() => {
        field.onChange({ target: { name, value } });
        setIsChecked((prev) => {
          if (isChecked) return prev;
          return !prev;
        });
        if (isOneOption) {
          setSelectedAddress(value);
        }
      }}
    >
      {/* <label className="block text-gray-700 text-[16px] mb-2" htmlFor={name}>
        {label}
      </label> */}
      <div className="flex">
        {/* {options.map((option) => ( */}
        <div>
          {/* <div className="w-[10px] h-[10px] border-[1px] border-gray bg-rose-500"></div> */}
          <div
            className={`
              w-[20px]
              h-[20px] 
              flex
              justify-center
              items-center
              rounded-full 
              border-2 
              border-gray-300 
            `}
          >
            <div
              className={`
                w-[11px]
                h-[11px]
                rounded-full
                ${isChecked ? 'bg-purple-500' : 'bg-transparent'}
                transform 
              `}
            ></div>
          </div>
          {/* <Field type="radio" id={name} name={name} value={value} className="custom-radio" /> */}
        </div>
        <span className="ml-2">{label}</span>
        {/* ))} */}
      </div>
      {hasError && <ErrorMessage name={name} component="div" className="text-red-500 text-sm mt-1" />}
    </div>
  );
}
