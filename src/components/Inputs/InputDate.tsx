import { Input } from '@chakra-ui/react';
import { Field, useField, ErrorMessage, FieldProps } from 'formik';
import { useState } from 'react';
import { format, set } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';

interface InputDateProps {
  name: string;
  label: string;
  includeHours?: boolean;
  defaultValue?: string;
  todayLimit?: boolean;
  onChange?: (value: string) => void;
  dateMsg?: string;
}

export default function InputDate({
  name,
  label,
  includeHours,
  defaultValue,
  todayLimit = false,
  onChange,
  dateMsg,
}: InputDateProps) {
  const mexicoCityTimeZone = 'America/Mexico_City';
  const today = new Date();
  const todayInMexicoCityTime = utcToZonedTime(today, mexicoCityTimeZone);
  const todayLimit2 = set(todayInMexicoCityTime, { hours: 0, minutes: 0 });

  let todayLimitString: string;
  if (includeHours) {
    todayLimitString = format(todayLimit2, "yyyy-MM-dd'T'HH:mm");
  } else {
    todayLimitString = format(todayLimit2, 'yyyy-MM-dd');
  }
  const [field, meta] = useField(name);
  const hasError = meta.touched && meta.error;

  const [inputValue, setInputValue] = useState(defaultValue || field.value);

  return (
    <Field
      name={name}
      component={({ form }: FieldProps) => (
        <div>
          <label className="block text-gray-700 text-[16px] mb-2">
            {label}{' '}
            {/* {inputValue && (
              <>
                | <span className="text-[14px]">(mes/dia/año)</span>
              </>
            )} */}
          </label>
          <Input
            defaultValue={inputValue}
            name={name}
            type={includeHours ? 'datetime-local' : 'date'}
            onBlur={(e) => {
              e.preventDefault();
              console.log('e.target.value ', e.target.value);
              form.setFieldTouched(field.name, true);
              form.setFieldValue(field.name, e.target.value);
              setInputValue(e.target.value);
              if (onChange) {
                onChange(e.target.value);
              }
            }}
            // onChange={(e) => {
            //   e.preventDefault();
            //   console.log('e.target.value ', e.target.value);
            //   form.setFieldTouched(field.name, true);
            //   form.setFieldValue(field.name, e.target.value);
            //   setInputValue(e.target.value);
            //   if (onChange) {
            //     onChange(e.target.value);
            //   }
            // }}
            className={`
              border ${hasError ? 'border-red-500' : 'border-[#9CA3AF]'}
              border-2
              text-[gray]
              rounded
              px-3 
              h-[40px] 
              w-full
              outline-none 
            `}
            min={todayLimit ? todayLimitString : undefined}
            // onKeyDown={(e) => e.preventDefault()}
          />
          <p className="ml-[8px] text-[14px] ">{dateMsg ? dateMsg : '*Utilizar el boton de calendario'} </p>
          {hasError && <ErrorMessage name={name} component="div" className="text-red-500 text-sm mt-1" />}
        </div>
      )}
    ></Field>
  );
}
