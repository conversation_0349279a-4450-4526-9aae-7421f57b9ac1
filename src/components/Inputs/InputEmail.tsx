import React from "react";
import { Field, useField } from "formik";
import ErrorMessage from "./ErrorMessage";
import { useI18n } from "@/i18n/client";

interface InputProps {
  label: string;
  name: string;
  placeholder?: string;
}

export default function InputEmail({ label, name, placeholder="Email" }: InputProps) {
  const [, meta] = useField(name);

  const hasError = meta.touched && meta.error;
  const t = useI18n();

  return (
    <div className="flex flex-col">
      <label className="block  text-textGray1 text-sm mb-2" htmlFor={name}>
        {t(label as keyof typeof t)}
      </label>
      <div className="flex">
        <Field
          type="email"
          id="emailLogin"
          name={name}
          className={`
            border ${hasError ? "border-red-500" : "border-primaryPrussianBlue"}
            border
            text-black 
            rounded
            px-3 
            h-[40px] 
            w-full
          `}
          placeholder={t(placeholder as keyof typeof t)}
        />
      </div>
      {hasError && (
         <ErrorMessage 
            message={t(meta.error as keyof typeof t)}
          />
      )}
    </div>
  );
}
