import { FormLabel, Radio, RadioGroup, Stack } from '@chakra-ui/react';
import { ErrorMessage, useField } from 'formik';
import { useEffect } from 'react';

interface RadioOptionsProps {
  // setSelectedAddress: any;
  selectedAddress: string;
  options: { value: string; label: string; name: string }[];
  name: string;
  onChange?: (value?: any) => void;
}

export default function RadioOptions({
  name,
  options,
  selectedAddress,
  // setSelectedAddress,
  onChange,
}: RadioOptionsProps) {
  const [field, meta] = useField(name);
  const hasError = meta.touched && meta.error;

  useEffect(() => {
    // console.log('Selected Address:', selectedAddress, 'soy el valor de field: ', field.value);
  }, [selectedAddress, field.value]);

  return (
    <>
      <RadioGroup
        onChange={(value) => {
          if (onChange) {
            onChange(value);
          }
          field.onChange({ target: { name: field.name, value } });
        }}
        value={selectedAddress}
      >
        <Stack direction="column">
          {options.map((op, i) => (
            <Radio
              key={i}
              colorScheme="purple"
              value={op.value}
              name={name}
              // onClick={() => {
              //   field.onChange({ target: { name } });
              // }}
            >
              <FormLabel
                cursor="pointer"
                htmlFor={name}
                onClick={() => {
                  if (onChange) {
                    onChange(field.value);
                  }
                  field.onChange({ target: { name: field.name, value: field.value } });
                }}
              >
                {op.label}
              </FormLabel>
            </Radio>
          ))}
        </Stack>
      </RadioGroup>
      {hasError && <ErrorMessage name={name} component="div" className="mt-1 text-sm text-red-500" />}
    </>
  );
}
