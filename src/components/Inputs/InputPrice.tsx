import React from 'react';
import { ErrorMessage, Field, useField } from 'formik';
import { BsCurrencyDollar } from 'react-icons/bs';

interface InputProps {
  label: string;
  name: string;
  placeholder: string;
  onChange?: (value: string) => void;
  currencySymbol?: string;
}

export default function InputPrice({ label, name, placeholder, onChange, currencySymbol }: InputProps) {
  const [field, meta] = useField(name);

  const hasError = meta.touched && meta.error;

  return (
    <div className="flex flex-col">
      <label className="block text-gray-700 text-normal mb-2" htmlFor={name}>
        {label}
      </label>
      <div className="flex relative items-center">
        <div
          className={`
            w-[40px] 
            h-[40px] 
            border-[1px]
            border-l-2
            border-r-0
            border-y-2
            ${hasError ? 'border-red-500' : 'border-[#9CA3AF]'}
            bg-[#EAECEE]
            rounded-l 
            flex 
            items-center 
            justify-center
          `}
        >
          <BsCurrencyDollar color={hasError ? 'red' : '#9CA3AF'} size={20} />
        </div>
        <Field
          type="number"
          id="emailLogin"
          name={name}
          value={field.value}
          onChange={(e: any) => {
            if (onChange) {
              onChange(e.target.value);
            }
            field.onChange({ target: { name, value: e.target.value } });
          }}
          placeholder={placeholder}
          className={`
            border ${hasError ? 'border-red-500' : 'border-[#9CA3AF]'}
            border-y-2
            border-r-2
            text-black 
            rounded-r
            px-3 
            h-[40px] 
            w-full
            outline-none 
          `}
        />
        <div className="absolute right-0 pr-3">{currencySymbol ? currencySymbol : 'MX'}</div>
      </div>
      {hasError && <ErrorMessage name={name} component="div" className="text-red-500 text-sm mt-1" />}
    </div>
  );
}
