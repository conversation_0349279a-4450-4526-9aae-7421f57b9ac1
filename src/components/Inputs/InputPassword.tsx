import React from "react";
import { Field, useField } from "formik";
import { LuEyeOff } from "react-icons/lu";
import  ErrorMessage from "./ErrorMessage";
import { useI18n } from "@/i18n/client";

interface InputProps {
  label: string;
  name: string;
  placeholder?: string;
}

export default function InputPassword({
  label,
  name,
  placeholder = "Password",
}: InputProps) {
  const [, meta] = useField(name);
  const hasError = meta.touched && meta.error;

  const t = useI18n();

  return (
    <div className="flex flex-col">
      <label className="block  text-textGray1 text-sm mb-2" htmlFor={name}>
        {t(label as keyof typeof t)}
      </label>
      <div className="flex">
        <Field
          type="password"
          id={name}
          name={name}
          placeholder={t(placeholder as keyof typeof t)}
          className={`
            border ${hasError ? "border-red-500" : "border-primaryPrussianBlue"}
            border-r-0
            text-black 
            rounded
            rounded-r-none
            px-3 
            h-[40px] 
            w-full
          `}
        />
        <div
          className={`
            w-[40px] 
            h-[40px] 
            border ${hasError ? "border-red-500" : "border-primaryPrussianBlue"}
            border-l-0
            rounded-r 
            flex 
            items-center 
            justify-center
          `}
        >
          <LuEyeOff size={20} />
        </div>
      </div>
      {hasError && <ErrorMessage message={t(meta.error as keyof typeof t)} />}
    </div>
  );
}
