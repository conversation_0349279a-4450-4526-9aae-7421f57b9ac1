import type { HTMLAttributes, ReactNode } from 'react';
import type { STEPPER_TRANSLATION_KEYS } from './useStepper';
import type { Options } from 'nuqs';

interface StepperProps extends HTMLAttributes<HTMLDivElement> {
  queryKey?: string;
  /**
   * The steps of the stepper.
   * @default []
   */
  steps: Step[];

  /** A function to translate the keys of the stepper. */
  t?: (key: STEPPER_TRANSLATION_KEYS) => string;
}

interface Step {
  /** The component of the step. */
  component: ReactNode;
  /** The description of the step. */
  description?: string;
  /** The title of the step. */
  title: string;
  /** Whether the step should be skipped. */
  skip?: boolean;
}

type StepperDirection = 1 | -1 | 0;

/** the first number is the active step, the second number is the direction and starts with 0 */
type StepTuple = [number, StepperDirection, skipped?: boolean];

export type { StepperProps, Step, StepTuple, StepperDirection };