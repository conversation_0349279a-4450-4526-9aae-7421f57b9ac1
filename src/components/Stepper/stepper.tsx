// import { animationFade, animationSlideInOut } from '#animations/index.ts';
// import { animationFade}
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AnimatePresence, motion } from 'framer-motion';
import { useQueryState } from 'nuqs';
import { GiCheckMark } from 'react-icons/gi';
import { LuFastForward } from 'react-icons/lu';

import type { ButtonHTMLAttributes, CSSProperties } from 'react';
import type { StepperProps, StepTuple } from './stepper-types';

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import { StepperContextProvider, useStepper } from './useStepper';
import { cn } from '@/lib/utils';

const BaseLine = ({ width }: { width: CSSProperties['width'] }) => (
  <div className="bg-muted-foreground absolute left-1/2 top-1/2 h-[4px] -translate-y-1/2" style={{ width }} />
);

const ActiveLine = (props: { width: CSSProperties['width'] }) => (
  <motion.div
    animate={{ width: props.width }}
    className="bg-primary/50 absolute left-1/2 top-1/2 h-[4px] -translate-y-1/2"
  />
);

const CompletedLine = (props: { width: CSSProperties['width'] }) => (
  <motion.div
    animate={{ width: props.width }}
    className="bg-primary absolute left-1/2 top-1/2 h-[4px] -translate-y-1/2"
  />
);

/**
 * A stepper component that allows you to create a step-by-step process for example a form.
 */
const Stepper = ({ steps, className, children, t, queryKey = 'stepper', ...restOfProps }: StepperProps) => {
  const [activeStepTuple, setActiveStepTuple] = useQueryState(queryKey, {
    parse: (query: string) => query.split(',').map(Number) as StepTuple,
    serialize: value => value.join(','),
    history: 'push',
    defaultValue: [0, 0] as StepTuple,
  });

  const activeStep = activeStepTuple[0];
  const direction = activeStepTuple[1];

  const goToNextStep = () => {
    // Check if the current active step is not the last step
    if (activeStep < steps.length - 1) {
      let nextStep = activeStep + 1;

      // Skip steps that are marked to be skipped
      while (nextStep < steps.length && steps[nextStep]?.skip) {
        nextStep += 1;
      }

      // If the next step is valid, update the active step tuple
      if (nextStep < steps.length) {
        setActiveStepTuple([nextStep, 1]);
      }
    }
  };

  const goToPreviousStep = (step?: number) => {
    // Check if a specific step is provided and valid
    if (step !== undefined && step >= 0 && step < activeStep) {
      // Skip any steps that are marked to be skipped
      while (step >= 0 && steps[step]?.skip) {
        step -= 1;
      }
      // If a valid previous step is found, update the active step tuple
      if (step >= 0) {
        setActiveStepTuple([step, -1]);
      }
    } else if (activeStep > 0) {
      // If no specific step is provided, move to the previous step
      let previousStep = activeStep - 1;
      // Skip any steps that are marked to be skipped
      while (previousStep >= 0 && steps[previousStep]?.skip) {
        previousStep -= 1;
      }
      // If a valid previous step is found, update the active step tuple
      if (previousStep >= 0) {
        setActiveStepTuple([previousStep, -1]);
      }
    }
  };

  return (
    <StepperContextProvider
      value={{
        activeStepTuple,
        goToNextStep,
        goToPreviousStep,
        setActiveStepTuple,
        t,
      }}>
      <div className={cn('flex flex-col gap-8', className)} {...restOfProps}>
        <Alert className="overflow-hidden">
          <AnimatePresence custom={direction} initial={false} mode="wait">
            {/* <motion.div key={steps[activeStep]!.title} custom={direction} {...animationSlideInOut}> */}
            <div>

              <AlertTitle className="text-balance text-center text-2xl">{steps[activeStep]!.title}</AlertTitle>
              <AlertDescription className="text-balance text-center">{steps[activeStep]!.description}</AlertDescription>
            </div>
            {/* </motion.div> */}
          </AnimatePresence>
        </Alert>

        <div className="flex justify-between">
          {steps.map((_, index) => {
            const isCompleted = index < activeStep;

            return (
              <div
                key={index}
                className={cn('group relative flex grow justify-center', [
                  index === activeStep && 'active',
                  isCompleted && 'completed',
                ])}>
                {index === 0 && (
                  <>
                    <BaseLine width={`${100 * (steps.length - 1)}%`} />
                    <ActiveLine width={`${100 * activeStep}%`} />
                    <CompletedLine width={`${100 * (activeStep <= 0 ? 0 : activeStep - 1)}%`} />
                  </>
                )}

                <StepButton
                  skipped={steps[index]?.skip}
                  disabled={index >= activeStep}
                  delayed={index === activeStep}
                  onClick={() => {
                    goToPreviousStep(index);
                  }}>
                  {index + 1}
                </StepButton>
              </div>
            );
          })}
        </div>
      </div>

      <div>
        <AnimatePresence custom={direction} initial={false} mode="wait">
          {/* <motion.div key={activeStep} custom={direction} {...animationFade}> */}
          <div>

            <div className="py-0">{steps[activeStep]?.component}</div>
          </div>
          {/* </motion.div> */}
        </AnimatePresence>
        {children}
      </div>
    </StepperContextProvider>
  );
};

interface StepButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  delayed?: boolean;
  skipped?: boolean;
}

const StepButton = ({ children, skipped, delayed, className, ...restOfProps }: StepButtonProps) => {
  const { t } = useStepper();

  if (skipped)
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <button
              {...restOfProps}
              className={cn(
                'bg-primary z-10 grid h-[40px] w-[40px] cursor-not-allowed place-items-center rounded-full',
                className,
              )}>
              <LuFastForward className="fill-primary-foreground stroke-primary-foreground h-6 w-6" />
            </button>
          </TooltipTrigger>
          <TooltipContent>{t?.('skip') || 'Deze stap is overgeslagen.'}</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );

  return (
    <button
      {...restOfProps}
      className={cn(
        delayed && 'delay-500',
        'bg-muted relative z-10 grid h-[40px] w-[40px] place-items-center rounded-full transition-all',
        'text-muted-foreground font-bold',
        'group/button',
        'ring-primary ring-offset-background/90 group-[.active]:bg-primary group-[.active]:text-primary-foreground ring-offset-4 group-[.active]:ring-2',
        'group-[.completed]:bg-primary group-[.completed]:text-primary-foreground',
        className,
      )}
      type="button">
      <GiCheckMark
        className={cn(
          'absolute grid place-items-center opacity-0 transition-opacity',
          'group-[.completed]:opacity-100 group-has-[button:hover,button:focus]:opacity-0',
        )}
      />
      <span
        className={cn(
          'transition-opacity',
          'group-[.completed]:opacity-0 group-has-[button:hover,button:focus]:opacity-100',
        )}>
        {children}
      </span>
    </button>
  );
};

export { Stepper };