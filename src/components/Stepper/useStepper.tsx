'use client';

import { createContext, useContext } from 'react';

import type { StepTuple } from './stepper-types';

export type STEPPER_TRANSLATION_KEYS = 'skip';

interface StepperContextType {
  goToNextStep: () => void;
  goToPreviousStep: (step?: number) => void;
  setActiveStepTuple: (value: StepTuple) => void;
  activeStepTuple: StepTuple;
  t?: (key: STEPPER_TRANSLATION_KEYS) => string;
}

const StepperContext = createContext<StepperContextType | null>(null);

export const useStepper = () => {
  const context = useContext(StepperContext);

  if (!context) {
    throw new Error('useStepper must be used within a Stepper');
  }

  return context;
};

export const StepperContextProvider = StepperContext.Provider;