'use client';
import { signOut } from 'next-auth/react';
import PrimaryButton from './PrimaryButton';
import { useI18n } from '@/i18n/client';
import { deleteCookie } from 'cookies-next';

export default function LogoutButton() {
  const t = useI18n();

  const handleLogout = async () => {
    try {

      deleteCookie('cookieKey');
      await signOut();
    } catch (error) {
      console.error('Error during logout:', error);
      await signOut(); // Intentar signOut de todas formas
    }
  };

  return (
    <PrimaryButton onClick={handleLogout}>
      {t("SignOut")}
    </PrimaryButton>
  );
}
