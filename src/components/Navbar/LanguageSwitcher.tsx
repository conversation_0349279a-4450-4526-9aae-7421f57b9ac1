"use client";
import React from "react";
import {
  Flex,
  HStack,
  VStack,
  useColorModeValue,
  Text,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
} from "@chakra-ui/react";
import { useChangeLocale, useI18n } from "@/i18n/client";
import { languagesWithCode, Tlanguages } from "@/i18n/configs";

const LanguageSwitcher = () => {
  const t = useI18n();
  const changeLocale = useChangeLocale();

  const handleLocaleChange = (
    e: React.MouseEvent<HTMLButtonElement, MouseEvent>
  ) => {
    const lang = e.currentTarget.ariaLabel as Tlanguages;
    changeLocale(lang);
  };

  return (
    <Flex alignItems={"center"}>
      <Menu>
        <MenuButton
          py={3}
          pr={4}
          transition="all 0.3s"
          _focus={{ boxShadow: "none" }}
        >
          <HStack>
            <VStack
              display={{ base: "none", md: "flex" }}
              alignItems="flex-start"
              spacing="1px"
              ml="2"
            >
              <Text fontSize="sm">{t("LanguageSwitcher")} </Text>
            </VStack>
          </HStack>
        </MenuButton>
        <MenuList
          bg={useColorModeValue("white", "gray.900")}
          borderColor={useColorModeValue("gray.200", "gray.700")}
        >
          {languagesWithCode.map(({ code, lang }) => {
            return (
              <MenuItem
                key={code}
                onClick={handleLocaleChange}
                aria-label={code}
              >
                <Text>{lang}</Text>
              </MenuItem>
            );
          })}
        </MenuList>
      </Menu>
    </Flex>
  );
};

export default LanguageSwitcher;
