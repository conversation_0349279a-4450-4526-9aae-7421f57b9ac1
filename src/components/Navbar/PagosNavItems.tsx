import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React from 'react';
import {
  FaCarSide,
  FaChevronDown,
  FaRegUserCircle,
  FaProductHunt,
  FaFileInvoiceDollar,
} from 'react-icons/fa';
import { AiFillSchedule } from 'react-icons/ai';

const PagosNavItems = () => {
  const pathname = usePathname();
  const navButton = {
    icon: <FaCarSide size={18} />,
    name: 'Pagos',
  };
  const subNavLinks = [
    {
      link: '/dashboard/pagos/clientes',
      icon: <FaRegUserCircle size={16} />,
      name: 'Clientes',
    },
    {
      link: '/dashboard/pagos/productos',
      icon: <FaProductHunt size={16} />,
      name: 'Productos',
    },
    {
      link: '/dashboard/pagos/payments',
      icon: <FaFileInvoiceDollar size={16} />,
      name: 'Payments',
      prefetch: true,
    },
    {
      link: '/dashboard/pagos/schedule',
      icon: <AiFillSchedule size={16} />,
      name: 'Calendario de pago',
    },
  ];

  return (
    <details className="group transition-all duration-150 ml-4 content-center h-10 open:h-52 overflow-hidden ">
      {pathname.includes('/dashboard/pagos') ? (
        <summary className="transition-all duration-500 flex cursor-pointer items-center rounded-lg px-4 py-2 mr-2 bg-primaryPurple text-white">
          {navButton.icon}
          <span className="text-[20px] font-semibold leading-tight ml-3.5 transition duration-300 text-white ">
            {navButton.name}
          </span>
          <span className="ml-auto shrink-0 transition duration-300 group-open:-rotate-180 ">
            <FaChevronDown color={'white'} />
          </span>
        </summary>
      ) : (
        <summary className="transition-all duration-500 flex cursor-pointer items-center rounded-lg px-4 py-2">
          <FaCarSide size={18} />
          <span className="text-gray-600 text-[20px] font-semibold leading-tight ml-3.5 transition duration-300 ">
            {' '}
            Pagos{' '}
          </span>
          <span className="ml-auto shrink-0 transition duration-300 group-open:-rotate-180 ">
            <FaChevronDown color={pathname.includes('/dashboard/pagos') ? 'white' : 'black'} />
          </span>
        </summary>
      )}

      <nav className="mt-1.5 ml-8 flex flex-col transition-all duration-500">
        {subNavLinks.map((item, key) => {
          return (
            <Link href={item.link} key={key} prefetch={item.prefetch || false}>
              <button
                className={
                  pathname === item.link
                    ? 'flex items-center rounded-lg px-4 py-2 text-white bg-primaryPurple'
                    : 'flex items-center rounded-lg px-4 py-2 hover:bg-gray-100 hover:text-gray-700'
                }
                key={key}
              >
                {item.icon}
                <span className="ml-3 text-sm font-medium"> {item.name} </span>
              </button>
            </Link>
          );
        })}
      </nav>
    </details>
  );
};

export default PagosNavItems;
