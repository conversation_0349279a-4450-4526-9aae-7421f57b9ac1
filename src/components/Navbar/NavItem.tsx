'use client';
import { useCurrentLocale } from '@/i18n/client';
import { Flex, FlexProps, Icon } from '@chakra-ui/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { IconType } from 'react-icons';

interface NavItemProps extends FlexProps {
  icon: IconType;
  children: string;
  link: string;
}
const NavItem = ({ icon, children, link, ...rest }: NavItemProps) => {
  const pathname = usePathname();
  const currentLocale = useCurrentLocale();
  const currentPath = pathname.replace(`/${currentLocale}`, '');



  // Ensure the link includes the current locale
  const localizedLink = link.startsWith('/') ? `/${currentLocale}${link}` : link;

  // Check if the current path matches the link (ignoring locale)
  const isActive = currentPath === link;

  return (
    <Link href={localizedLink} prefetch={false} className={` ${isActive ? 'bg-primary-gradient' : 'bg-white' } px-2 py-2 rounded-md
    ${isActive ? 'text-white' : 'text-primarySlateBlue'}
    `}>
        {icon && (
          <Icon
            fontSize="25"
            _groupHover={{
              color: 'white',
            }}
            as={icon}
          />
        )}
    </Link>
  );
};

export default NavItem;
