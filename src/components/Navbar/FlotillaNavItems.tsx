/* eslint-disable prettier/prettier */
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React from 'react';
import { FaCarSide, FaChevronDown, FaFileInvoice, FaRegUserCircle } from 'react-icons/fa';
import { FiHome } from 'react-icons/fi';
import { RiArrowDownSFill } from 'react-icons/ri';

interface FlotillaNavItemsProps {
  role: string;
}

const FlotillaNavItems = ({ role }: FlotillaNavItemsProps) => {
  const pathname = usePathname();
  const navButton = {
    icon: <FaCarSide size={18} />,
    name: 'Flotilla',
  };
  const subNavLinks = [
    {
      link: '/dashboard/flotilla/activos',
      icon: <FaRegUserCircle size={16} />,
      name: 'Activos',
    },
    {
      link: '/dashboard/flotilla/invoiced',
      icon: <FaFileInvoice size={16} />,
      name: 'Facturados',
    },
    {
      link: '/dashboard/flotilla/stock',
      icon: <FiHome size={16} />,
      name: 'Stock',
    },
    {
      link: '/dashboard/flotilla/reingresos',
      icon: <FaCarSide size={16} />,
      name: 'Reingresos',
    },
    {
      link: '/dashboard/flotilla/bajas',
      // icon: <FaCarSide size={16} />,
      icon: <RiArrowDownSFill size={16} />,
      name: 'Bajas',
    },
  ];

  const auditorNavLinks = [
    {
      link: '/dashboard/flotilla/activos',
      icon: <FaRegUserCircle size={16} />,
      name: 'Activos',
    },
  ];

  const navLinks = role === 'auditor' ? auditorNavLinks : subNavLinks;

  return (
    <details className="group transition-all duration-150 ml-4 content-center h-10 open:h-56 overflow-hidden ">
      {pathname.includes('/dashboard/flotilla') ? (
        <summary className="transition-all duration-500 flex cursor-pointer items-center rounded-lg px-4 py-2 mr-2 bg-primaryPurple text-white">
          {navButton.icon}
          <span className="text-[20px] font-semibold leading-tight ml-3.5 transition duration-300 text-white ">
            {navButton.name}
          </span>
          <span className="ml-auto shrink-0 transition duration-300 group-open:-rotate-180 ">
            <FaChevronDown color={'white'} />
          </span>
        </summary>
      ) : (
          <summary
            className={`transition-all duration-500 flex cursor-pointer items-center rounded-lg px-4 py-2 mr-2`}
          >
          <FaCarSide size={18} />
          <span className="text-gray-600 text-[20px] font-semibold leading-tight ml-3.5 transition duration-300 ">
            {' '}
            Flotilla{' '}
          </span>
          <span className="ml-auto shrink-0 transition duration-300 group-open:-rotate-180 ">
            <FaChevronDown color={pathname.includes('/dashboard/flotilla') ? 'white' : 'black'} />
          </span>
        </summary>
      )}

      <nav className="mt-1.5 ml-8 flex flex-col transition-all duration-500 ">
        {navLinks.map((item, key) => {
          return (
            <Link href={item.link} key={key} prefetch={false}>
              <button
                className={
                  pathname === item.link
                    ? 'flex items-center rounded-lg px-4 py-2 text-white bg-primaryPurple'
                    : 'flex items-center rounded-lg px-4 py-2 hover:bg-gray-100 hover:text-gray-700'
                }
                key={key}
              >
                {item.icon}
                <span className="ml-3 text-sm font-medium"> {item.name} </span>
              </button>
            </Link>
          );
        })}
      </nav>
    </details>
  );
};

export default FlotillaNavItems;
