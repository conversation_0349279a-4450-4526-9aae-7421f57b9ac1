'use client';
import React, { ReactNode } from 'react';
import {
  Avatar,
  Box,
  Button,
  Flex,
  HStack,
  VStack,
  useColorModeValue,
  Text,
  useDisclosure,
  FlexProps,
  Menu,
  MenuButton,
  MenuDivider,
  MenuItem,
  MenuList,
  useToast,
  Show,
} from '@chakra-ui/react';
import { FiHome } from 'react-icons/fi';
import Image from 'next/image';
import { useParams, useRouter } from 'next/navigation';
import { signOut } from 'next-auth/react';
import axios from 'axios';
import { URL_API } from '@/constants';
import { useCurrentUser } from '@/Providers/CurrentUserProvider';
import { deleteCookie } from 'cookies-next';
import { useI18n } from '@/i18n/client';

// We've removed all the sidebar navigation items since we're hiding the left menu

export default function SideNavbar({ children }: { children: ReactNode }) {
  // We no longer need the sidebar disclosure state since we've removed the sidebar
  const { onOpen } = useDisclosure();

  return (
    <>
      <Show>
        <Box minH="10vh" bg={useColorModeValue('gray.100', 'gray.900')} position="relative" w="100%">
          {/* Left sidebar has been removed */}
          <MobileNav onOpen={onOpen} />
          <Box
            as="main"
            ml={{ base: 0, md: 0 }} /* Removed left margin since sidebar is gone */
            bg="#FAFAFA"
            className='py-5 px-5 lg:px-10 lg:py-8'
            minH="calc(100vh - 60px)"
            w="auto"
          >
            {children}
          </Box>
        </Box>
      </Show>
    </>
  );
}

// We've removed the SidebarContent component since we're hiding the left menu

interface MobileProps extends FlexProps {
  onOpen: () => void;
}

const MobileNav = ({ onOpen, ...rest }: MobileProps) => {
  const router = useRouter();
  const toast = useToast();
  const params = useParams();
  const locale = params.locale as string || 'es';
  const t = useI18n();

  const { user } = useCurrentUser();
  const handleLogOut = async () => {
    try {
      const response = await axios.post(
        `${URL_API}/auth/logout`,
        {},
        {
          headers: {
            Authorization: `Bearer ${user?.accessToken}`,
          },
        }
      );
      if (response.status === 200) {
        // La solicitud fue exitosa, puedes ejecutar otra función aquí
        // setTimeout(() => {
        toast({
          status: 'info',
          title: 'Cerrando sesión...',
          // description: 'Vuelve a iniciar sesión porfavor',
          isClosable: true,
          position: 'top',
          duration: 3000,
        });
        setTimeout(async () => {
          await signOut();
        }, 3000);
        // }, 1000);
      }
    } catch (error) {
      // force sign out
    }
    deleteCookie('cookieKey');
    router.push('/');
    signOut();
  };

  const goToDashboard = () => {
    router.push(`/${locale}/dashboard/home`);
  };

  return (
    <Flex
      // as="nav"
      ml={{ base: 0, md: 0 }} // Updated margin since sidebar is gone
      px={{ base: 4, md: 4 }}
      height="60px"
      alignItems="center"
      position="sticky"
      zIndex={20}
      top={0}
      bg={useColorModeValue('white', 'gray.900')}
      borderBottomWidth="1px"
      borderBottomColor={useColorModeValue('gray.200', 'gray.700')}
      justifyContent="space-between" // Changed to space-between to accommodate home button
      {...rest}
    >
      <Flex alignItems="center">
        <Image alt="logo" src="/images/logo.png" width="70" height="70" className="mr-4" />

        {/* Home button */}
        <Button
          aria-label="Go to dashboard"
          leftIcon={<FiHome size={20} />}
          px={3}
          py={2}
          borderRadius="md"
          variant="ghost"
          color="blue.500"
          onClick={goToDashboard}
          _hover={{ bg: 'blue.50' }}
          mr={2}
        >
          {t('Home')}
        </Button>
      </Flex>

      <HStack spacing={{ base: '0', md: '6' }}>
        <Flex alignItems={'center'}>
          <Menu>
            <MenuButton py={3} pr={4} transition="all 0.3s" _focus={{ boxShadow: 'none' }}>
              <HStack>
                <VStack display={{ base: 'none', md: 'flex' }} alignItems="flex-start" spacing="1px" ml="2">
                  <Text fontSize="sm">{user?.name} </Text>
                </VStack>
                <Avatar
                  size={'sm'}
                  ml="10px"
                  src={
                    user?.image?.url ||
                    'https://w7.pngwing.com/pngs/81/570/png-transparent-profile-logo-computer-icons-user-user-blue-heroes-logo-thumbnail.png'
                  }
                />
              </HStack>
            </MenuButton>
            <MenuList
              bg={useColorModeValue('white', 'gray.900')}
              borderColor={useColorModeValue('gray.200', 'gray.700')}
            >
              <MenuItem onClick={() => router.push('/dashboard/perfil')}>Perfil</MenuItem>
              <MenuDivider />
              <MenuItem onClick={() => handleLogOut()}>Cerrar Sesión</MenuItem>
            </MenuList>
          </Menu>
        </Flex>
      </HStack>
    </Flex>
  );
};
