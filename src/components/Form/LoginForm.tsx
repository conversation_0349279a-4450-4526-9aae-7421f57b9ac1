import { Form } from "formik";
import InputEmail from "../Inputs/InputEmail";
import InputPassword from "../Inputs/InputPassword";
import Link from "next/link";
import { useI18n } from "@/i18n/client";


const ForgotPassowrdLink = () => {
  const t = useI18n();
  return (
    <Link
      className="text-[12px] font-bold pt-6 pb-4 text-primar underline"
      href="#"
      prefetch={false}
    >
      {t("ForgotPassword")}
    </Link>
  );
};

interface LoginFormProps {
  valid?: boolean;
  dirty?: boolean;
}

export default function LoginForm({ valid, dirty }: LoginFormProps) {
  const t = useI18n();
  const validate = dirty && valid;

  return (
    <Form className="grid w-full xl:w-3/4 gap-3">
      <InputEmail name="email" placeholder="Email" label="Email" />
      <InputPassword name="password" label="Password" />
      <ForgotPassowrdLink />
      <button
        type="submit"
        disabled={!validate}
        className={`
          ${!validate ? "bg-[#9CA3AF]" : "bg-primary-gradient"}
          text-white   rounded-full  h-10 cursor-pointer w-60 `}
      >
        {t("SignIn")}
      </button>
    </Form>
  );
}
