import { Form } from 'formik';
import InputPassword from '../Inputs/InputPassword';

interface ResetPasswordFormProps {
  valid?: boolean;
  dirty?: boolean;
}

export default function ChangePasswordForm({ valid, dirty }: ResetPasswordFormProps) {
  const validate = dirty && valid;

  return (
    <Form className="grid w-full gap-3">
      <p className="flex justify-center text-2xl text-black">Recuperar contraseña</p>
      <div className="grid gap-2">
        <InputPassword name="password" label="Contraseña" />
        <InputPassword name="confirmPassword" label="Confirmar Contraseña" />
      </div>
      <button
        type="submit"
        disabled={!validate}
        className={`
          ${!validate ? 'bg-[#9CA3AF]' : 'bg-[#5800F7]'}
          text-white rounded-md  h-[40px] cursor-pointer`}
      >
        Actualiza Contraseña
      </button>
      <a className="text-[14px] text-[#5800F7] font-bold flex justify-center mt-6" href="/">
        Atrás para iniciar sesión
      </a>
    </Form>
  );
}
