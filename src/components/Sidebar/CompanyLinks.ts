import { FiHome } from 'react-icons/fi'
import { BiUser, BiSolidCity, BiCalendar, BiListUl } from 'react-icons/bi'
import { CompanyUserRole } from '@/lib/user-types'

interface LinkItemProps {
  name: string;
  icon: any;
  link: string;
  category?: string;
}

const AllCompanyLinkItems: Array<LinkItemProps> = [
  // Panel
  { name: "Dashboard", icon: FiHome, link: "/dashboard", category: "Panel" },

  // Administración
  { name: 'Users', icon: BiUser, link: "/dashboard/company-users", category: "Administración" },
  { name: 'Cities', icon: BiSolidCity, link: "/dashboard/cities", category: "Administración" },

  // Operaciones
  { name: 'Installations Calendar', icon: BiCalendar, link: "/dashboard/installation-schedule", category: "Operaciones" },
  { name: 'Installation List', icon: BiListUl, link: "/dashboard/installation-list", category: "Operaciones" },
];

export const getCompanyLinks = (userRole: CompanyUserRole): Array<LinkItemProps> => {
  return AllCompanyLinkItems.filter(item => {
    // Si el link es para Users, solo mostrarlo para owner y admin
    if (item.link === '/dashboard/company-users') {
      return userRole === CompanyUserRole.OWNER || userRole === CompanyUserRole.ADMIN;
    }
    // Mostrar todos los demás links
    return true;
  });
};