import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';

const badgeVariants = cva(
  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  {
    variants: {
      variant: {
        default: 'border-transparent bg-primary text-primary-foreground hover:bg-primary/80',
        secondary: 'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',
        destructive: 'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',
        outline: 'text-foreground',
        // for active status badge
        // active: 'bg-green-100 text-green-800 hover:bg-green-100',
        // for invited status badge
        // invited: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100',
        // for suspended status badge
        // suspended: 'bg-red-100 text-red-800 hover:bg-red-100',
        // for default status badge
        // default: 'bg-gray-100 text-gray-800 hover:bg-gray-100',
      },
      status: {
        default: 'bg-gray-100 text-gray-800 hover:bg-gray-100',
        active: 'bg-green-100 text-green-800 hover:bg-green-100',
        inactive: 'bg-red-100 text-red-800 hover:bg-red-100',
        invited: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100',
        suspended: 'bg-red-100 text-red-800 hover:bg-red-100',
      }
    },
    defaultVariants: {
      variant: 'default',
      status: 'default',
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
  VariantProps<typeof badgeVariants> { }

function Badge({ className, variant, status, ...props }: BadgeProps) {
  return <div className={cn(badgeVariants({ variant, status }), className)} {...props} />;
}

export { Badge, badgeVariants };