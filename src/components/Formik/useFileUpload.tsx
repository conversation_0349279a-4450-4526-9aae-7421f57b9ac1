// import { useState } from 'react';
// import { MediaStatus, MediaType } from '@/app/[locale]/dashboard/clientes/solicitudes/enums';
// import { URL_API } from '@/constants';

// interface Media {
//   id?: string | undefined;

//   fileName: string;

//   path: string;

//   type: MediaType;

//   status: MediaStatus;

//   mimeType: string;

//   url?: string;
// }

// const useFileUpload = (mediaType: string, totalFiles = 5) => {
//   const [uploadedFiles, setUploadedFiles] = useState<Media[]>([]);
//   const [isLoading, setIsLoading] = useState(false);

//   const uploadFile = async (file: File) => {
//     setIsLoading(true);

//     const formData = new FormData();

//     formData.append('file', file);
//     formData.append('mediaType', mediaType);

//     try {
//       const response = await fetch(`${URL_API}/media/upload`, {
//         method: 'POST',
//         body: formData,
//       });

//       const data = await response.json();
//       if (response.ok) {
//         setUploadedFiles([...uploadedFiles, data.data]);
//       }
//     } catch (error) {
//       console.log(error);
//     }

//     setIsLoading(false);
//   };

//   const deleteFile = (index: number) => {
//     const newUploadedFiles = uploadedFiles.filter((_, idx) => idx !== index);
//     setUploadedFiles(newUploadedFiles);
//   };

//   return {
//     uploadedFiles,
//     uploadFile,
//     deleteFile,
//     isLoading,
//     istotalFilesReached: uploadedFiles.length >= totalFiles,
//   };
// };

// export default useFileUpload;
