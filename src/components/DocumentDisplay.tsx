'use client';
import { MdPictureAsPdf } from 'react-icons/md';

export default function DocumentDisplay({
  url,
  docName,
  backgroundColor = 'rgba(88, 0, 247, 0.2)',
  textColor = '#5800F7',
}: {
  url: string;
  docName: string;
  backgroundColor?: string;
  textColor?: '#5800F7' | '#5CAFFC' | '#CC6FF8';
}) {
  const lenghtName = docName.length;
  return (
    <div
      className={`
        h-[28px] 
        ${lenghtName > 22 ? 'w-[100%]' : 'w-[max-content]'}
        px-2 
        flex items-center 
        gap-1
        overflow-hidden
        text-[${textColor || 'white'}] 
        rounded 
        cursor-pointer 
        mt-1
      `}
      style={{ backgroundColor }}
      onClick={() => {
        window.open(url);
      }}
    >
      <div>
        <MdPictureAsPdf size={20} />
      </div>
      <p className="whitespace-nowrap overflow-hidden text-overflow-ellipsis">{docName}</p>
    </div>
  );
}
