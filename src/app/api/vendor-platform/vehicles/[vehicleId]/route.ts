import { NextRequest, NextResponse } from 'next/server';
import { URL_API } from '@/constants';
import getCurrentUser from '@/actions/getCurrentUser';

export async function GET(
  request: NextRequest,
  { params }: { params: { vehicleId: string } }
) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Usuario no autenticado' },
        { status: 401 }
      );
    }

    const { vehicleId } = params;
    console.log('🔗 API Route - Getting vehicle info for ID:', vehicleId);

    // Forward the request to the backend API
    const response = await fetch(
      `${URL_API}/vendor-platform/vehicles/${vehicleId}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log('✅ API Route - Backend response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Route - Backend error:', errorText);
      
      return NextResponse.json(
        {
          success: false,
          message: `Error del backend: ${response.status}`,
          data: null,
        },
        { status: response.status }
      );
    }

    const responseData = await response.json();
    console.log('📦 API Route - Vehicle data received:', responseData);

    return NextResponse.json({
      success: true,
      data: responseData.data || responseData,
      message: responseData.message || 'Vehículo obtenido exitosamente',
    });
  } catch (error: any) {
    console.error('❌ API Route - Error getting vehicle:', error);

    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Error inesperado al obtener el vehículo',
        data: null,
      },
      { status: 500 }
    );
  }
}
