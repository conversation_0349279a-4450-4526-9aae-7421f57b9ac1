import { NextRequest, NextResponse } from 'next/server';
import { URL_API } from '@/constants';
import getCurrentUser from '@/actions/getCurrentUser';

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Usuario no autenticado' },
        { status: 401 }
      );
    }

    // Get search parameters from the URL
    const { searchParams } = new URL(request.url);
    const stockId = searchParams.get('stockId');
    const limit = searchParams.get('limit') || '10';
    const page = searchParams.get('page') || '1';
    const status = searchParams.get('status');
    const workshopId = searchParams.get('workshopId');
    const search = searchParams.get('search');

    console.log('🔗 API Route - Getting corrective maintenance orders');
    console.log('📋 API Route - Parameters:', {
      stockId,
      limit,
      page,
      status,
      workshopId,
      search
    });

    // Build the backend API URL
    const backendUrl = new URL(`${URL_API}/vendor-platform/corrective-maintenance/orders`);
    
    // Add query parameters
    if (stockId) backendUrl.searchParams.append('stockId', stockId);
    if (limit) backendUrl.searchParams.append('limit', limit);
    if (page) backendUrl.searchParams.append('page', page);
    if (status) backendUrl.searchParams.append('status', status);
    if (workshopId) backendUrl.searchParams.append('workshopId', workshopId);
    if (search) backendUrl.searchParams.append('search', search);

    console.log('📞 API Route - Backend URL:', backendUrl.toString());

    // Forward the request to the backend API
    const response = await fetch(backendUrl.toString(), {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    console.log('✅ API Route - Backend response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Route - Backend error:', errorText);
      
      return NextResponse.json(
        {
          success: false,
          message: `Error del backend: ${response.status}`,
          data: [],
        },
        { status: response.status }
      );
    }

    const responseData = await response.json();
    console.log('📦 API Route - Backend response data:', responseData);
    console.log('📊 API Route - Data length:', responseData.data?.length || 0);

    return NextResponse.json({
      success: true,
      data: responseData.data || [],
      message: responseData.message || 'Órdenes obtenidas exitosamente',
      pagination: responseData.pagination || {
        page: parseInt(page),
        limit: parseInt(limit),
        total: responseData.data?.length || 0,
        totalPages: Math.ceil((responseData.data?.length || 0) / parseInt(limit)),
      },
    });
  } catch (error: any) {
    console.error('❌ API Route - Error getting orders:', error);

    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Error inesperado al obtener las órdenes',
        data: [],
      },
      { status: 500 }
    );
  }
}
