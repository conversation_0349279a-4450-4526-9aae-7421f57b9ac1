import { NextRequest, NextResponse } from 'next/server';

interface SlackWebhookPayload {
  webhook_url: string;
  channel?: string;
  message: {
    text?: string;
    attachments?: any[];
    blocks?: any[];
  };
  quotation_id: string;
  type: 'new' | 'reminder' | 'overdue' | 'urgent';
}

export async function POST(request: NextRequest) {
  try {
    const body: SlackWebhookPayload = await request.json();
    
    // Validate required fields
    if (!body.webhook_url || !body.message || !body.quotation_id) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields: webhook_url, message, quotation_id' 
        },
        { status: 400 }
      );
    }

    // Validate webhook URL format
    if (!body.webhook_url.startsWith('https://hooks.slack.com/')) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid Slack webhook URL format' 
        },
        { status: 400 }
      );
    }

    // Prepare Slack message payload
    const slackPayload = {
      channel: body.channel,
      username: 'OneCarNow Fleet Bot',
      icon_emoji: ':car:',
      ...body.message,
    };

    // Send to Slack
    const slackResponse = await fetch(body.webhook_url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(slackPayload),
    });

    if (!slackResponse.ok) {
      const errorText = await slackResponse.text();
      console.error('Slack webhook error:', errorText);
      
      return NextResponse.json(
        { 
          success: false, 
          error: `Slack webhook failed: ${slackResponse.status} ${slackResponse.statusText}`,
          details: errorText
        },
        { status: 500 }
      );
    }

    // Log successful notification (in production, you might want to store this in a database)
    console.log(`Slack notification sent successfully for quotation ${body.quotation_id} (type: ${body.type})`);

    // Optional: Store notification log in database
    try {
      await logNotification({
        quotationId: body.quotation_id,
        type: body.type,
        channel: body.channel,
        sentAt: new Date().toISOString(),
        status: 'sent'
      });
    } catch (logError) {
      console.error('Failed to log notification:', logError);
      // Don't fail the request if logging fails
    }

    return NextResponse.json({
      success: true,
      message: 'Slack notification sent successfully',
      quotation_id: body.quotation_id,
      type: body.type,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error sending Slack notification:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error while sending Slack notification',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Optional: Function to log notifications to database
async function logNotification(notificationData: {
  quotationId: string;
  type: string;
  channel?: string;
  sentAt: string;
  status: string;
}) {
  // In a real implementation, you would save this to your database
  // Example with your existing API:
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/notifications/slack/log`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.API_TOKEN}`, // If needed
      },
      body: JSON.stringify(notificationData),
    });

    if (!response.ok) {
      throw new Error(`Failed to log notification: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error logging notification to database:', error);
    throw error;
  }
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({
    service: 'Slack Notification API',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    endpoints: {
      'POST /api/notifications/slack/send': 'Send Slack notification'
    }
  });
}