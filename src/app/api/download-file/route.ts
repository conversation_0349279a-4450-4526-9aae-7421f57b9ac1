import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const { url, filename } = await req.json(); // Parseamos el cuerpo de la solicitud

    if (!url || !filename) {
      return new Response('Missing URL or filename parameter', { status: 400 });
    }
    const response = await fetch(url);

    if (!response.ok) {
      return NextResponse.json({ message: 'Url expired' }, { status: response.status });
    }

    let contentType = response.headers.get('content-type');
    if (!contentType) {
      contentType = 'application/octet-stream'; // Valor por defecto si no se obtiene el content-type
    }

    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    return new Response(buffer, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    });
  } catch (error: any) {
    console.log('error.message', error.message, error.response, '-----------------------------------');
    // console.error('Erro<PERSON> al descargar el archivo:', error.message);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
