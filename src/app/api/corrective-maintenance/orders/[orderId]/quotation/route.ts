import { NextRequest, NextResponse } from 'next/server';
import { URL_API } from '@/constants';
import getCurrentUser from '@/actions/getCurrentUser';

export async function POST(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Usuario no autenticado' },
        { status: 401 }
      );
    }

    const { orderId } = params;
    console.log('🔗 API Route - Creating quotation for order:', orderId);

    // Get the FormData from the request
    const formData = await request.formData();
    
    // Log FormData contents for debugging
    console.log('📋 API Route - FormData entries:');
    for (const [key, value] of formData.entries()) {
      if (value instanceof File) {
        console.log(`${key}: File(${value.name}, ${value.size} bytes)`);
      } else {
        console.log(`${key}: ${value}`);
      }
    }

    // Forward the FormData to the backend API
    const response = await fetch(
      `${URL_API}/vendor-platform/corrective-maintenance/orders/${orderId}/quotation`,
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          // Don't set Content-Type, let fetch set it automatically for FormData
        },
        body: formData,
      }
    );

    console.log('✅ API Route - Backend response status:', response.status);

    const responseData = await response.json();
    console.log('📦 API Route - Backend response data:', responseData);

    if (response.ok) {
      return NextResponse.json({
        success: true,
        data: responseData.data || responseData,
        message: responseData.message || 'Cotización creada exitosamente',
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          message: responseData.message || 'Error al crear la cotización',
        },
        { status: response.status }
      );
    }
  } catch (error: any) {
    console.error('❌ API Route - Error creating quotation:', error);

    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Error inesperado al crear la cotización',
      },
      { status: 500 }
    );
  }
}
