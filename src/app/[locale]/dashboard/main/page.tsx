import { getI18n } from "@/i18n/server";

export const metadata = {
  title: "Dashboard",
  description: "Dashboard Screen",
};

export default async function Dashboard() {
  const t = await getI18n();

  return (
    <div className="flex flex-col">
      <div className="mb-4 flex flex-row justify-between items-center">
        <h1 className="bg-primary-gradient bg-clip-text text-transparent font-bold text-[32px]">
          {t("Welcome")}
        </h1>
      </div>
      <div className=" bg-white p-4 rounded-md ">
        <div className="bg-primary-gradient px-4 py-4  text-white rounded-sm ">
          <h2 className="font-bold">
            {t("GettingStartedMsg")}
          </h2>
          <p>{"Lorem ipsum dolor sit amet"}</p>
        </div>
      </div>
    </div>
  );
}
