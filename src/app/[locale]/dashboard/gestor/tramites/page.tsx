'use client';

import { Box, Heading, SimpleGrid, Text, useToast } from '@chakra-ui/react';
import { Suspense } from 'react';
import { useRouter } from 'next/navigation';
import { useTramites, Procedure } from '@/Providers/TramitesProvider';
import { useMemo } from 'react';

export default function TramitesPage() {
  const { loading, error, procedures } = useTramites();
  const toast = useToast();
  const router = useRouter();

  const proceduresByCity = useMemo(() => {
    if (!procedures || procedures.length === 0) return {};

    return procedures.reduce((acc: Record<string, any[]>, procedure) => {
      const state = procedure.tramiteId?.state || 'Sin estado';
      if (!acc[state]) {
        acc[state] = [];
      }
      acc[state].push(procedure);
      return acc;
    }, {});
  }, [procedures]);

  if (error) {
    toast({
      title: 'Error',
      description: 'No se pudieron cargar los trámites',
      status: 'error',
      duration: 5000,
      isClosable: true,
    });
  }

  if (loading) {
    return (
      <Box p={4}>
        <Heading as="h1" size="xl" mb={4}>
          Cargando trámites...
        </Heading>
      </Box>
    );
  }

  if (Object.keys(proceduresByCity).length === 0) {
    return (
      <Box p={4}>
        <Heading as="h1" size="xl" mb={4}>
          Trámites por ciudad
        </Heading>
        <Text>No hay trámites disponibles</Text>
      </Box>
    );
  }

  return (
    <Box p={4}>
      <Heading as="h1" size="xl" mb={4}>
        Trámites por ciudad
      </Heading>

      <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
        {Object.entries(proceduresByCity).map(([city, cityProcedures]) => (
          <Box
            key={city}
            p={6}
            borderWidth="1px"
            borderRadius="lg"
            _hover={{ shadow: 'md' }}
            cursor="pointer"
            onClick={() => router.push(`/dashboard/gestor/tramites/${city}`)}
          >
            <Heading size="md" mb={2}>{city.toString().toUpperCase()}</Heading>
            <Text color="gray.600">{cityProcedures.length} trámites</Text>
            <Text color="gray.500" fontSize="sm" mt={2}>
              {cityProcedures.filter(p => p.status === 'Pendiente').length} pendientes
            </Text>
          </Box>
        ))}
      </SimpleGrid>
    </Box>
  );
}
