'use client';

import { useState, useEffect } from 'react';
import { Procedure } from '@/Providers/TramitesProvider';
import { Calendar, Clock, FileText, X, DollarSign, Timer } from 'lucide-react';
import axios from 'axios';
import { URL_API } from '@/constants';
import { useCurrentUser } from '@/Providers/CurrentUserProvider';
import DocumentDisplay from '@/components/DocumentDisplay';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';

interface ProcedureDetailModalProps {
  procedure: Procedure | null;
  isOpen: boolean;
  onClose: () => void;
  onStartProcedure: (procedureId: string) => Promise<void>;
  onUpdate?: () => void;
}

interface DocumentWithStatus extends Document {
  _id?: string;
  name: string;
  format: string;
  url?: string;
  hasPhysical?: boolean;
}

// Form value interfaces
type PlateFormValues = {
  plates: string;
  frontImg: File | null;
  backImg: File | null;
  platesDocument: File | null;
}

type CirculationFormValues = {
  number: string;
  validity: string;
  frontImg: File | null;
  backImg: File | null;
  noExpiration: boolean;
}

// Form schemas
const plateSchema = Yup.object().shape({
  plates: Yup.string().max(12, 'Máximo 12 caracteres').required('No. de placas requerido'),
  frontImg: Yup.mixed()
    .test('fileType', 'El archivo debe ser de tipo imagen', (value) => {
      if (!value) return true;
      const fileTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      const fileType = (value as File).type;
      return fileTypes.includes(fileType);
    })
    .required('Imagen frontal requerida'),
  backImg: Yup.mixed()
    .test('fileType', 'El archivo debe ser de tipo imagen', (value) => {
      if (!value) return true;
      const fileTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      const fileType = (value as File).type;
      return fileTypes.includes(fileType);
    })
    .required('Imagen trasera requerida'),
  platesDocument: Yup.mixed()
    .test('fileType', 'El archivo debe ser PDF', (value) => {
      if (!value) return true;
      const fileTypes = ['application/pdf'];
      const fileType = (value as File).type;
      return fileTypes.includes(fileType);
    })
    .required('Documento de placas requerido')
});

const circulationCardSchema = Yup.object().shape({
  number: Yup.string().max(16, 'Máximo 16 caracteres').required('No. de tarjeta requerido'),
  validity: Yup.string().max(10, 'Máximo 10 caracteres').when('noExpiration', {
    is: false,
    then: Yup.string().required('Vigencia requerida'),
    otherwise: Yup.string().notRequired(),
  }),
  frontImg: Yup.mixed()
    .test('fileType', 'El archivo debe ser de tipo imagen', (value) => {
      if (!value) return true;
      const fileTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      const fileType = (value as File).type;
      return fileTypes.includes(fileType);
    })
    .required('Imagen frontal requerida'),
  backImg: Yup.mixed()
    .test('fileType', 'El archivo debe ser de tipo imagen', (value) => {
      if (!value) return true;
      const fileTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      const fileType = (value as File).type;
      return fileTypes.includes(fileType);
    })
    .required('Imagen trasera requerida'),
  noExpiration: Yup.boolean().default(false)
});

export default function ProcedureDetailModal({
  procedure,
  isOpen,
  onClose,
  onStartProcedure,
  onUpdate
}: ProcedureDetailModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [documents, setDocuments] = useState<DocumentWithStatus[]>([]);
  const [documentsComplete, setDocumentsComplete] = useState(false);
  const [token, setToken] = useState('');
  const [tokenValidated, setTokenValidated] = useState(false);
  const [documentType, setDocumentType] = useState<'plates' | 'circulation' | null>(null);
  const [documentsUploaded, setDocumentsUploaded] = useState(false);
  const [platesUploaded, setPlatesUploaded] = useState(false);
  const [circulationUploaded, setCirculationUploaded] = useState(false);
  const { user } = useCurrentUser();

  // Form hooks
  const plateForm = useForm({
    resolver: yupResolver(plateSchema) as any,
    defaultValues: {
      plates: '',
      frontImg: null,
      backImg: null,
      platesDocument: null
    }
  }) as any;

  const circulationForm = useForm({
    resolver: yupResolver(circulationCardSchema) as any,
    defaultValues: {
      number: '',
      validity: '',
      frontImg: null,
      backImg: null,
      noExpiration: false
    }
  }) as any;

  // Limpiar estados cuando se abre el modal
  useEffect(() => {
    if (isOpen && procedure) {
      setError(null);
      setSuccess(null);

      // Determinar automáticamente el tipo de documento basado en el nombre del trámite
      if (procedure.tramiteId.name) {
        const tramiteName = procedure.tramiteId.name.toLowerCase();
        if (tramiteName.includes('placa')) {
          setDocumentType('plates');
        } else if (tramiteName.includes('circulacion') || tramiteName.includes('circulación')) {
          setDocumentType('circulation');
        }
      }

      // Inicializar documentos con el estado de posesión física
      if (procedure.tramiteId.documents) {
        const docsWithStatus = procedure.tramiteId.documents.map(doc => ({
          ...doc,
          hasPhysical: false
        }));
        setDocuments(docsWithStatus as DocumentWithStatus[]);
      }
    }
  }, [isOpen, procedure]);

  // Validar que tenemos los datos necesarios
  if (!isOpen) return null;
  if (!procedure) {
    console.error('No procedure data provided to modal');
    return null;
  }


  const handleContinueProcess = async () => {
    try {
      setIsSubmitting(true);
      setError(null);
      setSuccess(null);

      // Make API request to update procedure status to "En Proceso"
      if (user?.accessToken) {
        await axios.patch(`${URL_API}/vendor-platform/gestores/procedimientos/${procedure._id}`,
          {
            status: 'En Proceso'
          },
          {
            headers: {
              Authorization: `Bearer ${user.accessToken}`,
            }
          }
        );
      } else {
        throw new Error('No hay sesión de usuario activa');
      }

      setSuccess('Trámite actualizado correctamente');

      // Close modal and update table after a short delay
      setTimeout(() => {
        onClose();
        if (onUpdate) onUpdate();
      }, 1000);
    } catch (err) {
      console.error('Error updating procedure:', err);
      setError('Error al actualizar el trámite. Intente nuevamente.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const validateToken = async () => {
    try {
      setIsSubmitting(true);
      setError(null);

      if (!token.trim()) {
        setError('Por favor ingrese un token válido');
        setIsSubmitting(false);
        return;
      }

      // Make API request to validate token
      if (user?.accessToken) {
        const response = await axios.post(
          `${URL_API}/vendor-platform/gestores/procedimientos/token/validate`,
          { token },
          {
            headers: {
              Authorization: `Bearer ${user.accessToken}`,
            }
          }
        );

        if (response.status === 200) {
          setTokenValidated(true);
          setSuccess('Token validado correctamente');
        } else {
          setError('Token inválido');
        }
      } else {
        throw new Error('No hay sesión de usuario activa');
      }
    } catch (err) {
      console.error('Error validating token:', err);
      setError('Error al validar el token. Intente nuevamente.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFinishProcedure = async () => {
    try {
      setIsSubmitting(true);
      setError(null);
      setSuccess(null);

      if (!tokenValidated) {
        setError('Debe validar el token primero');
        setIsSubmitting(false);
        return;
      }

      // Make API request to update procedure status to "Completado"
      if (user?.accessToken) {
        await axios.patch(
          `${URL_API}/vendor-platform/gestores/procedimientos/${procedure._id}`,
          {
            status: 'Completado'
          },
          {
            headers: {
              Authorization: `Bearer ${user.accessToken}`,
            }
          }
        );
      } else {
        throw new Error('No hay sesión de usuario activa');
      }

      setSuccess('Trámite finalizado correctamente');

      // Close modal and update table after a short delay
      setTimeout(() => {
        onClose();
        if (onUpdate) onUpdate();
      }, 1000);
    } catch (err) {
      console.error('Error finishing procedure:', err);
      setError('Error al finalizar el trámite. Intente nuevamente.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleStartProcedure = async () => {
    try {
      setIsSubmitting(true);
      setError(null);
      setSuccess(null);

      // Make API request to update procedure status to "Esperando Documentos"
      if (user?.accessToken) {
        await axios.patch(`${URL_API}/vendor-platform/gestores/procedimientos/${procedure._id}`,
          {
            status: 'Esperando Documentos'
          },
          {
            headers: {
              Authorization: `Bearer ${user.accessToken}`,
            }
          }
        );
      } else {
        throw new Error('No hay sesión de usuario activa');
      }

      // Call the onStartProcedure function passed as prop
      await onStartProcedure(procedure._id);

      setSuccess('Trámite iniciado correctamente');

      // Close modal and update table after a short delay
      setTimeout(() => {
        onClose();
        if (onUpdate) onUpdate();
      }, 1000);
    } catch (err) {
      console.error('Error starting procedure:', err);
      setError('Error al iniciar el trámite. Intente nuevamente.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const canStartProcedure = procedure.status === 'Pendiente';

  const handlePlateSubmit = async (values: PlateFormValues) => {
    try {
      setIsSubmitting(true);
      setError(null);

      // Crear un nuevo FormData
      const formData = new FormData();

      // Añadir el número de placas (asegurarse de que sea una cadena limpia)
      formData.append('plates', values.plates.trim());

      // Añadir imágenes y documentos
      if (values.frontImg) {
        formData.append('frontImg', values.frontImg);
      }

      if (values.backImg) {
        formData.append('backImg', values.backImg);
      }

      if (values.platesDocument) {
        formData.append('platesDocument', values.platesDocument);
      }

      // Add history data
      formData.append('historyData[userId]', process.env.NEXT_PUBLIC_USER || '');
      formData.append('historyData[step]', 'DOCUMENTACIÓN');
      formData.append('historyData[description]','Placas editadas');

      // Determinar si estamos editando o agregando nuevas placas
      const isEditing = procedure.status !== 'En Proceso';
      formData.append('isEditing', 'true');

      // Make API request to update plates
      if (user?.accessToken) {
        await axios.patch(
          `${URL_API}/vendor-platform/gestores/procedimientos/update/carPlates/${procedure.vehicleId}`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
              Authorization: `bearer ${user.accessToken}`,
            },
          }
        );

        // After saving plates, show the circulation card section
        setPlatesUploaded(true);
        setDocumentType('circulation'); // Automatically switch to circulation card form
        setSuccess('Placas agregadas correctamente. Ahora agregue la tarjeta de circulación.');
      } else {
        throw new Error('No hay sesión de usuario activa');
      }
    } catch (err) {
      console.error('Error uploading plates:', err);
      setError('Error al agregar las placas. Intente nuevamente.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCirculationSubmit = async (values: CirculationFormValues) => {
    try {
      setIsSubmitting(true);
      setError(null);

      // Crear un nuevo FormData
      const formData = new FormData();

      // Añadir número y validez de la tarjeta (asegurarse de que sean cadenas limpias)
      formData.append('number', values.number.trim());

      // Si no tiene vigencia, enviar "Sin vigencia", de lo contrario enviar el valor ingresado
      if (values.noExpiration) {
        formData.append('validity', 'Sin vigencia');
      } else {
        formData.append('validity', values.validity.trim());
      }

      // Añadir imágenes
      if (values.frontImg) {
        formData.append('frontImg', values.frontImg);
      }

      if (values.backImg) {
        formData.append('backImg', values.backImg);
      }

      // Add history data
      formData.append('historyData[userId]', process.env.NEXT_PUBLIC_USER || '');
      formData.append('historyData[step]', 'DOCUMENTACIÓN');
      formData.append('historyData[description]', procedure.status === 'En Proceso' ? 'Tarjeta de circulación agregada' : 'Tarjeta de circulación editada');

      // Determinar si estamos editando o agregando nueva tarjeta
      const isEditing = procedure.status !== 'En Proceso';
      formData.append('isEditing', isEditing ? 'true' : 'false');

      // Añadir physicalStatus válido
      formData.append('physicalStatus', 'AVAILABLE_IN_STOCK');

      // Make API request to update circulation card
      if (user?.accessToken) {
        await axios.patch(
          `${URL_API}/vendor-platform/gestores/procedimientos/update/circulationCard/${procedure.vehicleId}`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
              Authorization: `bearer ${user.accessToken}`,
            },
          }
        );

        // After saving circulation card, show the token section
        setCirculationUploaded(true);
        setDocumentsUploaded(true);
        setSuccess('Tarjeta de circulación agregada correctamente. Ahora valide el token para finalizar.');
      } else {
        throw new Error('No hay sesión de usuario activa');
      }
    } catch (err) {
      console.error('Error uploading circulation card:', err);
      setError('Error al agregar la tarjeta de circulación. Intente nuevamente.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 overflow-auto"
      onClick={onClose} // Cerrar al hacer clic fuera del modal
    >
      <div
        className="relative w-full max-w-2xl p-6 mx-auto my-8 bg-white rounded-lg shadow-xl max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()} // Evitar que se cierre al hacer clic dentro del modal
      >
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute p-1 text-gray-400 transition-colors duration-200 rounded-full top-4 right-4 hover:bg-gray-100 hover:text-gray-600"
        >
          <X className="w-6 h-6" />
        </button>

        {/* Header */}
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-800">{procedure.tramiteId.name}</h2>
          <p className="mt-1 text-sm text-gray-500">{procedure.tramiteId.description}</p>
        </div>

        {/* Status badge */}
        <div className="mb-6">
          <span className={`px-3 py-1 text-sm font-medium rounded-full
            ${procedure.status === 'Completado' ? 'bg-green-100 text-green-800' :
              procedure.status === 'Pendiente' ? 'bg-yellow-100 text-yellow-800' :
              procedure.status === 'En Proceso' ? 'bg-blue-100 text-blue-800' :
              procedure.status === 'Agendado' ? 'bg-purple-100 text-purple-800' :
              procedure.status === 'Cancelado' ? 'bg-red-100 text-red-800' :
              'bg-gray-100 text-gray-800'}`}>
            {procedure.status}
          </span>
        </div>

        {/* Info grid */}
        <div className="grid grid-cols-1 gap-4 mb-6 md:grid-cols-2">
          <div className="flex items-start">
            <Calendar className="w-5 h-5 mr-2 text-gray-400" />
            <div>
              <p className="text-sm font-medium text-gray-500">Fecha de Creación</p>
              <p className="text-sm text-gray-800">{new Date(procedure.createdAt).toLocaleDateString()}</p>
            </div>
          </div>

          <div className="flex items-start">
            <Clock className="w-5 h-5 mr-2 text-gray-400" />
            <div className="w-full">
              <p className="text-sm font-medium text-gray-500">Fecha Estimada de Finalización</p>
              <p className="text-sm text-gray-800">
                {procedure.tramiteId.duration ?
                  new Date(new Date(procedure.createdAt).getTime() + procedure.tramiteId.duration * 24 * 60 * 60 * 1000).toLocaleDateString() :
                  'No especificado'}
              </p>
            </div>
          </div>

          <div className="flex items-start col-span-2">
            <FileText className="w-5 h-5 mr-2 text-gray-400" />
            <div>
              <p className="text-sm font-medium text-gray-500">Notas</p>
              <p className="text-sm text-gray-800">{procedure.tramiteId.description || 'Sin notas'}</p>
            </div>
          </div>

          {/* Cost field - now displayed as read-only */}
          <div className="flex items-start">
            <DollarSign className="w-5 h-5 mr-2 text-gray-400" />
            <div>
              <p className="text-sm font-medium text-gray-500">Costo</p>
              <p className="text-sm text-gray-800">
                {procedure.tramiteId.cost ? `$${procedure.tramiteId.cost.toFixed(2)}` : 'No especificado'}
              </p>
            </div>
          </div>

          {/* Duration field */}
          <div className="flex items-start">
            <Timer className="w-5 h-5 mr-2 text-gray-400" />
            <div>
              <p className="text-sm font-medium text-gray-500">Duración</p>
              <p className="text-sm text-gray-800">
                {procedure.tramiteId.duration ? `${procedure.tramiteId.duration} días` : 'No especificado'}
              </p>
            </div>
          </div>
        </div>

        {/* Documents section */}
        <div className="mb-6">
          <h3 className="mb-2 text-lg font-medium text-gray-800">Documentos Requeridos</h3>
          {procedure.status === 'Esperando Documentos' && (
            <div className="mb-3 flex items-center">
              <input
                type="checkbox"
                id="documents-complete"
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                onChange={(e) => setDocumentsComplete(e.target.checked)}
                checked={documentsComplete}
              />
              <label htmlFor="documents-complete" className="ml-2 text-sm font-medium text-gray-600">
                Documentos completos
              </label>
            </div>
          )}
          {documents && documents.length > 0 ? (
            <ul className="pl-5 space-y-2">
              {documents.map((doc, index) => (
                <li key={doc._id || index} className="text-sm text-gray-600 flex items-center">
                  <FileText className="w-4 h-4 mr-2 text-gray-400" />
                  {doc.name} <span className="text-gray-400 ml-1">({doc.format})</span>
                  {doc.url && (
                    <div className="ml-2">
                      <DocumentDisplay
                        url={doc.url}
                        docName="Ver documento"
                        backgroundColor="rgba(88, 0, 247, 0.1)"
                      />
                    </div>
                  )}

                  {!canStartProcedure && doc.hasPhysical && (
                    <div className="ml-auto">
                      <span className="px-2 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-full">
                        Documento físico ✓
                      </span>
                    </div>
                  )}
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-sm text-gray-500">No hay documentos requeridos</p>
          )}
        </div>
        {/* Error and success messages */}
        {error && (
          <div className="p-3 mb-4 text-sm text-red-700 bg-red-100 rounded-lg">
            {error}
          </div>
        )}

        {success && (
          <div className="p-3 mb-4 text-sm text-green-700 bg-green-100 rounded-lg">
            {success}
          </div>
        )}

        {/* Document upload section for En Proceso status only */}
        {procedure.status === 'En Proceso' && !documentsUploaded && (
          <div className="mb-6">
            <h3 className="mb-2 text-lg font-medium text-gray-800">Documentación</h3>
            <p className="mb-4 text-sm text-gray-600">
              Antes de finalizar el trámite, debe agregar la documentación correspondiente:
            </p>

            {/* Progress indicator */}
            <div className="mb-6">
              <div className="flex items-center mb-2">
                <div className={`w-8 h-8 flex items-center justify-center rounded-full mr-2 ${platesUploaded ? 'bg-green-500 text-white' : 'bg-primaryPurple text-white'}`}>
                  1
                </div>
                <div className="h-1 flex-grow bg-gray-200">
                  <div className={`h-1 ${platesUploaded ? 'bg-green-500' : 'bg-gray-200'}`} style={{ width: platesUploaded ? '100%' : '0%' }}></div>
                </div>
                <div className={`w-8 h-8 flex items-center justify-center rounded-full mx-2 ${circulationUploaded ? 'bg-green-500 text-white' : platesUploaded ? 'bg-primaryPurple text-white' : 'bg-gray-300 text-gray-600'}`}>
                  2
                </div>
                <div className="h-1 flex-grow bg-gray-200">
                  <div className={`h-1 ${circulationUploaded ? 'bg-green-500' : 'bg-gray-200'}`} style={{ width: circulationUploaded ? '100%' : '0%' }}></div>
                </div>
                <div className={`w-8 h-8 flex items-center justify-center rounded-full ml-2 ${documentsUploaded ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-600'}`}>
                  3
                </div>
              </div>
              <div className="flex justify-between text-xs text-gray-600">
                <span className={platesUploaded ? 'text-green-500 font-medium' : ''}>Placas</span>
                <span className={circulationUploaded ? 'text-green-500 font-medium' : ''}>Tarjeta de Circulación</span>
                <span className={documentsUploaded ? 'text-green-500 font-medium' : ''}>Token</span>
              </div>
            </div>

            {/* Plates form - only show if not uploaded yet */}
            {!platesUploaded && (
              <form onSubmit={plateForm.handleSubmit(handlePlateSubmit)} className="space-y-4">
                <h4 className="font-medium text-gray-700">Paso 1: Agregar Placas</h4>
                <div>
                  <label htmlFor="plates" className="block text-sm font-medium text-gray-700">
                    Número de Placas <span className="text-red-500">*</span>
                  </label>
                  <input
                    id="plates"
                    type="text"
                    {...plateForm.register('plates')}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                  {plateForm.formState.errors.plates && (
                    <p className="mt-1 text-sm text-red-600">{plateForm.formState.errors.plates.message?.toString()}</p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="frontImg" className="block text-sm font-medium text-gray-700">
                      Imagen Frontal <span className="text-red-500">*</span>
                    </label>
                    <div className="mt-1 mb-2">
                      <img
                        src="/images/placas/ejemplo-frontal.svg"
                        alt="Ejemplo de imagen frontal de placas"
                        className="w-full max-w-[200px] h-auto border border-gray-300 rounded-md"
                      />
                      <p className="text-xs text-gray-500 mt-1">Ejemplo: Imagen frontal de placas</p>
                    </div>
                    <input
                      id="frontImg"
                      type="file"
                      accept="image/jpeg,image/jpg,image/png"
                      onChange={(e) => {
                        if (e.target.files && e.target.files.length > 0) {
                          plateForm.setValue('frontImg', e.target.files[0]);
                        }
                      }}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    />
                    {plateForm.formState.errors.frontImg && (
                      <p className="mt-1 text-sm text-red-600">{plateForm.formState.errors.frontImg.message?.toString()}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="backImg" className="block text-sm font-medium text-gray-700">
                      Imagen Trasera <span className="text-red-500">*</span>
                    </label>
                    <div className="mt-1 mb-2">
                      <img
                        src="/images/placas/ejemplo-trasero.svg"
                        alt="Ejemplo de imagen trasera de placas"
                        className="w-full max-w-[200px] h-auto border border-gray-300 rounded-md"
                      />
                      <p className="text-xs text-gray-500 mt-1">Ejemplo: Imagen trasera de placas</p>
                    </div>
                    <input
                      id="backImg"
                      type="file"
                      accept="image/jpeg,image/jpg,image/png"
                      onChange={(e) => {
                        if (e.target.files && e.target.files.length > 0) {
                          plateForm.setValue('backImg', e.target.files[0]);
                        }
                      }}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    />
                    {plateForm.formState.errors.backImg && (
                      <p className="mt-1 text-sm text-red-600">{plateForm.formState.errors.backImg.message?.toString()}</p>
                    )}
                  </div>
                </div>

                <div>
                  <label htmlFor="platesDocument" className="block text-sm font-medium text-gray-700">
                    Documento de Placas (PDF) <span className="text-red-500">*</span>
                  </label>
                  <input
                    id="platesDocument"
                    type="file"
                    accept="application/pdf"
                    onChange={(e) => {
                      if (e.target.files && e.target.files.length > 0) {
                        plateForm.setValue('platesDocument', e.target.files[0]);
                      }
                    }}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                  {plateForm.formState.errors.platesDocument && (
                    <p className="mt-1 text-sm text-red-600">{plateForm.formState.errors.platesDocument.message?.toString()}</p>
                  )}
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`w-full px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                    !isSubmitting ? 'bg-primaryPurple hover:bg-primaryBtnHover' : 'bg-gray-400 cursor-not-allowed'
                  }`}
                >
                  {isSubmitting ? 'Guardando...' : 'Continuar con Tarjeta de Circulación'}
                </button>
              </form>
            )}

            {/* Circulation card form - only show if plates are uploaded but circulation card is not */}
            {platesUploaded && !circulationUploaded && (
              <form onSubmit={circulationForm.handleSubmit(handleCirculationSubmit)} className="space-y-4">
                <h4 className="font-medium text-gray-700">Paso 2: Agregar Tarjeta de Circulación</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="number" className="block text-sm font-medium text-gray-700">
                      Número de Tarjeta <span className="text-red-500">*</span>
                    </label>
                    <input
                      id="number"
                      type="text"
                      {...circulationForm.register('number')}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    />
                    {circulationForm.formState.errors.number && (
                      <p className="mt-1 text-sm text-red-600">{circulationForm.formState.errors.number.message?.toString()}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="validity" className="block text-sm font-medium text-gray-700">
                      Vigencia <span className={circulationForm.watch('noExpiration') ? '' : 'text-red-500'}>*</span>
                    </label>
                    <input
                      id="validity"
                      type="date"
                      disabled={circulationForm.watch('noExpiration')}
                      {...circulationForm.register('validity')}
                      className={`mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                        circulationForm.watch('noExpiration') ? 'bg-gray-100 cursor-not-allowed' : ''
                      }`}
                    />
                    {circulationForm.formState.errors.validity && (
                      <p className="mt-1 text-sm text-red-600">{circulationForm.formState.errors.validity.message?.toString()}</p>
                    )}

                    <div className="mt-2 flex items-center">
                      <input
                        id="noExpiration"
                        type="checkbox"
                        {...circulationForm.register('noExpiration')}
                        className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        onChange={(e) => {
                          circulationForm.setValue('noExpiration', e.target.checked);
                          if (e.target.checked) {
                            circulationForm.setValue('validity', '');
                          }
                        }}
                      />
                      <label htmlFor="noExpiration" className="ml-2 block text-sm text-gray-700">
                        No tiene vigencia
                      </label>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="frontImg" className="block text-sm font-medium text-gray-700">
                      Imagen Frontal <span className="text-red-500">*</span>
                    </label>
                    <div className="mt-1 mb-2">
                      <img
                        src="/images/tarjeta-circulacion/ejemplo-frontal.svg"
                        alt="Ejemplo de imagen frontal de tarjeta de circulación"
                        className="w-full max-w-[200px] h-auto border border-gray-300 rounded-md"
                      />
                      <p className="text-xs text-gray-500 mt-1">Ejemplo: Imagen frontal de tarjeta de circulación</p>
                    </div>
                    <input
                      id="frontImg"
                      type="file"
                      accept="image/jpeg,image/jpg,image/png"
                      onChange={(e) => {
                        if (e.target.files && e.target.files.length > 0) {
                          circulationForm.setValue('frontImg', e.target.files[0]);
                        }
                      }}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    />
                    {circulationForm.formState.errors.frontImg && (
                      <p className="mt-1 text-sm text-red-600">{circulationForm.formState.errors.frontImg.message?.toString()}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="backImg" className="block text-sm font-medium text-gray-700">
                      Imagen Trasera <span className="text-red-500">*</span>
                    </label>
                    <div className="mt-1 mb-2">
                      <img
                        src="/images/tarjeta-circulacion/ejemplo-trasero.svg"
                        alt="Ejemplo de imagen trasera de tarjeta de circulación"
                        className="w-full max-w-[200px] h-auto border border-gray-300 rounded-md"
                      />
                      <p className="text-xs text-gray-500 mt-1">Ejemplo: Imagen trasera de tarjeta de circulación</p>
                    </div>
                    <input
                      id="backImg"
                      type="file"
                      accept="image/jpeg,image/jpg,image/png"
                      onChange={(e) => {
                        if (e.target.files && e.target.files.length > 0) {
                          circulationForm.setValue('backImg', e.target.files[0]);
                        }
                      }}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    />
                    {circulationForm.formState.errors.backImg && (
                      <p className="mt-1 text-sm text-red-600">{circulationForm.formState.errors.backImg.message?.toString()}</p>
                    )}
                  </div>
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`w-full px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                    !isSubmitting ? 'bg-primaryPurple hover:bg-primaryBtnHover' : 'bg-gray-400 cursor-not-allowed'
                  }`}
                >
                  {isSubmitting ? 'Guardando...' : 'Continuar con Token'}
                </button>
              </form>
            )}
          </div>
        )}

        {/* Token validation section - only show after both documents are uploaded */}
        {procedure.status === 'En Proceso' && documentsUploaded && (
          <div className="mb-6">
            <h3 className="mb-2 text-lg font-medium text-gray-800">Paso 3: Finalizar Trámite</h3>
            <p className="mb-2 text-sm text-gray-600">Para finalizar el trámite, ingrese el token de validación:</p>

            <div className="flex space-x-2">
              <input
                type="text"
                value={token}
                onChange={(e) => setToken(e.target.value)}
                placeholder="Ingrese el token"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={tokenValidated}
              />
              <button
                onClick={validateToken}
                disabled={isSubmitting || tokenValidated}
                className={`px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                  !isSubmitting && !tokenValidated ? 'bg-primaryPurple hover:bg-primaryBtnHover' : 'bg-gray-400 cursor-not-allowed'
                }`}
              >
                {isSubmitting ? 'Validando...' : 'Validar'}
              </button>
            </div>
          </div>
        )}

        {/* Action buttons */}
        <div className="flex justify-end space-x-3">
          {procedure.status === 'Pendiente' ? (
            <button
              onClick={handleStartProcedure}
              disabled={isSubmitting}
              className={`px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                !isSubmitting ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-400 cursor-not-allowed'
              }`}
            >
              {isSubmitting ? 'Procesando...' : 'Iniciar Trámite'}
            </button>
          ) : procedure.status === 'Esperando Documentos' ? (
            <button
              onClick={handleContinueProcess}
              disabled={isSubmitting || !documentsComplete}
              className={`px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                documentsComplete && !isSubmitting ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-400 cursor-not-allowed'
              }`}
            >
              {isSubmitting ? 'Procesando...' : 'Continuar'}
            </button>
          ) : procedure.status === 'En Proceso' && documentsUploaded && tokenValidated ? (
            <button
              onClick={handleFinishProcedure}
              disabled={isSubmitting}
              className={`px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                !isSubmitting ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-400 cursor-not-allowed'
              }`}
            >
              {isSubmitting ? 'Procesando...' : 'Finalizar'}
            </button>
          ) : (
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cerrar
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
