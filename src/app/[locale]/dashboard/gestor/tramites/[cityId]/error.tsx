'use client'; // Error boundaries must be Client Components

import { useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Button } from '@chakra-ui/react';

export default function TramitesError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const { cityId } = useParams();
  
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Tramites page error:', error);
  }, [error]);

  return (
    <div className="p-4">
      <h1 className="mb-4 text-2xl font-bold">Error al cargar trámites en {cityId}</h1>
      <div className="p-4 mb-4 text-red-500 border border-red-200 rounded-lg bg-red-50">
        Ocurrió un error al cargar los trámites. Por favor, intente nuevamente.
      </div>
      <Button
        colorScheme="blue"
        onClick={() => reset()}
      >
        Intentar nuevamente
      </Button>
    </div>
  );
}