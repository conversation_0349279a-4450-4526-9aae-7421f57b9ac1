'use client';

import { useCurrentUser } from "@/Providers/CurrentUserProvider";

export default function GestorDashboard() {
  const { user } = useCurrentUser();

  // Determinar el rol a mostrar
  const displayRole = user.userType === 'company-gestor' ? 'Compañía y Gestor' : 'Gestor';

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Gestor Dashboard</h1>
      <p>Welcome, {user.name}!</p>
      <p>Your role: {displayRole}</p>
    </div>
  );
}