'use client';

import { useEffect } from "react";
import { useCurrentUser } from "@/Providers/CurrentUserProvider";
import { useRouter } from "next/navigation";

export default function GestorUserLayout({ children }: { children: React.ReactNode }) {
  const { user } = useCurrentUser();
  const router = useRouter();

  useEffect(() => {
    // Verificar si el usuario está autenticado y es de tipo gestor o company-gestor
    if (!user || (user.userType !== 'gestor' && user.userType !== 'company-gestor' && user.userType !== 'superAdmin')) {
      router.push('/dashboard/main');
    }
  }, [user, router]);

  // Si no hay usuario o no es gestor o company-gestor, mostrar un estado de carga
  if (!user || (user.userType !== 'gestor' && user.userType !== 'company-gestor' && user.userType !== 'superAdmin')) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-gray-600">Verificando acceso...</p>
        </div>
      </div>
    );
  }

  // Si el usuario es gestor, mostrar el contenido
  return <>{children}</>;
}