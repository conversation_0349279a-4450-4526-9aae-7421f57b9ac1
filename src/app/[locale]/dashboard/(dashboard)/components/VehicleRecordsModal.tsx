'use client';
import { WeeklyRecord } from '@/actions/getWeeklyRecords';
import ModalContainer from '../../flotilla/components/Modals/ModalContainer';
import { useRecordsModal } from '../stores/useRecordsModal';
import { useMemo, useState } from 'react';
// import { Table, TableContainer, Th, Thead, Tr, Tbody } from '@chakra-ui/react';

interface VehicleRecordsModalProps {
  data: WeeklyRecord['stockVehicles'];
  filter?: string;
  filterName?: string;
}

export default function VehicleRecordsModal({ data, filter, filterName }: VehicleRecordsModalProps) {
  const vehicles = data.filter((vehicle) => vehicle.status.toLowerCase() === filter);

  const [search, setSearch] = useState('');

  const vehiclesMemo = useMemo(() => {
    if (search) {
      return vehicles.filter((vehicle) => vehicle.carNumber.toLowerCase().includes(search.toLowerCase()));
    }
    return vehicles;
  }, [search, vehicles]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
  };

  const recordsModal = useRecordsModal();

  return (
    <ModalContainer
      width="w-[800px]"
      className="relative min-h-[500px] h-[500px] max-h-[500px] "
      title={`Registros de vehículos ${filterName || filter} `}
      onClose={recordsModal.onClose}
    >
      <>
        <div className="absolute top-[20px] right-[50px]">
          <input
            type="text"
            placeholder="Buscar.."
            className="h-[30px] border-[1px] border-gray-500 rounded px-3 text-[14px] "
            onChange={handleSearch}
          />
        </div>

        <div className="flex w-full h-full overflow-y-auto flex-col gap-1 ">
          <p>Total de vehiculos: {vehiclesMemo.length} </p>
          {vehiclesMemo.map((vehicle, index) => (
            <div key={index} className="ml-2 flex flex-row gap-4">
              <p>{vehicle.carNumber}</p>
            </div>
          ))}
          {/* <TableContainer>
            <Table variant="striped" colorScheme="gray">
              <Thead>
                <Tr>
                  <Th px="4px" pl="68px">
                    Contrato
                  </Th>
                </Tr>
              </Thead>
              <Tbody>
                {vehiclesMemo.map((vehicle, index) => (
                  <Tr key={index}>
                    <Th px="4px" pl="68px">
                      {vehicle.carNumber}
                    </Th>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </TableContainer> */}
        </div>
      </>
    </ModalContainer>
  );
}
