'use client';
import { FaCarSide } from "react-icons/fa";
import { TbReportAnalytics } from "react-icons/tb";
import { CustomIcon } from "./Icon";
import { FaFileInvoice } from "react-icons/fa";

export const FleetSection = () => {
    const sections = [
      {
        title: "Fleet List",
        description: "List of suppliers for all medical facilities",
        icon: FaCarSide,
      },
      {
        title: "Reports / Analytics",
        description: "Data showing maintenance trends",
        icon: TbReportAnalytics,
      },
      {
        title: "All Invoices",
        description: "Coming soon",
        icon: FaFileInvoice,
      },
    ];
  
    return (
      <div className=" bg-white rounded shadow px-6 py-6 flex flex-col relative ">
        <h2 className=" text-xl font-semibold  text-primaryPrussianBlue ">
          {"Fleet"}
        </h2>
        <div className="flex gap-10 py-6 ">
          {sections.map((section) => {
            return (
              <FleetSectionCard
                key={section.title}
                title={section.title}
                description={section.description}
                Icon={section.icon}
              />
            );
          })}
        </div>
      </div>
    );
  };
  
  const FleetSectionCard = (props: any) => {
    const { title, description, Icon } = props;
  
    return (
      <div className="w-72 flex gap-1">
        <div className="w-1/4">
          <CustomIcon icon={Icon} />
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-primaryRoyalBlue">
            {title}
          </h3>
          <p className="text-xs text-primaryBlueGray">{description}</p>
        </div>
      </div>
    );
  };