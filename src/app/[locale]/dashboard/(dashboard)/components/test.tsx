'use client';

import { useChangeLocale, useI18n } from "@/i18n/client";
import { useRouter } from "next/navigation";

export const TestComp = () => {
    const changeLocale = useChangeLocale();
    const t = useI18n();

    const router = useRouter();

    return <div>
      

        <button onClick={()=>{
            changeLocale('es');

            router.refresh();

        }} >{t('navbar')}</button>
    </div>
}