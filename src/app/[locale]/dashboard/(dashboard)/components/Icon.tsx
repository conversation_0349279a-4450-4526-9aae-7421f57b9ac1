"use client";

import { Flex, FlexProps, Icon } from "@chakra-ui/react";
import { BiCar, BiCalendar, BiUser } from "react-icons/bi";
import { Avatar, Circle } from "@chakra-ui/react";
import { IconType } from "react-icons";

interface ICustomIcon {
  icon: IconType;
}

export const CustomIcon = ({ icon }: ICustomIcon) => {
  return (
    <div className="bg-primaryAliceBlue h-14 w-14 py-2 rounded-full  flex items-center justify-center">
      <Icon
        fontSize="40"
        _groupHover={{
          color: "white",
        }}
        as={icon}
      />
    </div>
  );
};
