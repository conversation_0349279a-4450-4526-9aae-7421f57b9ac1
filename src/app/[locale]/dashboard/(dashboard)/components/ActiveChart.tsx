import { VehicleCard } from '@/actions/getAllVehicles';
import BarChart from '@/components/Charts/BarChart';
import { cities2 } from '@/constants';

interface ActivesChartProps {
  activesVehicles: VehicleCard[];
}

export default function ActivesChart({ activesVehicles }: ActivesChartProps) {
  const groupByCategory = activesVehicles.reduce(
    (group, vehicle) => {
      const { vehicleState } = vehicle;
      const city = cities2[vehicleState]?.label || 'CDMX';
      group[city] = group[city] ?? [];
      group[city].push(vehicle);
      return group;
    },
    {} as {
      [key: string]: VehicleCard[];
    }
  );

  const regionKeyLabels = Object.keys(groupByCategory).map((key) => key);

  const activesPerRegion = Object.keys(groupByCategory).map((key) => groupByCategory[key].length);
  const chartData = {
    label: `Suma total: ${activesPerRegion.reduce((acc, curr) => acc + curr, 0)} `,
    labels: regionKeyLabels,
    indicators: activesPerRegion,
  };

  return (
    <div
      className=" my-[50px] bg-white px-3 pt-[25px] pb-[50px] rounded shadow flex flex-col gap-1 "
      style={{ height: '450px', width: '100%' }}
    >
      <p className="pl-[15px] text-[24px] text-textGray2 font-[600] ">Activos</p>
      <BarChart data={chartData} barThickness={50} />
    </div>
  );
}
