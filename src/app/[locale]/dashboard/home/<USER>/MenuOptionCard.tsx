'use client';

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { IconType } from "react-icons";
import { FiArrowRight } from "react-icons/fi";
import { useParams, useRouter } from "next/navigation";
import { useMemo, useCallback } from "react";
import { useI18n } from "@/i18n/client";

interface MenuOptionCardProps {
  title: string;
  description: string;
  icon: IconType;
  link: string;
  color?: string;
}

export default function MenuOptionCard({
  title,
  description,
  icon: Icon,
  link,
  color = "bg-primary-gradient"
}: MenuOptionCardProps) {
  // Get the current locale from the URL and router for navigation
  const params = useParams();
  const router = useRouter();
  const locale = params.locale as string || 'es';
  const t = useI18n();

  // Ensure the link includes the locale - use useMemo to avoid recalculation on every render
  const fullLink = useMemo(() => {
    return link.startsWith('/')
      ? `/${locale}${link}`
      : `/${locale}/${link}`;
  }, [locale, link]);

  // <PERSON>le click to navigate - use useCallback to avoid recreation on every render
  const handleClick = useCallback(() => {
    console.log("Navigating to:", fullLink);

    // For all links, use the normal fullLink
    // The route groups are not part of the URL, they're just for organization
    router.push(fullLink);
  }, [router, fullLink]);

  // Determine text and hover colors based on the card's background color
  const getTextAndHoverColors = () => {
    if (color.includes('green')) {
      return {
        textColor: 'text-green-600',
        hoverTextColor: 'group-hover:text-green-800',
        hoverBorderColor: 'hover:border-green-300'
      };
    } else if (color.includes('blue')) {
      return {
        textColor: 'text-blue-600',
        hoverTextColor: 'group-hover:text-blue-800',
        hoverBorderColor: 'hover:border-blue-300'
      };
    } else if (color.includes('purple')) {
      return {
        textColor: 'text-purple-600',
        hoverTextColor: 'group-hover:text-purple-800',
        hoverBorderColor: 'hover:border-purple-300'
      };
    } else if (color.includes('red')) {
      return {
        textColor: 'text-red-600',
        hoverTextColor: 'group-hover:text-red-800',
        hoverBorderColor: 'hover:border-red-300'
      };
    } else {
      return {
        textColor: 'text-blue-600',
        hoverTextColor: 'group-hover:text-blue-800',
        hoverBorderColor: 'hover:border-blue-300'
      };
    }
  };

  const { textColor, hoverTextColor, hoverBorderColor } = getTextAndHoverColors();

  return (
    <div onClick={handleClick} className="w-full h-full">
      <Card className={`cursor-pointer hover:shadow-lg transition-all duration-300 h-full border-gray-200 ${hoverBorderColor} transform hover:-translate-y-1 group`}>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-3 mb-3">
            <div className={`${color} p-3 rounded-full text-white`}>
              <Icon size={24} />
            </div>
            <CardTitle className="text-xl">{t(title as keyof typeof t) || title}</CardTitle>
          </div>
          <CardDescription className="text-gray-600">{t(description as keyof typeof t) || description}</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-between items-center">
          <div className={`flex items-center text-sm ${textColor} ${hoverTextColor} font-medium`}>
            <span>{t('Access')}</span>
            <FiArrowRight className="ml-1 transition-transform duration-300 group-hover:translate-x-1" />
          </div>
          <div className="text-xs text-gray-400">{t('ClickToAccess')}</div>
        </CardContent>
      </Card>
    </div>
  );
}
