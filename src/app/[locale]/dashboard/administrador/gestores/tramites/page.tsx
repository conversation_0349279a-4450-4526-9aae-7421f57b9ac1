'use client';

import { useState, useEffect } from 'react';
import { Box, useToast, SimpleGrid, Text, Flex, Spinner, Heading } from '@chakra-ui/react';
import { Plus } from 'lucide-react';
import axios from 'axios';
import { URL_API } from '@/constants';
import { useCurrentUser } from '@/Providers/CurrentUserProvider';
import CreateTramiteModal from './CreateTramiteModal';

interface Tramite {
  _id: string;
  name: string;
  state: string;
  documents: Array<{ name: string; format: string }>;
  cost: number;
  duration: number;
  description: string;
  createdAt: string;
  updatedAt: string;
}

export default function AdminTramitesPage() {
  const [tramites, setTramites] = useState<Tramite[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { user } = useCurrentUser();
  const toast = useToast();

  const fetchTramites = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!user?.accessToken) {
        setError('No hay sesión de usuario activa');
        setLoading(false);
        return;
      }

      const response = await axios.get(`${URL_API}/vendor-platform/gestores/tramites/all`, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      });

      if (response.data && Array.isArray(response.data.data)) {
        setTramites(response.data.data);
      } else {
        setError('Formato de respuesta inválido');
      }
    } catch (err) {
      console.error('Error fetching tramites:', err);
      setError('Error al cargar los trámites');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?.accessToken) {
      fetchTramites();
    }
  }, [user?.accessToken]);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleTramiteCreated = () => {
    fetchTramites();
    setIsModalOpen(false);
    toast({
      title: 'Trámite creado',
      description: 'El trámite ha sido creado exitosamente',
      status: 'success',
      duration: 5000,
      isClosable: true,
    });
  };

  return (
    <Box p={4}>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Administración de Trámites</h1>
        <button
          onClick={handleOpenModal}
          className="flex items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <Plus className="h-4 w-4 mr-2" />
          Crear Trámite
        </button>
      </div>

      {loading ? (
        <Flex justifyContent="center" alignItems="center" height="200px">
          <Spinner size="xl" />
        </Flex>
      ) : error ? (
        <Box p={4} bg="red.50" color="red.500" borderRadius="md" mb={4}>
          {error}
        </Box>
      ) : tramites.length === 0 ? (
        <Box p={4} bg="gray.50" borderRadius="md" textAlign="center">
          <Text>No hay trámites disponibles. Crea uno nuevo con el botón "Crear Trámite".</Text>
        </Box>
      ) : (
        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
          {tramites.map((tramite) => (
            <Box
              key={tramite._id}
              p={6}
              borderWidth="1px"
              borderRadius="lg"
              _hover={{ shadow: 'md' }}
            >
              <Heading size="md" mb={2}>{tramite.name}</Heading>
              <Text color="gray.600" mb={2}>Estado: {tramite.state}</Text>
              <Text color="gray.600" mb={2}>Costo: ${tramite.cost}</Text>
              <Text color="gray.600" mb={2}>Duración: {tramite.duration} días</Text>
              <Text color="gray.500" noOfLines={2} mb={2}>{tramite.description}</Text>
              <Text color="gray.500" fontSize="sm">
                Documentos requeridos: {tramite.documents.length}
              </Text>
            </Box>
          ))}
        </SimpleGrid>
      )}

      {isModalOpen && (
        <CreateTramiteModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          onTramiteCreated={handleTramiteCreated}
        />
      )}
    </Box>
  );
}
