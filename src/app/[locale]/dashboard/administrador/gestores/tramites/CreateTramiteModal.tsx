'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dalContent,
  <PERSON>dal<PERSON><PERSON>er,
  <PERSON>dal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  FormErrorMessage,
  VStack,
  HStack,
  Box,
  Text,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Select,
  useToast,
  Checkbox,
  SimpleGrid,
  Spinner,
} from '@chakra-ui/react';
import { useForm, Controller, useFieldArray } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';
import { X, Plus } from 'lucide-react';
import axios from 'axios';
import { URL_API } from '@/constants';
import { useCurrentUser } from '@/Providers/CurrentUserProvider';
import { CONTRACT_REGIONS, DOCUMENTS } from '@/utils/gestores';

// Define interfaces for Gestor data
interface Gestor {
  _id: string;
  name: string;
  phone: string;
  email: string;
  tramites: string[];
  createdAt: string;
  updatedAt: string;
  __v: number;
}

interface CreateTramiteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onTramiteCreated: () => void;
}

// Define the validation schema
const tramiteSchema = Yup.object().shape({
  name: Yup.string().required('El nombre es requerido'),
  state: Yup.string().required('El estado es requerido'),
  description: Yup.string().required('La descripción es requerida'),
  cost: Yup.number()
    .typeError('El costo debe ser un número')
    .required('El costo es requerido')
    .min(0, 'El costo no puede ser negativo'),
  duration: Yup.number()
    .typeError('La duración debe ser un número')
    .required('La duración es requerida')
    .min(1, 'La duración debe ser al menos 1 día'),
  gestorId: Yup.string().required('El gestor es requerido'),
  documents: Yup.array().of(
    Yup.object().shape({
      name: Yup.string().required('El nombre del documento es requerido'),
      format: Yup.string().required('El formato del documento es requerido'),
      isAdminUpload: Yup.boolean().notRequired(),
    })
  ),
});

// Define the form values type
interface TramiteFormValues {
  name: string;
  state: string;
  description: string;
  cost: number;
  duration: number;
  gestorId: string;
  documents: Array<{
    name: string;
    format: string;
    isAdminUpload?: boolean;
  }>
}

export default function CreateTramiteModal({
  isOpen,
  onClose,
  onTramiteCreated,
}: CreateTramiteModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showCustomDocumentForm, setShowCustomDocumentForm] = useState(false);
  const [selectedPredefinedDocuments, setSelectedPredefinedDocuments] = useState<string[]>([]);
  const [customDocName, setCustomDocName] = useState('');
  const [customDocFormat, setCustomDocFormat] = useState('');
  const [gestores, setGestores] = useState<Gestor[]>([]);
  const [loadingGestores, setLoadingGestores] = useState(false);
  const [gestorError, setGestorError] = useState<string | null>(null);
  const { user } = useCurrentUser();
  const toast = useToast();

  // Fetch gestores when the modal opens
  useEffect(() => {
    if (isOpen && user?.accessToken) {
      fetchGestores();
    }
  }, [isOpen, user?.accessToken]);

  // Function to fetch gestores
  const fetchGestores = async () => {
    try {
      setLoadingGestores(true);
      setGestorError(null);

      const response = await axios.get(`${URL_API}/vendor-platform/gestores`, {
        headers: {
          Authorization: `Bearer ${user?.accessToken}`,
        },
      });

      if (response.data && Array.isArray(response.data.data)) {
        setGestores(response.data.data);
      } else {
        setGestorError('Formato de respuesta inválido');
      }
    } catch (error) {
      console.error('Error fetching gestores:', error);
      setGestorError('Error al cargar los gestores');
    } finally {
      setLoadingGestores(false);
    }
  };

  // Initialize the form
  const {
    register,
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm<TramiteFormValues>({
    resolver: yupResolver(tramiteSchema) as any,
    defaultValues: {
      name: '',
      state: '',
      description: '',
      cost: 0,
      duration: 1,
      gestorId: '',
      documents: [],
    },
  });

  // Setup field array for documents
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'documents',
  });

  // Handle form submission
  const onSubmit = async (data: TramiteFormValues) => {
    try {
      setIsSubmitting(true);

      if (!user?.accessToken) {
        toast({
          title: 'Error',
          description: 'No hay sesión de usuario activa',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
        return;
      }

      // Make API request to create trámite
      await axios.post(
        `${URL_API}/vendor-platform/gestores/tramites`,
        data,
        {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      // Reset form and close modal
      reset();
      onTramiteCreated();
    } catch (error) {
      console.error('Error creating tramite:', error);
      toast({
        title: 'Error',
        description: 'No se pudo crear el trámite. Intente nuevamente.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle document selection
  const handleDocumentSelection = (documentName: string, documentType: string, isChecked: boolean) => {
    if (isChecked) {
      // Add document to selected list
      setSelectedPredefinedDocuments(prev => [...prev, documentName]);

      // Add document to form
      append({ name: documentName, format: documentType });
    } else {
      // Remove document from selected list
      setSelectedPredefinedDocuments(prev => prev.filter(name => name !== documentName));

      // Remove document from form
      const index = fields.findIndex(field => field.name === documentName);
      if (index !== -1) {
        remove(index);
      }
    }
  };

  // Toggle custom document form
  const toggleCustomDocumentForm = () => {
    setShowCustomDocumentForm(prev => !prev);
    setCustomDocName('');
    setCustomDocFormat('');
  };

  // Add custom document
  const addCustomDocument = () => {
    if (customDocName && customDocFormat) {
      append({ name: customDocName, format: customDocFormat, isAdminUpload: true });
      setCustomDocName('');
      setCustomDocFormat('');
    }
  };

  // Handle modal close
  const handleClose = () => {
    reset();
    setSelectedPredefinedDocuments([]);
    setShowCustomDocumentForm(false);
    setCustomDocName('');
    setCustomDocFormat('');
    setGestorError(null);
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="xl" scrollBehavior="inside">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Crear Nuevo Trámite</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <form id="create-tramite-form" onSubmit={handleSubmit(onSubmit)}>
            <VStack spacing={4} align="stretch">
              <FormControl isInvalid={!!errors.name}>
                <FormLabel htmlFor="name">Nombre del Trámite</FormLabel>
                <Input id="name" {...register('name')} />
                <FormErrorMessage>{errors.name?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.state}>
                <FormLabel htmlFor="state">Estado</FormLabel>
                <Select id="state" placeholder="Seleccione un estado" {...register('state')}>
                  {CONTRACT_REGIONS.map((region) => (
                    <option key={region.value} value={region.value}>
                      {region.label}
                    </option>
                  ))}
                </Select>
                <FormErrorMessage>{errors.state?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.description}>
                <FormLabel htmlFor="description">Descripción</FormLabel>
                <Textarea id="description" {...register('description')} />
                <FormErrorMessage>{errors.description?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.gestorId}>
                <FormLabel htmlFor="gestorId">Gestor</FormLabel>
                {loadingGestores ? (
                  <HStack spacing={2}>
                    <Spinner size="sm" />
                    <Text fontSize="sm">Cargando gestores...</Text>
                  </HStack>
                ) : gestorError ? (
                  <Text color="red.500" fontSize="sm">{gestorError}</Text>
                ) : (
                  <Select
                    id="gestorId"
                    placeholder="Seleccione un gestor"
                    {...register('gestorId')}
                  >
                    {gestores.map((gestor) => (
                      <option key={gestor._id} value={gestor._id}>
                        {gestor.name} ({gestor.email})
                      </option>
                    ))}
                  </Select>
                )}
                <FormErrorMessage>{errors.gestorId?.message}</FormErrorMessage>
              </FormControl>

              <HStack spacing={4}>
                <FormControl isInvalid={!!errors.cost}>
                  <FormLabel htmlFor="cost">Costo (MXN)</FormLabel>
                  <Controller
                    name="cost"
                    control={control}
                    render={({ field }) => (
                      <NumberInput
                        id="cost"
                        min={0}
                        onChange={(valueString) => field.onChange(parseFloat(valueString))}
                        value={field.value}
                      >
                        <NumberInputField />
                        <NumberInputStepper>
                          <NumberIncrementStepper />
                          <NumberDecrementStepper />
                        </NumberInputStepper>
                      </NumberInput>
                    )}
                  />
                  <FormErrorMessage>{errors.cost?.message}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.duration}>
                  <FormLabel htmlFor="duration">Duración (días)</FormLabel>
                  <Controller
                    name="duration"
                    control={control}
                    render={({ field }) => (
                      <NumberInput
                        id="duration"
                        min={1}
                        onChange={(valueString) => field.onChange(parseInt(valueString))}
                        value={field.value}
                      >
                        <NumberInputField />
                        <NumberInputStepper>
                          <NumberIncrementStepper />
                          <NumberDecrementStepper />
                        </NumberInputStepper>
                      </NumberInput>
                    )}
                  />
                  <FormErrorMessage>{errors.duration?.message}</FormErrorMessage>
                </FormControl>
              </HStack>

              <Box>
                <FormLabel>Documentos Requeridos</FormLabel>

                <div className="flex space-x-4 mb-4">
                  <button
                    type="button"
                    className={`flex-1 px-3 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                      !showCustomDocumentForm
                        ? "text-white bg-blue-600 hover:bg-blue-700"
                        : "text-gray-700 bg-gray-100 hover:bg-gray-200"
                    }`}
                    onClick={() => setShowCustomDocumentForm(false)}
                  >
                    Documentos Predefinidos
                  </button>
                  <button
                    type="button"
                    className={`flex-1 px-3 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                      showCustomDocumentForm
                        ? "text-white bg-blue-600 hover:bg-blue-700"
                        : "text-gray-700 bg-gray-100 hover:bg-gray-200"
                    }`}
                    onClick={() => toggleCustomDocumentForm()}
                  >
                    Documentos Personalizados
                  </button>
                </div>

                {!showCustomDocumentForm ? (
                  // Vista de documentos predefinidos
                  <Box>
                    <SimpleGrid columns={2} spacing={2} maxH="200px" overflowY="auto" p={2} borderWidth="1px" borderRadius="md">
                      {DOCUMENTS.map((doc) => (
                        <Checkbox
                          key={doc.name}
                          isChecked={selectedPredefinedDocuments.includes(doc.name)}
                          onChange={(e) => handleDocumentSelection(doc.name, doc.type, e.target.checked)}
                          size="sm"
                        >
                          {doc.name}
                        </Checkbox>
                      ))}
                    </SimpleGrid>
                  </Box>
                ) : (
                  // Vista de documentos personalizados
                  <Box>
                    <HStack mb={2}>
                      <Input
                        placeholder="Nombre del documento"
                        size="sm"
                        value={customDocName}
                        onChange={(e) => setCustomDocName(e.target.value)}
                        flex="3"
                      />
                      <Select
                        placeholder="Formato"
                        size="sm"
                        value={customDocFormat}
                        onChange={(e) => setCustomDocFormat(e.target.value)}
                        flex="2"
                      >
                        <option value="digital">Digital</option>
                        <option value="físico">Fisico</option>
                      </Select>
                      <button
                        type="button"
                        className="px-2 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        onClick={addCustomDocument}
                        disabled={!customDocName || !customDocFormat}
                      >
                        <Plus size={16} />
                      </button>
                    </HStack>
                  </Box>
                )}

                {/* Lista de documentos seleccionados */}
                {fields.length > 0 && fields.some(field => field.name && field.format) && (
                  <Box mt={4} maxH="150px" overflowY="auto">
                    <Text fontWeight="medium" mb={2} fontSize="sm">
                      Documentos seleccionados ({fields.filter(f => f.name && f.format).length})
                    </Text>
                    <VStack align="stretch" spacing={1}>
                      {fields.filter(f => f.name && f.format).map((field, index) => (
                        <HStack key={index} justify="space-between" p={1} borderWidth="1px" borderRadius="md" fontSize="sm">
                          <Text noOfLines={1} flex="1">
                            {field.name} ({field.format})
                          </Text>
                          <button
                            type="button"
                            aria-label="Eliminar documento"
                            className="p-1 text-red-600 hover:bg-red-50 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                            onClick={() => {
                              // Si es un documento predefinido, actualizar la lista de seleccionados
                              if (selectedPredefinedDocuments.includes(field.name)) {
                                setSelectedPredefinedDocuments(prev =>
                                  prev.filter(name => name !== field.name)
                                );
                              }
                              // Encontrar el índice real en el array de fields
                              const realIndex = fields.findIndex(f => f.id === field.id);
                              if (realIndex !== -1) {
                                remove(realIndex);
                              }
                            }}
                          >
                            <X size={14} />
                          </button>
                        </HStack>
                      ))}
                    </VStack>
                  </Box>
                )}
              </Box>
            </VStack>
          </form>
        </ModalBody>

        <ModalFooter>
          <button
            type="button"
            className="mr-3 px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            onClick={handleClose}
          >
            Cancelar
          </button>
          <button
            className="flex items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={isSubmitting}
            type="submit"
            form="create-tramite-form"
          >
            {isSubmitting ? 'Creando...' : 'Crear Trámite'}
          </button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
