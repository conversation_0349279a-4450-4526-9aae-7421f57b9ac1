import getUserById from "@/actions/getUserById";
import { redirect } from "next/navigation";



export default async function CompanyUserLayout({ children }: { children: React.ReactNode }) {

  // Check if user type is company, company-gestor or superAdmin, if not, redirect to dashboard

  const user = await getUserById();

  if (!user || (user.userType !== 'company' && user.userType !== 'company-gestor' && user.userType !== 'superAdmin')) return redirect('/dashboard');

  return (
    <>
      {children}
    </>
  )
}