'use client'

import { usePathname, useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"

export function Pagination({
  currentPage,
  totalPages,
  search,
  status
}: {
  currentPage: number
  totalPages: number
  search?: string
  status?: string
}) {
  const router = useRouter()
  const pathname = usePathname()

  const createPageURL = (pageNumber: number) => {
    const params = new URLSearchParams()

    if (search) params.set('search', search)
    if (status) params.set('status', status)
    params.set('page', pageNumber.toString())

    return `${pathname}?${params.toString()}`
  }

  // No mostrar paginación si solo hay una página
  if (totalPages <= 1) {
    return null
  }

  return (
    <div className="flex items-center justify-center space-x-2 mt-6">
      <Button
        variant="outline"
        size="sm"
        onClick={() => router.push(createPageURL(currentPage - 1))}
        disabled={currentPage <= 1}
      >
        <ChevronLeft className="h-4 w-4" />
        <span className="sr-only">Página anterior</span>
      </Button>

      <div className="flex items-center space-x-2">
        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
          // Mostrar páginas alrededor de la página actual
          let pageNumber: number

          if (totalPages <= 5) {
            // Si hay 5 o menos páginas, mostrar todas
            pageNumber = i + 1
          } else if (currentPage <= 3) {
            // Si estamos en las primeras páginas
            pageNumber = i + 1
          } else if (currentPage >= totalPages - 2) {
            // Si estamos en las últimas páginas
            pageNumber = totalPages - 4 + i
          } else {
            // Si estamos en el medio
            pageNumber = currentPage - 2 + i
          }

          return (
            <Button
              key={pageNumber}
              variant={currentPage === pageNumber ? "default" : "outline"}
              size="sm"
              onClick={() => router.push(createPageURL(pageNumber))}
            >
              {pageNumber}
            </Button>
          )
        })}
      </div>

      <Button
        variant="outline"
        size="sm"
        onClick={() => router.push(createPageURL(currentPage + 1))}
        disabled={currentPage >= totalPages}
      >
        <ChevronRight className="h-4 w-4" />
        <span className="sr-only">Página siguiente</span>
      </Button>
    </div>
  )
}
