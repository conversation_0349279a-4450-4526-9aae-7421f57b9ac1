import { Suspense } from "react"
import { getInstallationAppointments } from "@/lib/installation-actions"
import { InstallationList } from "./_components/installation-list"
import { SearchForm } from "./_components/search-form"

export default async function InstallationListPage({
  params,
  searchParams,
}: {
  params: { locale: string }
  searchParams: { [key: string]: string | string[] | undefined }
}) {
  // Extraer parámetros de búsqueda
  const page = typeof searchParams.page === 'string' ? parseInt(searchParams.page) : 1
  const search = typeof searchParams.search === 'string' ? searchParams.search : undefined
  const statusParam = typeof searchParams.status === 'string' ? searchParams.status : undefined

  // Si el estado es "all" o no está definido, no aplicamos filtro
  const status = statusParam === 'all' ? undefined : statusParam

  // Obtener datos paginados
  const appointmentsData = await getInstallationAppointments(page, 10, search, status)

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Listado de Instalaciones</h1>
      </div>

      <SearchForm initialSearch={search} initialStatus={statusParam || ''} />

      <Suspense fallback={<div>Cargando instalaciones...</div>}>
        <InstallationList
          appointments={appointmentsData}
          currentPage={page}
          search={search}
          status={statusParam}
          locale={params.locale}
        />
      </Suspense>
    </div>
  )
}
