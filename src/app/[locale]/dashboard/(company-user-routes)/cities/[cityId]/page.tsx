import { notFound } from "next/navigation"
import { Suspense } from "react"
// import { getCityById, getCrewsByCity } from "@/lib/city-actions"
import { getCityById, getCrewsByCity } from "@/lib/city-actions copy"
import { CityDetail } from "@/app/_components/city/city-detail"
import { CrewList } from "@/app/_components/crews/crew-list"
import { CreateCrewButton } from '@/app/_components/crews/create-crew-button';

interface CityDetailPageProps {
  params: {
    cityId: string,
    locale: string
  }
}

export default async function CityDetailPage({ params }: CityDetailPageProps) {
  const cityId = params.cityId

  if (!cityId) {
    return notFound()
  }

  const city = await getCityById(cityId);

  if (!city) {
    return notFound()
  }

  const crews = await getCrewsByCity(cityId);


  return (
    <div className="container mx-auto py-6">
      <CityDetail city={city} />

      <div className="mt-10">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold">Cuadrillas</h2>
          <CreateCrewButton cityId={cityId} />
        </div>

        <Suspense fallback={<div>Cargando cuadrillas...</div>}>
          <CrewList crews={crews} cityId={cityId} locale={params.locale} />
        </Suspense>
      </div>
    </div>
  )
}
