import { notFound } from "next/navigation"
import { getCityById, getCrewById, getNeighborhoodById } from "@/lib/city-actions copy"
import { NeighborhoodDetail } from "@/app/_components/neighborhood/neighborhood-detail"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { NeighborhoodSettings } from "@/app/_components/neighborhood/neighborhood-settings"

interface NeighborhoodDetailPageProps {
  params: {
    cityId: string
    crewId: string
    neighborhoodId: string
    locale: string
  }
}

export default async function NeighborhoodDetailPage({ params }: NeighborhoodDetailPageProps) {
  const { cityId, crewId, neighborhoodId } = params;

  if (!cityId || !crewId || !neighborhoodId) {
    return notFound()
  }
  const [city, crew, neighborhood] = await Promise.all([getCityById(cityId), getCrewById(crewId), getNeighborhoodById(neighborhoodId)])

  if (!city || !crew || !neighborhood || crew.cityId !== cityId || neighborhood.crewId !== crewId || neighborhood.cityId !== cityId) {
    return notFound()
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center mb-6">
        <Link href={`/${params.locale}/dashboard/cities/${city._id}/crews/${crew._id}`} passHref>
          <Button variant="ghost" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Volver a {crew.name}
          </Button>
        </Link>
      </div>
      <Tabs defaultValue="details">
        <TabsList>
          <TabsTrigger value="details">Detalles</TabsTrigger>
          <TabsTrigger value="settings">Ajustes de Disponibilidad</TabsTrigger>
        </TabsList>
        <TabsContent value="details">

          <NeighborhoodDetail neighborhood={neighborhood} crew={crew} city={city} />
        </TabsContent>
        <TabsContent value="settings">
          <NeighborhoodSettings scheduleConfig={neighborhood.scheduleConfig} />
        </TabsContent>
      </Tabs>
    </div>
  )
}

