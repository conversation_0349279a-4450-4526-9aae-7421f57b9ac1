import { notFound } from "next/navigation"
import { Suspense } from "react"
// import { getCityById, getCrewById, getNeighborhoodsByCrew } from "@/lib/city-actions"
import { getCityById, getCrewById, getNeighborhoodsByCrew } from "@/lib/city-actions copy"
import { CrewDetail } from "@/app/_components/crews/crew-detail"
import { NeighborhoodList } from "@/app/_components/neighborhood/neighborhood-list"
import { CreateNeighborhoodButton } from "@/app/_components/neighborhood/create-neighborhood"

interface CrewDetailPageProps {
  params: {
    cityId: string
    crewId: string
    locale: string
  }
}

export default async function CrewDetailPage({ params }: CrewDetailPageProps) {

  const { cityId, crewId } = params;

  if (!cityId || !crewId) {
    return notFound()
  }

  const [city, crew] = await Promise.all([getCityById(cityId), getCrewById(crewId)])
  if (!city || !crew || crew.cityId !== cityId) {
    return notFound()
  }

  const neighborhoods = await getNeighborhoodsByCrew(crewId)

  return (
    <div className="container mx-auto py-6">
      <CrewDetail crew={crew} city={city} />

      <div className="mt-10">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold">Colonias</h2>
          <CreateNeighborhoodButton cityId={cityId} crewId={crewId} />
        </div>

        <Suspense fallback={<div>Cargando colonias...</div>}>
          <NeighborhoodList neighborhoods={neighborhoods} cityId={cityId} crewId={crewId} locale={params.locale} />
        </Suspense>
      </div>
    </div>
  )
}

