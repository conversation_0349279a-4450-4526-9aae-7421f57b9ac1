import { Suspense } from "react"
import { getCities } from "@/lib/city-actions copy"
// import { getCities } from "@/lib/city-actions"
import { CityList } from "@/app/_components/city/city-list"
import { CreateCityButton } from "@/app/_components/city/create-city-button"
import type { City } from "@/constants/companyService"

export default async function CitiesPage({ params }: { params: { locale: string } }) {
  const cities = await getCities();

  if (!cities) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Ciudades</h1>
          <CreateCityButton />
        </div>
        <div className="text-center">
          <p>No se encontraron ciudades</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Ciudades</h1>
        <CreateCityButton />
      </div>

      <Suspense fallback={<div>Cargando ciudades...</div>}>
        <CityList cities={cities as any} locale={params.locale} />
      </Suspense>
    </div>
  )
}
