'use client';

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>Left, ChevronLeft, ChevronRight, Clock, LoaderCircle } from "lucide-react";
import { companyService } from "@/constants/companyService";
import { DateTime } from "luxon";
import { useToast } from "@chakra-ui/react";
import SelectInput from "@/components/Inputs/SelectInput";
import Spinner from "@/components/Loading/Spinner";
import FormikContainer from "@/components/Formik/FormikContainer";
import { useFormikContext } from 'formik';
import { useCurrentUser } from '@/Providers/CurrentUserProvider';

interface DayInfo {
  date: Date | null;
  disabled: boolean;
  isToday: boolean;
}

interface Neighborhood {
  _id: string;
  name: string;
}

interface ScheduleConfig {
  weeklySchedule: WeeklySchedule;
  installationDuration: number;
  timezone: string;
  maxSimultaneousInstallations: number;
}

interface NeighborhoodWithSchedule extends Neighborhood {
  scheduleConfig?: ScheduleConfig;
}

interface RescheduleInstallationProps {
  appointmentId: string;
  currentNeighborhoodId: string;
  onClose: () => void;
  onSuccess: () => void;
}

export default function RescheduleInstallation({
  appointmentId,
  currentNeighborhoodId,
  onClose,
  onSuccess
}: RescheduleInstallationProps) {
  const [step, setStep] = useState(1); // 1: Selección de colonia, 2: Calendario, 3: Horarios
  const [neighborhoods, setNeighborhoods] = useState<NeighborhoodWithSchedule[]>([]);
  const [selectedNeighborhood, setSelectedNeighborhood] = useState<{ label: string; value: string } | null>(null);
  const [selectedNeighborhoodData, setSelectedNeighborhoodData] = useState<NeighborhoodWithSchedule | null>(null);
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [availableSlots, setAvailableSlots] = useState<string[]>([]);
  const [selectedSlot, setSelectedSlot] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const toast = useToast();
  const { user } = useCurrentUser<'company'>();

  // Cargar las colonias disponibles
  useEffect(() => {
    const fetchNeighborhoods = async () => {
      try {
        // If user.companyId is not available, don't make the request
        if (!user?.companyId) {
          console.error("Company ID not available");
          setError("No se pudo obtener el ID de la compañía");
          setLoading(false);
          return;
        }

        setLoading(true);
        const response = await companyService.getAllNeighborhoods({
          params: {
            companyId: user.companyId
          }
        });
        const data = response.data as NeighborhoodWithSchedule[];
        setNeighborhoods(data);

        // Preseleccionar la colonia actual
        const currentNeighborhood = data.find(
          (n: NeighborhoodWithSchedule) => n._id === currentNeighborhoodId
        );

        if (currentNeighborhood) {
          setSelectedNeighborhood({
            label: currentNeighborhood.name,
            value: currentNeighborhood._id
          });
          setSelectedNeighborhoodData(currentNeighborhood);
        }
      } catch (error) {
        console.error("Error al cargar colonias:", error);
        setError("No se pudieron cargar las colonias disponibles");
        toast({
          title: "Error al cargar colonias",
          status: "error",
          duration: 3000,
          isClosable: true,
          position: "top",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchNeighborhoods();
  }, [currentNeighborhoodId, toast, user?.companyId]);

  // Actualizar selectedNeighborhoodData cuando cambia selectedNeighborhood
  useEffect(() => {
    if (selectedNeighborhood && neighborhoods.length > 0) {
      const neighborhoodData = neighborhoods.find(n => n._id === selectedNeighborhood.value);
      setSelectedNeighborhoodData(neighborhoodData || null);
    }
  }, [selectedNeighborhood, neighborhoods]);

  const DAYS = ["Dom", "Lun", "Mar", "Mié", "Jue", "Vie", "Sáb"];
  const MONTHS = [
    "Enero", "Febrero", "Marzo", "Abril", "Mayo", "Junio",
    "Julio", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre"
  ];

  const handlePreviousMonth = () => {
    setCurrentMonth(prevMonth => {
      const newMonth = new Date(prevMonth);
      newMonth.setMonth(newMonth.getMonth() - 1);
      return newMonth;
    });
  };

  const handleNextMonth = () => {
    setCurrentMonth(prevMonth => {
      const newMonth = new Date(prevMonth);
      newMonth.setMonth(newMonth.getMonth() + 1);
      return newMonth;
    });
  };

  const getDaysInMonth = (date: Date): DayInfo[] => {
    const days: DayInfo[] = [];
    const year = date.getFullYear();
    const month = date.getMonth();

    // Primer día del mes
    const firstDay = new Date(year, month, 1);
    // Último día del mes
    const lastDay = new Date(year, month + 1, 0);

    // Días del mes anterior para completar la primera semana
    const firstDayOfWeek = firstDay.getDay();
    for (let i = firstDayOfWeek; i > 0; i--) {
      days.push({
        date: null,
        disabled: true,
        isToday: false,
      });
    }

    // Días del mes actual
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Mapeo de índice de día a nombre de día en inglés (para la configuración)
    const dayMap: { [key: number]: keyof WeeklySchedule } = {
      0: 'sunday',
      1: 'monday',
      2: 'tuesday',
      3: 'wednesday',
      4: 'thursday',
      5: 'friday',
      6: 'saturday',
    };

    for (let i = 1; i <= lastDay.getDate(); i++) {
      const currentDate = new Date(year, month, i);
      const dayOfWeek = currentDate.getDay();
      const dayName = dayMap[dayOfWeek];

      // Verificar si el día está en el horario de la colonia
      const isDayAvailable = selectedNeighborhoodData?.scheduleConfig?.weeklySchedule[dayName] !== undefined;

      // Verificar si el día es hoy o futuro
      const isPastDay = currentDate < today;

      days.push({
        date: currentDate,
        disabled: isPastDay || !isDayAvailable,
        isToday: currentDate.toDateString() === today.toDateString(),
      });
    }

    return days;
  };

  const handleDateSelect = async (dayInfo: DayInfo) => {
    if (!dayInfo.date || dayInfo.disabled || !selectedNeighborhood) return;

    setSelectedDate(dayInfo.date);
    setStep(3);

    try {
      setLoading(true);
      setError(null);

      const dateStr = dayInfo.date.toISOString().split('T')[0];

      // Usar la URL correcta para obtener los slots disponibles
      const response = await companyService.getInstallationSlots(
        selectedNeighborhood.value,
        dateStr
      );

      setAvailableSlots(response.data);
    } catch (error: any) {
      console.error("Error al obtener horarios:", error);
      setError("No se pudieron obtener los horarios disponibles");
      toast({
        title: "Error al obtener horarios",
        status: "error",
        duration: 3000,
        isClosable: true,
        position: "top",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTimeSelect = (slot: string) => {
    setSelectedSlot(slot);
  };

  const handleReschedule = async () => {
    if (!selectedSlot || !selectedNeighborhood) {
      setError("Por favor selecciona un horario");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      await companyService.rescheduleInstallationAppointment(
        appointmentId,
        selectedSlot,
        selectedNeighborhood.value
      );

      toast({
        title: "Cita reagendada exitosamente",
        status: "success",
        duration: 3000,
        isClosable: true,
        position: "top",
      });

      onSuccess();
      onClose();
    } catch (error: any) {
      console.error("Error al reagendar:", error);
      setError("No se pudo reagendar la cita");
      toast({
        title: "Error al reagendar la cita",
        status: "error",
        duration: 3000,
        isClosable: true,
        position: "top",
      });
    } finally {
      setLoading(false);
    }
  };

  // Función para renderizar el paso 1: Selección de colonia
  const renderNeighborhoodSelection = () => {

    if (loading) {
      return <p>Cargando colonias...</p>;
    }

    return (

      <div className="space-y-4">

        <h2 className="text-xl font-bold text-center">Selecciona una colonia</h2>

        <div className="space-y-4">
          <SelectInput
            label="Colonia"
            name="neighborhood"
            options={neighborhoods.map((neighborhood) => ({
              label: neighborhood.name,
              value: neighborhood._id,
            }))}
            // onChange={(option) => setSelectedNeighborhood(option)}
            onChange={(option) => {
              setSelectedNeighborhood(option);
              setSelectedNeighborhoodData(neighborhoods.find((n) => n._id === option.value)!);
            }}
          />

          {error && <p className="text-red-500 text-sm">{error}</p>}

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button
              disabled={!selectedNeighborhood}
              onClick={() => setStep(2)}
            >
              Continuar
            </Button>
          </div>
        </div>
      </div>
    )
  };

  // Función para renderizar el paso 2: Calendario
  const renderCalendar = () => (
    <div className="space-y-4">
      {/* {loading && <Spinner />} */}

      <h2 className="text-xl font-bold text-center">Reagendar Instalación</h2>

      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={() => setStep(1)}>
          <ArrowLeft className="mr-2" /> Regresar
        </Button>

        <h3 className="text-lg font-medium">Selecciona una fecha</h3>
      </div>

      {selectedNeighborhoodData && (
        <div className="bg-blue-50 p-3 rounded-md mb-4">
          <p className="text-sm text-blue-700">
            <span className="font-medium">Días disponibles para {selectedNeighborhoodData.name}:</span>{' '}
            {Object.keys(selectedNeighborhoodData.scheduleConfig?.weeklySchedule || {}).length > 0 ? (
              Object.entries(selectedNeighborhoodData.scheduleConfig?.weeklySchedule || {}).map(([day, _]) => {
                const dayNames: Record<string, string> = {
                  monday: 'Lunes',
                  tuesday: 'Martes',
                  wednesday: 'Miércoles',
                  thursday: 'Jueves',
                  friday: 'Viernes',
                  saturday: 'Sábado',
                  sunday: 'Domingo'
                };
                return dayNames[day];
              }).join(', ')
            ) : (
              'No hay días configurados'
            )}
          </p>
        </div>
      )}

      <div className="flex items-center justify-between mb-4">
        <Button variant="outline" onClick={handlePreviousMonth}>
          <ChevronLeft className="h-4 w-4" />
        </Button>

        <h3 className="text-lg font-medium">
          {MONTHS[currentMonth.getMonth()]} {currentMonth.getFullYear()}
        </h3>

        <Button variant="outline" onClick={handleNextMonth}>
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      <div className="grid grid-cols-7 gap-1 text-center">
        {DAYS.map(day => (
          <div key={day} className="font-medium text-sm py-2">
            {day}
          </div>
        ))}
      </div>

      <div className="grid grid-cols-7 gap-1">
        {getDaysInMonth(currentMonth).map((day, index) => (
          <Button
            key={index}
            variant={selectedDate?.toDateString() === day.date?.toDateString() ? "default" : "ghost"}
            disabled={day.disabled}
            className={`h-12
                      ${day.isToday ? 'border-2 border-primary' : ''}
                      ${!day.date ? 'invisible' : ''}
                      ${day.disabled && day.date ? 'opacity-50 bg-gray-100' : ''}`}
            onClick={() => handleDateSelect(day)}
          >
            {day.date?.getDate()}
          </Button>
        ))}
      </div>

      {error && <p className="text-red-500 text-sm">{error}</p>}
    </div>
  );

  // Función para renderizar el paso 3: Selección de horario
  const renderTimeSlots = () => (
    <div className="space-y-4">


      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={() => {
          setStep(2);
          setSelectedSlot(null);
          setAvailableSlots([]);
        }}>
          <ArrowLeft className="mr-2" /> Regresar
        </Button>

        <h2 className="text-lg font-bold">Selecciona un horario</h2>
      </div>



      {selectedDate && (
        <p className="text-center">
          {selectedDate.toLocaleDateString('es-ES', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          })}
        </p>
      )}

      {availableSlots.length === 0 && !loading ? (
        <div className="text-center p-4">
          <p>No hay horarios disponibles para la fecha seleccionada.</p>
          <Button
            variant="outline"
            onClick={() => setStep(2)}
            className="mt-4"
          >
            Seleccionar otra fecha
          </Button>
        </div>
      ) : (
        <div className="grid gap-2 grid-cols-[repeat(auto-fit,minmax(120px,1fr))]">
          {availableSlots.map((slot) => {
            // Convertir el slot de la zona horaria del servidor a la zona horaria local
            const localTime = DateTime.fromISO(slot)
              .setZone('local') // Esto usa la zona horaria del navegador
              .toFormat('HH:mm'); // Formato 24 horas

            return (
              <Button
                key={slot}
                variant={selectedSlot === slot ? "default" : "outline"}
                className="p-4"
                onClick={() => handleTimeSelect(slot)}
              >
                <Clock className="w-4 h-4 mr-2" />
                {localTime}
              </Button>
            );
          })}
        </div>
      )}
      {loading && (
        <div className="flex justify-center">

          <LoaderCircle className="animate-spin h-4 w-4" />
        </div>
      )}

      {error && <p className="text-red-500 text-sm">{error}</p>}

      <div className="flex justify-end space-x-2 mt-4">
        <Button variant="outline" onClick={onClose}>
          Cancelar
        </Button>
        <Button
          disabled={!selectedSlot}
          onClick={handleReschedule}
        >
          Confirmar reagendamiento
        </Button>
      </div>
    </div>
  );

  // Valores iniciales para Formik
  const initialValues = {
    neighborhood: selectedNeighborhood,
    date: selectedDate,
    timeSlot: selectedSlot
  };

  // Renderizar el componente principal con FormikContainer
  return (
    <FormikContainer
      initialValues={initialValues}
      onSubmit={async () => {
        // La lógica de envío se maneja en handleReschedule
      }}
      onClose={onClose}
      hideFooter
    >
      <WorkshopsModifier
        selectedNeighborhood={selectedNeighborhood}
        currentNeighborhoodId={currentNeighborhoodId}
      />
      {step === 1 && renderNeighborhoodSelection()}
      {step === 2 && renderCalendar()}
      {step === 3 && renderTimeSlots()}
    </FormikContainer>
  );
}

// Create a function that gets the workshops as above, but access to formik form and modify the initialValues
// with the preselected workshop data and options
function WorkshopsModifier({
  selectedNeighborhood,
  currentNeighborhoodId,
}: {
  selectedNeighborhood: { label: string; value: string } | null;
  currentNeighborhoodId: string;
}) {

  const formik = useFormikContext();
  // console.log('values: ', formik.values);

  useEffect(() => {
    if (selectedNeighborhood) {
      formik.setFieldValue('neighborhood', selectedNeighborhood);
    }
  }, [selectedNeighborhood]);


  return null;
}


