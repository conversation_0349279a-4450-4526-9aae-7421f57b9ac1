'use client';

import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { AppointmentStatus, InstallationStatus } from "@/types/global-export";
import InputMultipleFiles from "@/components/Inputs/InputMultipleFiles";
import { Button } from "@/components/ui/button";
import CustomInput from "@/components/Inputs/CustomInput";
import { useEffect, useMemo, useState } from 'react';
import { companyService } from '@/constants/companyService';
import { useToast } from '@chakra-ui/react';
import Spinner from '@/components/Loading/Spinner';
import * as Yup from 'yup';
import FormikContainer from '@/components/Formik/FormikContainer';
import { fileListValidator } from '@/validatorSchemas/filesValidators';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { DateTime } from 'luxon';
import Swal from 'sweetalert2';
import { CalendarClock } from "lucide-react";
import RescheduleInstallation from './RescheduleInstallation';
import { useCurrentUser } from '@/Providers/CurrentUserProvider';
import { statusStyleMap } from '../client-page';
export interface InstallationEventModalProps {
  isOpen: boolean;
  onClose: () => void;
  event: {
    _id: string;
    index: number;
    title: string;
    start: Date;
    end: Date;
    status: keyof typeof InstallationStatus;
    appointment: {
      arrivedAt?: string;
      companyProof?: {
        imagesUrls: string[];
        updatedAt: string;
      };
      driverProof?: {
        imagesUrls: string[];
        updatedAt: string;
      };
      address: {
        street: string;
        interiorNumber: string;
        exteriorNumber: string;
        colony: string;
        zipCode: string;
        references: string;
      };
      city: {
        _id: string;
        name: string;
      };
      crew: {
        _id: string;
        name: string;
      };
      neighborhood: {
        _id: string;
        name: string;
      };
      associate: {
        firstName: string;
        lastName: string;
        email: string;
        phone: number;
      };
      stockVehicle: {
        carNumber: string;
        model: string;
        brand: string;
        vin: string;
        carPlates: {
          plates: string;
        };
      };
      notes?: string;
      notAttendedReason?: string;
    }
  } | null;
  updateEvent: (eventIndex: number, updatedEvent: any) => void;
  setSelectedEvent: (event: any) => void;
}

const uploadEvidenceSchema = Yup.object().shape({
  images: fileListValidator('Debe seleccionar al menos una imagen', 'all-images', 1),
  notes: Yup.string().optional(),
});

// Agregar un nuevo esquema de validación para el formulario de "No se realizó"
const notAttendedSchema = Yup.object().shape({
  notAttendedReason: Yup.string()
    .transform((value) => value.trim())
    .min(6, 'La razón debe tener al menos 6 caracteres')
    .required('La razón es requerida'),
});

const statusMap: Record<keyof typeof InstallationStatus, string> = {
  scheduled: 'Agendada',
  rescheduled: 'Reagendada',
  completed: 'Completada',
  canceled: 'Cancelada',
  'not-attended': 'No atendida',
  installed: 'Instalada',
}


export function InstallationEventModal({ isOpen, onClose, event: eventProp, updateEvent, setSelectedEvent }: InstallationEventModalProps) {
  if (!eventProp) return null;

  const params = useParams<{ locale: string }>();
  const [loading, setLoading] = useState(false);
  const [event, setEvent] = useState(eventProp);
  const toast = useToast();
  const [showReschedule, setShowReschedule] = useState(false);
  const [showNotAttendedForm, setShowNotAttendedForm] = useState(false);
  const { user } = useCurrentUser<'company'>();

  // Add null checks to handle cases where permissions might not be available yet
  const isAllowed = user?.permissions?.role === 'admin' ||
                   user?.permissions?.role === 'owner' ||
                   user?.userType === 'superAdmin';



  const handleMarkArrived = async () => {
    try {
      setLoading(true);
      const response = await companyService.markArrived(event._id);
      console.log('Response of mark arrived', response);
      const newEvent = {
        ...event,
        appointment: {
          ...event.appointment,
          arrivedAt: response.data.arrivedAt,
        },
      };

      updateEvent(event.index, newEvent);
      setEvent(newEvent as any);

      toast({
        title: 'Llegada marcada con éxito',
        status: 'success',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
    } catch (error: any) {
      toast({
        title: 'Error al marcar llegada',
        status: 'error',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
      console.log('error', error.response.data);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values: { images: FileList; notes: string }) => {
    console.log('Form submitted with:', {
      images: values.images,
      notes: values.notes
    });

    const valuesParsed = {
      proofImages: Array.from(values.images),
      notes: values.notes,
    };

    try {
      setLoading(true);
      const response = await companyService.uploadEvidence(event._id, valuesParsed);
      console.log('Response of upload evidence', response);

      // Update the event status to 'completed'
      const newEvent = {
        ...event,
        appointment: {
          ...event.appointment,
          companyProof: response.data.companyProof,
        },
        status: 'installed'
      }
      updateEvent(event.index, newEvent);

      setEvent(newEvent as any);

      toast({
        title: 'Evidencia subida con éxito',
        status: 'success',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
      // onClose();
    } catch (error) {
      toast({
        title: 'Error al subir la evidencia',
        status: 'error',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
    } finally {
      setLoading(false);
    }

  };

  const [forceEnable, setForceEnable] = useState(localStorage.getItem('enable-images-upload') === 'true');

  useEffect(() => {

    const isEnable = localStorage.getItem('enable-images-upload') === 'true';
    setForceEnable(isEnable); // Update the state based on the localStorage value on every render

  }, [isOpen]);

  const parsedArrivedAt = useMemo(() => {
    if (!event.appointment.arrivedAt) return null;
    // format like this: 20 de enero de 2025 12:00 pm using luxon with DataTime
    return DateTime.fromISO(event.appointment.arrivedAt).setLocale('es').toFormat('dd cccc yyyy hh:mm a');
  }, [event.appointment.arrivedAt]);

  const parsedStartTime = useMemo(() => {
    if (!event.start) return null;
    // format like this: 20 de enero de 2025 12:00 pm using luxon with DataTime
    const date = DateTime.fromJSDate(event.start)
      .setLocale('es')
      .toFormat("cccc d 'de' MMMM 'de' yyyy");
    // first letter in uppercase
    return date.charAt(0).toUpperCase() + date.slice(1);
  }, [event.start]);

  const handleNotAttended = async (values: { notAttendedReason: string }) => {
    try {
      // Cerrar el modal antes de mostrar la confirmación
      setShowNotAttendedForm(false);
      const copiedEvent = { ...event };
      onClose();

      const { isConfirmed } = await Swal.fire({
        title: '¿Estás seguro de marcar como no realizado?',
        text: 'Esta acción no se puede deshacer',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Sí, marcar como no realizado',
        cancelButtonText: 'Cancelar',
      });

      if (isConfirmed) {
        setLoading(true);
        try {


          const response = await companyService.markNotAttended(event._id, values.notAttendedReason.trim());

          console.log('Response of mark not attended', response);

          // Actualizar el evento con la nueva información
          const newEvent = {
            ...event,
            status: 'not-attended',
            appointment: {
              ...event.appointment,
              notAttendedReason: values.notAttendedReason,
            },
          };

          updateEvent(event.index, newEvent);
          setEvent(newEvent as any);

          toast({
            title: 'Instalación marcada como no realizada',
            status: 'success',
            duration: 3000,
            isClosable: true,
            position: 'top',
          });
        } catch (error: any) {
          console.log('error', error);
          toast({
            title: 'Error al marcar como no realizada',
            description: error.message || 'Ocurrió un error al procesar la solicitud',
            status: 'error',
            duration: 3000,
            isClosable: true,
            position: 'top',
          });
        }
      } else {
        // Si cancela, restaurar el evento seleccionado
        setSelectedEvent(copiedEvent);
      }
    } catch (error: any) {
      toast({
        title: 'Error al marcar como no realizada',
        description: error.message || 'Ocurrió un error al procesar la solicitud',
        status: 'error',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {
        loading && <Spinner zIndex={900} />
      }
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className='sm:w-3/4 lg:w-3/5 xl:w-2/5 overflow-y-auto max-h-[80%]'>
          <DialogHeader>
            <DialogTitle>Detalles de la Instalación</DialogTitle>
          </DialogHeader>

          <div className="grid gap-4 pt-4 grid-cols-1">
            <div className="grid grid-cols-4 items-center gap-4">
              <div className="col-span-4 space-y-4">
                {/* Status and id of the event */}
                <div className='grid md:grid-cols-2 gap-4'>
                  {/* Show the status and id of the event in one row, using columns */}
                  <div>
                    <h3 className="font-medium">Estado de la Cita de Instalación</h3>
                    {/* <p className="text-sm text-gray-500">{event.status}</p> */}
                    <p
                      className="text-sm text-gray-500"
                      style={{
                        color: statusStyleMap[event.status]?.borderColor,
                      }}
                    >
                      {statusMap[event.status] || event.status}
                    </p>
                  </div>
                  <div>
                    <h3 className="font-medium">ID de la Instalación</h3>
                    <p className="text-sm text-gray-500">{event._id}</p>
                  </div>
                </div>

                {/* Arrival status section */}
                <div className="border-t pt-4">
                  <h3 className="font-medium mb-2">Estado de Llegada</h3>
                  {event.appointment.arrivedAt ? (
                    <p className="text-sm text-gray-500">
                      Llegada registrada: {parsedArrivedAt}
                    </p>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Button
                          // onClick={handleMarkArrived}
                          onClick={async () => {
                            // handleMarkArrived();
                            // Add a confirmation with swal before marking arrived
                            // Swal.fire({

                            // Close the modal before showing the confirmation
                            const copiedEvent = { ...event };
                            onClose();

                            await Swal.fire({
                              title: '¿Estás seguro de marcar la llegada?',
                              text: 'Esta acción no se puede deshacer',
                              icon: 'warning',
                              showCancelButton: true,
                              confirmButtonColor: '#3085d6',
                              cancelButtonColor: '#d33',
                              confirmButtonText: 'Sí, marcar llegada',
                              cancelButtonText: 'Cancelar',
                            }).then((result) => {
                              if (result.isConfirmed) {
                                handleMarkArrived();
                              } else {
                                setSelectedEvent(copiedEvent);
                              }
                            });
                          }}
                          className="w-full"
                        >
                          Marcar llegada
                        </Button>

                        {
                          isAllowed && (
                            <Button
                              variant="outline"
                              className="w-full"
                              onClick={() => setShowReschedule(true)}
                              disabled={
                                event.status === 'completed' ||
                                event.status === 'installed'
                              }
                            >
                              <CalendarClock className="mr-2 h-4 w-4" />
                              Reagendar
                            </Button>
                          )
                        }

                    </div>
                  )}
                </div>

                {/* No se realizó section */}
                {event.appointment.notAttendedReason && event.appointment.arrivedAt ? (
                  <div className="border-t pt-4">
                    <h3 className="font-medium mb-2">Razón de no realización</h3>
                    <p className="text-sm text-gray-500">
                      {event.appointment.notAttendedReason}
                    </p>
                  </div>
                ) : (
                  (event.status !== 'scheduled' && event.status !== 'rescheduled') ? null : (
                    <div className="border-t pt-4">
                      {showNotAttendedForm ? (
                        <FormikContainer
                          initialValues={{ notAttendedReason: '' }}
                          onSubmit={handleNotAttended}
                          validatorSchema={notAttendedSchema}
                          hideFooter
                        >
                          <div className="space-y-4">
                            <CustomInput
                              name="notAttendedReason"
                              label="Razón por la que no se realizó"
                              type="textarea"
                            // placeholder="Ej: Cliente no disponible, Vehículo no disponible, etc."
                            />
                            <div className="flex justify-end space-x-2">
                              <Button
                                variant="outline"
                                onClick={() => setShowNotAttendedForm(false)}
                              >
                                Cancelar
                              </Button>
                              <Button
                                type="submit"
                                className="bg-red-500 hover:bg-red-600 text-white"
                              >
                                Confirmar
                              </Button>
                            </div>
                          </div>
                        </FormikContainer>
                      ) : (
                        <Button
                          className="w-full bg-red-500 hover:bg-red-600 text-white"
                          onClick={() => setShowNotAttendedForm(true)}
                            >
                          No se pudo realizar (agregar razón)
                        </Button>
                      )}
                    </div>
                  )
                )}

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium">Cliente</h3>
                    <p className="text-sm text-gray-500">
                      {event.appointment.associate.firstName} {event.appointment.associate.lastName}
                    </p>
                    <p className="text-sm text-gray-500">{event.appointment.associate.email}</p>
                    <p className="text-sm text-gray-500">{event.appointment.associate.phone}</p>
                  </div>

                  <div>
                    <h3 className="font-medium">Vehículo</h3>
                    <p className="text-sm text-gray-500">
                      {event.appointment.stockVehicle.brand} {event.appointment.stockVehicle.model} | {event.appointment.stockVehicle.carNumber}
                    </p>
                    <p className="text-sm text-gray-500">
                      Placas: {event.appointment.stockVehicle.carPlates.plates}
                    </p>
                    <p className="text-sm text-gray-500">
                      VIN: {event.appointment.stockVehicle.vin}
                    </p>
                  </div>
                </div>

                {/* Añadir información de fecha y hora */}
                <div className="mt-4">
                  <h3 className="font-medium">Fecha y Hora de la Cita</h3>
                  <p className="text-sm text-gray-500">
                    {parsedStartTime}
                  </p>
                  <p className="text-sm text-gray-500">
                    Horario: {DateTime.fromJSDate(event.start).toFormat('HH:mm')} - {DateTime.fromJSDate(event.end).toFormat('HH:mm')}
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium">Ubicación</h3>
                    <p className="text-sm text-gray-500">
                      {event.appointment.address.street} {event.appointment.address.exteriorNumber}
                      {event.appointment.address.interiorNumber && `, Int. ${event.appointment.address.interiorNumber}`}
                    </p>
                    <p className="text-sm text-gray-500">
                      Col. {event.appointment.address.colony}, CP. {event.appointment.address.zipCode}
                    </p>
                    <p className="text-sm text-gray-500">
                      {event.appointment.city.name} - {event.appointment.neighborhood.name}
                    </p>
                    {event.appointment.address.references && (
                      <p className="text-sm text-gray-500">
                        Referencias: {event.appointment.address.references}
                      </p>
                    )}
                    {/* Create a Link to redirect to the neighborhood detail page */}
                    <div className='mt-2'>
                      <Link
                        href={`/${params.locale}/dashboard/cities/${event.appointment.city._id}/crews/${event.appointment.crew._id}/neighborhoods/${event.appointment.neighborhood._id}`}
                        // passHref
                        className='text-blue-500 underline '
                        target="_blank"
                      >
                        {/* <Button className="w-full">
                      </Button> */}
                        Ver detalles de la colonia
                      </Link>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div>
                      <h3 className="font-medium">Ciudad Asignada </h3>
                      <p className="text-sm text-gray-500">{event.appointment.city.name}</p>
                    </div>
                    <div>
                      <h3 className="font-medium">Equipo Asignado</h3>
                      <p className="text-sm text-gray-500">{event.appointment.crew.name}</p>
                    </div>
                    <div>
                      <h3 className="font-medium">Colonia Asignada</h3>
                      <p className="text-sm text-gray-500">{event.appointment.neighborhood.name}</p>
                    </div>
                  </div>
                </div>

                {/* Proof of installation */}

                <div className="grid md:grid-cols-2 gap-4">
                  {
                    event.appointment.companyProof && event.appointment.companyProof.imagesUrls.length > 0 && (
                      <div>
                        <h3 className="font-medium">Evidencia de Instalación (Compañía)</h3>
                        <div className="flex gap-3 overflow-x-auto">
                          {event.appointment.companyProof.imagesUrls.map((image: string, index: number) => (
                            <img
                              key={index}
                              src={image}
                              alt={index.toString()}
                              className="w-24 h-24 object-cover"
                            />
                          ))}
                        </div>
                      </div>
                    )
                  }
                  {event.appointment.driverProof && event.appointment.driverProof.imagesUrls.length > 0 && (
                    <div>
                      <h3 className="font-medium">Evidencia de Instalación (Conductor)</h3>
                      <div className="flex gap-3 overflow-x-auto">
                        {event.appointment.driverProof.imagesUrls.map((image: string, index: number) => (
                          <img
                            key={index}
                            src={image}
                            alt={index.toString()}
                            className="w-24 h-24 object-cover"
                          />
                        ))}
                      </div>
                    </div>
                  )
                  }
                </div>

                {
                  event.status === 'scheduled' && (

                    <FormikContainer
                      initialValues={{ images: "" as unknown as FileList, notes: '' }}
                      onSubmit={handleSubmit}
                      validatorSchema={uploadEvidenceSchema}
                      validateOnMount={true}
                      hideFooter
                    >
                      {({ isSubmitting, dirty, isValid }) => {
                        const validate = dirty && isValid;
                        return (
                          <div className="space-y-4">
                            <div>
                              <InputMultipleFiles
                                name="images"
                                label="Imágenes"
                                accept="all-images"
                                buttonText="Subir imágenes"
                                placeholder="Imágenes no mayores a 2 MB"
                                capture={true} // Habilita la opción de cámara
                              />
                            </div>

                            <div>
                              <h3 className="font-medium mb-2">Notas</h3>
                              <CustomInput
                                name="notes"
                                label="Notas adicionales (opcional)"
                                type="textarea"
                              />
                            </div>

                            <Button
                              type="submit"
                              disabled={
                                forceEnable ? isSubmitting || !validate
                                  : isSubmitting || !validate
                              }

                              className="w-full disabled:cursor-not-allowed"
                            >
                              Confirmar Instalación
                            </Button>
                          </div>
                        )
                      }}
                    </FormikContainer>
                  )
                }

              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Modal de reagendamiento */}
      {showReschedule && (
        <Dialog open={showReschedule} onOpenChange={() => setShowReschedule(false)}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Reagendar instalación</DialogTitle>
            </DialogHeader>
            <RescheduleInstallation
              appointmentId={event._id}
              currentNeighborhoodId={event.appointment.neighborhood._id}
              onClose={() => setShowReschedule(false)}
              onSuccess={() => {
                // Refrescar los datos después de reagendar
                updateEvent(event.index, {
                  ...event,
                  status: 'rescheduled'
                });
                onClose();
              }}
            />
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}

// function useCanConfirm({ event }: { event: InstallationEventModalProps['event'] }) {
//   const [currentTime, setCurrentTime] = useState(new Date());

//   useEffect(() => {
//     const interval = setInterval(() => {
//       setCurrentTime(new Date());
//     }, 1000);

//     return () => clearInterval(interval);
//   }, []);

//   const canConfirm = useMemo(() => {
//     const eventStart = new Date(event!.start);
//     const oneHourAndHalf = 1.5 * 60 * 60 * 1000; // 1.5 hours in milliseconds
//     return currentTime.getTime() - eventStart.getTime() >= oneHourAndHalf;
//   }, [currentTime, event]);

//   return canConfirm;
// }
