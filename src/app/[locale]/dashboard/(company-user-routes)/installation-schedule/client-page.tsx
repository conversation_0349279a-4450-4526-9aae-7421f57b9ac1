'use client'

import { cn } from "@/lib/utils";
import { AppointmentStatus, InstallationStatus } from "@/types/global-export";
import { DateTime } from "luxon";
import { useState, useCallback, useEffect } from "react";
import { luxonLocalizer, Calendar, View, Messages, Formats, NavigateAction } from "react-big-calendar";
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { InstallationEventModal } from "./_components/InstallationEventModal";
import { companyService } from "@/constants/companyService";
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { shouldRefetchByMonth } from '../../(workshop-user-routes)/appointments/_utils/dates';
import useHandleResize from '@/hooks/useHandleRezise';
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { debounce } from 'lodash';
import { getStateByLabel, getStateByCode, stateSelectOptions } from '@/constants/company';
import { Badge } from "@/components/ui/badge";
import { useCurrentUser } from '@/Providers/CurrentUserProvider';
import Spinner from '@/components/Loading/Spinner';

const messages: Messages = {
  date: 'Fecha',
  time: 'Hora',
  event: 'Evento',
  allDay: 'Todo el día',
  week: 'Semana',
  work_week: 'Semana laboral',
  day: 'Día',
  month: 'Mes',
  previous: 'Anterior',
  next: 'Siguiente',
  yesterday: 'Ayer',
  tomorrow: 'Mañana',
  today: 'Hoy',
  agenda: 'Agenda',
  showMore: (total: number) => `+${total} más`,
  noEventsInRange: 'No hay citas en este rango.',
};

export default function InstallationScheduleClientPage() {
  const params = useSearchParams();
  const [view, setView] = useState<View>('week');
  const [selectedEvent, setSelectedEvent] = useState(null);

  const dateParam = params.get('date');

  const currentDate = dateParam ? new Date(dateParam) : new Date();
  const start = params.get('start');
  const end = params.get('end');
  const isMobile = useHandleResize({ breakpoint: 500 });
  const queryClient = useQueryClient();
  const [isUserLoading, setIsUserLoading] = useState(true);
  const { user } = useCurrentUser();

  // Add effect to handle user loading state
  useEffect(() => {
    if (user) {
      setIsUserLoading(false);
    } else {
      // Set a timeout to stop showing the loading state after 2 seconds
      const timer = setTimeout(() => {
        setIsUserLoading(false);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [user]);

  // Query key generator
  const getQueryKey = (start: Date, end: Date) => {
    // Asegurarse de que las fechas son válidas
    const safeStart = DateTime.fromJSDate(start).isValid ? start : new Date();
    const safeEnd = DateTime.fromJSDate(end).isValid ? end : new Date();

    return [
      'installation-appointments',
      safeStart.toISOString().split('T')[0],
      safeEnd.toISOString().split('T')[0]
    ];
  };

  const formats: Formats = {
    // timeGutterFormat: (date: Date) => {
    //   // Simplifica el formato a "9 AM", "11 AM", etc.
    //   return DateTime.fromJSDate(date).toFormat('h a').toLowerCase();
    // },
    timeGutterFormat: isMobile ? (date: Date) => DateTime.fromJSDate(date).toFormat('h a').toLowerCase() : (date: Date) => DateTime.fromJSDate(date).toFormat('h:mm a'),
    dateFormat: 'dd',
    dayFormat: (date: Date) => {
      const day = DateTime.fromJSDate(date).setLocale('es').toFormat('dd ccc');
      const [dayNum, dayName] = day.split(' ');
      return `${dayNum} ${dayName.charAt(0).toUpperCase() + dayName.slice(1)}`;
    },
    dayHeaderFormat: (date: Date) => {
      const day = DateTime.fromJSDate(date).setLocale('es').toFormat('cccc d');
      const [dayName, dayNum] = day.split(' ');
      return `${dayName.charAt(0).toUpperCase() + dayName.slice(1)} ${dayNum}`;
    },
    monthHeaderFormat: (date: Date) => {
      const month = DateTime.fromJSDate(date).setLocale('es').toFormat('MMMM yyyy');
      const [monthName, year] = month.split(' ');
      return `${monthName.charAt(0).toUpperCase() + monthName.slice(1)} ${year}`;
    },
    dayRangeHeaderFormat: ({ start, end }: any) => {
      const months = [
        'Enero',
        'Febrero',
        'Marzo',
        'Abril',
        'Mayo',
        'Junio',
        'Julio',
        'Agosto',
        'Septiembre',
        'Octubre',
        'Noviembre',
        'Diciembre',
      ];

      const startDate = DateTime.fromJSDate(start);
      const endDate = DateTime.fromJSDate(end);

      if (startDate.month === endDate.month) {
        return `${startDate.day} - ${endDate.day} de ${months[startDate.month - 1]}`;
      }

      return `${startDate.day} de ${months[startDate.month - 1]} - ${endDate.day} de ${months[endDate.month - 1]
        }`;
    },
    eventTimeRangeFormat: () => '',
  };

  const currentDate2 = DateTime.fromJSDate(currentDate);

  // Calcula la diferencia hasta el domingo (siendo que para domingo: weekday === 7, y 7 % 7 === 0)
  const diffToSunday = currentDate2.weekday % 7;

  // Obtiene el inicio de la semana (domingo)
  const startWeekDay = currentDate2.minus({ days: diffToSunday });

  // El último día de la semana (sábado) es 6 días después del domingo
  const endWeekDay = startWeekDay.plus({ days: 6 });
  const endWeekDayJsDate = endWeekDay.toJSDate();

  const endDate = endWeekDay.month !== currentDate2.month ?
    new Date(endWeekDayJsDate.getFullYear(), endWeekDayJsDate.getMonth(), endWeekDayJsDate.getDate(), 23, 59, 59)
    :
    new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0, 23, 59, 59);


  const [rangeDates, setRangeDates] = useState<{ start: Date, end: Date }>(() => {
    if (start && end) {
      return {
        start: new Date(start),
        end: new Date(end)
      };
    }

    const initialDate = DateTime.fromJSDate(currentDate);
    const startOfWeek = initialDate.startOf('week');
    const endOfWeek = startOfWeek.plus({ days: 6 }).endOf('day');

    return {
      start: startOfWeek.toJSDate(),
      end: endOfWeek.toJSDate()
    };
  });

  const debouncedRefetch = useCallback(
    debounce((start: Date, end: Date) => {
      queryClient.invalidateQueries({
        queryKey: getQueryKey(start, end)
      });
    }, 300),
    []
  );

  const locale = Intl.DateTimeFormat().resolvedOptions().locale;

  const { data: events = [], isLoading, isError, refetch } = useQuery({
    queryKey: getQueryKey(rangeDates.start, rangeDates.end),
    queryFn: async () => {
      const start = DateTime.fromJSDate(rangeDates.start).toISO()!;
      const end = DateTime.fromJSDate(rangeDates.end).toISO()!;

      const appointments = await companyService.getInstallationAppointmentsByDateRange({
        startDate: start.split('T')[0],
        endDate: end.split('T')[0]
      });

      return appointments.data.map((appointment: any, index: number) => ({
        _id: appointment._id,
        index,
        title: appointment.associate.firstName + ' ' + appointment.associate.lastName,
        start: new Date(appointment.startTime),
        end: new Date(appointment.endTime),
        status: appointment.status,
        appointment,
      }));
    },
    staleTime: 1000 * 60 * 5, // 5 minutos
  });

  const handleNavigate = async (date: Date, view: View, action: NavigateAction) => {
    let start: Date;
    let end: Date;

    // Asegurarse de que la fecha de entrada es válida
    const safeDate = DateTime.fromJSDate(date).isValid
      ? date
      : new Date();

    switch (view) {
      case 'day':
        const dayDateTime = DateTime.fromJSDate(safeDate);
        start = dayDateTime.startOf('day').toJSDate();
        end = dayDateTime.endOf('day').toJSDate();
        break;
      case 'week':
        const weekStart = DateTime.fromJSDate(safeDate).startOf('week');
        start = weekStart.toJSDate();
        end = weekStart.plus({ days: 6 }).endOf('day').toJSDate();
        break;
      case 'month':
        const monthStart = DateTime.fromJSDate(safeDate).startOf('month');
        start = monthStart.toJSDate();
        end = monthStart.endOf('month').toJSDate();
        break;
      default:
        start = DateTime.fromJSDate(safeDate).toJSDate();
        end = DateTime.fromJSDate(safeDate).plus({ days: 1 }).toJSDate();
        break;
    }

    // Verificar que las fechas resultantes son válidas
    if (!DateTime.fromJSDate(start).isValid || !DateTime.fromJSDate(end).isValid) {
      console.error('Invalid date generated');
      return;
    }

    // Actualizar inmediatamente el rango visual
    setRangeDates({ start, end });

    // Debounce el refetch
    debouncedRefetch(start, end);
  };

  const handleRangeChange = (range: any, view?: View) => {
    // console.log('range', range);
    // console.log('view', view);
    if (!range) return;

    const start = range[0] || range.start;
    const end = range[range.length - 1] || range.end;

    try {
    // Actualizar inmediatamente el rango visual
      setRangeDates({ start, end });

      // Debounce el refetch
      debouncedRefetch(start, end);
    } catch (error) {
      console.error('Error handling range change:', error);
    }
  };

  const router = useRouter();
  const pathname = usePathname();

  // Show loading spinner while user data is loading
  if (isUserLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner />
      </div>
    );
  }

  return (
    <div className="space-y-4 ">
      <div className="flex justify-between mb-4 items-center">

      <Button
        variant="outline"
        size="sm"
        className="right-4 top-4 z-10"
        onClick={() => refetch()}
      >
        <RefreshCw className={cn("h-4 w-4 mr-2", isLoading && "animate-spin")} />
        Actualizar
      </Button>

        {/* Exportar información en otra pagina, redirigir */}

        <Button
          // variant="outline"
          size="sm"
          className="right-4 top-4 z-10"
          onClick={() => {
            router.push(`${pathname}/export`)
          }}
        >
          Exportar Datos
        </Button>
      </div>

      {/* Añadir la leyenda de colores */}
      <StateColorLegend />

      {
        view === 'month' && (
          <style>
            {`.rbc-row-content {
                height: 95px !important;
              }
            `}
          </style>
        )
      }
      <style>
        {`.rbc-label .rbc-time-header-gutter {
            width: ${isMobile ? '100px' : '150px'} !important;
           }
        `}
      </style>
      <Calendar
        className=" "
        localizer={luxonLocalizer(DateTime)}
        events={events}
        defaultDate={currentDate}
        // defaultDate={new Date("2025-05-02")}

        min={new Date('2025-12-20T09:00:00')}
        max={new Date('2025-12-20T20:00:00')}
        //   startAccessor="start"
        //   endAccessor="end"
        //   step={30}
        //   timeslots={1}
        views={['month', 'week', 'day']}
        culture={locale}
        defaultView={view}
        onView={(calendarView) => setView(calendarView)}
        messages={messages}
        formats={formats}
        eventPropGetter={eventStyleGetter}
        onSelectEvent={(event: any) => setSelectedEvent(event)}
        components={{
          event: EventComponent,
          month: {
            event: EventComponentMonthView,
            header: CustomWeekDayHeader,
          },
          // CustomTimeSlotWrapper,
          // timeSlotWrapper: CustomTimeSlotWrapper,
          timeGutterHeader: () => {
            return (
              <>
                {isLoading && (
                  <div className="calendar-loader">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
                  </div>
                )}
              </>
            );
          }
        }}
        popup
        onNavigate={handleNavigate}
        onRangeChange={handleRangeChange}
      />
      {selectedEvent && (
        <InstallationEventModal
          isOpen={!!selectedEvent}
          onClose={() => setSelectedEvent(null)}
          event={selectedEvent}
          setSelectedEvent={setSelectedEvent}
          updateEvent={() => {
            // Solo necesitamos invalidar la query para que React Query actualice los datos
            queryClient.invalidateQueries({
              queryKey: getQueryKey(rangeDates.start, rangeDates.end)
            });
          }}
        />
      )}
    </div>
  )
}

// Definir un objeto para mapear estados a estilos
export const statusStyleMap:
  Record<string, {
    borderColor: string;
    opacity: number;
    textStyle: string;
    indicator: React.ReactNode
  }> = {
  [InstallationStatus.canceled]: {
    borderColor: '#EF4444', // Rojo para cancelados
    opacity: 0.7,
    textStyle: 'line-through decoration-red-500',
    indicator: null
  },
  [InstallationStatus.completed]: {
    borderColor: '#10B981', // Verde para completados
    opacity: 1,
    textStyle: 'line-through decoration-green-500',
    indicator: null
  },
  [InstallationStatus['not-attended']]: {
    // borderColor: '#F59E0B', // Amarillo/naranja para no atendidos
    opacity: 1,
    // textStyle: 'line-through decoration-yellow-500',
    // red color:
    textStyle: 'line-through decoration-red-500',
    borderColor: '#EF4444', // Rojo para no atendidos
    indicator: null
  },
  [InstallationStatus.installed]: {
    borderColor: '#F59E0B', // Amarillo/naranja para instalados sin confirmar
    opacity: 1,
    textStyle: 'line-through decoration-yellow-500',
    indicator: null
  },
  [InstallationStatus.scheduled]: {
    borderColor: '#3B82F6', // Azul por defecto
    opacity: 1,
    textStyle: '',
    indicator: <span className="inline-block w-2 h-2 mr-1 bg-blue-500 rounded-full"></span>
  },
  [InstallationStatus.rescheduled]: {
    borderColor: '#8B5CF6', // Púrpura
    opacity: 1,
    textStyle: '',
    indicator: <span className="inline-block w-2 h-2 mr-1 bg-purple-500 rounded-full"></span>
  },
  default: {
    borderColor: '#3B82F6', // Azul por defecto
    opacity: 1,
    textStyle: '',
    indicator: null
  }
};

const eventStyleGetter = (event: any) => {
  const statusStyle = statusStyleMap[event.status] || statusStyleMap.default;

  // Obtener el código del estado de la ciudad
  const stateCode = event.appointment.city.state;

  // Buscar el estado en stateSelectOptions por su código
  const stateOption = getStateByLabel(stateCode);

  // Usar el color del estado si existe, o azul por defecto
  const borderColor = stateOption?.color || '#3B82F6';

  return {
    style: {
      color: '#3B3E45',
      backgroundColor: '#F3F4F6',
      borderRadius: '4px',
      borderColor: '#000',
      borderWidth: '.13em',
      border: 0,
      borderLeft: `5px solid ${borderColor}`,
      borderBottom: `2px solid gray`,
      opacity: statusStyle.opacity,
    },
  };
};

const EventComponent = ({ event }: { event: any }) => {
  const statusStyle = statusStyleMap[event.status] || statusStyleMap.default;
  const city = event.appointment.city.name;
  const neighborhood = event.appointment.neighborhood.name;

  return (
    <>
      <span className={cn('font-bold text-[#3B3E45] text-xs', statusStyle.textStyle)}>
        {statusStyle.indicator}
        {event?.appointment?.associate?.firstName} {event?.appointment?.associate?.lastName}
      </span>
      <span className="text-xs block text-[#666E7D]"><b> Ciudad:</b> {city}</span>
      <span className="text-xs block text-[#666E7D]"> <b> Colonia:</b> {neighborhood}</span>
    </>
  );
};

const EventComponentMonthView = ({ event }: { event: any }) => {
  const statusStyle = statusStyleMap[event.status] || statusStyleMap.default;
  const driverName = event.appointment.associate.firstName + ' ' + event.appointment.associate.lastName;

  return (
    <span className={cn('text-sm font-bold text-[#3B3E45]', statusStyle.textStyle)}>
      {statusStyle.indicator}
      {driverName}
    </span>
  );
};

const CustomWeekDayHeader = ({ label }: { label: string }) => (
  <div className="bg-white text-[#3B3E45] text-center p-2">{label}</div>
);

const CustomTimeSlotWrapper = ({ value, children }: any/*  { value: Date; children: React.ReactNode } */) => {
  const time = DateTime.fromJSDate(value).toFormat('h:mm a');

  return (
    <div className="custom-time-slot">
      <div className="time-label">{time}</div>
      {children}
    </div>
  );
};

// Componente para mostrar la leyenda de colores de estados

const validRoles = ({ permissions, userType = '' }: { permissions: any; userType: string }) => {
  return permissions?.role === 'admin' || permissions?.role === 'owner' || userType === 'superAdmin' || userType === 'company-gestor';
}

const colorStatusMap = {

  installed: {
    color: '#F59E0B',
    label: 'Instalada',
    longLabel: 'Instalada (sin confirmar por cliente)',
    className: ''
  },
  completed: {
    color: '#10B981',
    label: 'Completada',
    className: ''
  },
  notAttended: {
    color: '#EF4444',
    label: 'No Atendida',
    className: ''
  },
  scheduled: {
    color: '#3B82F6',
    label: 'Agendada',
    className: 'w-2 h-2 rounded-full'
  },
  rescheduled: {
    color: '#8B5CF6',
    label: 'Reagendada',
    className: 'w-2 h-2 rounded-full'
  },

} as const;

const StateColorLegend = () => {
  try {
    const { user } = useCurrentUser<'company'>();
    // Add null checks to handle cases where permissions might not be available yet
    const permissions = user?.permissions || { role: '', allowedCities: [] };
    const showAllStates = validRoles({ permissions, userType: user?.userType });
    const allowedRegions = permissions?.allowedCities || [];
  // Ensure allowedRegions is an array before using includes
  const filteredStates = showAllStates ? stateSelectOptions :
    (Array.isArray(allowedRegions) ?
      stateSelectOptions.filter((state) => allowedRegions.some(city =>
        // Handle both string and City object cases
        (typeof city === 'string' && city === state.label) ||
        // Handle City object case
        (typeof city === 'object' && city && 'state' in city && city.state === state.label)
      )) :
      []);

    return (
    <div className="mb-4 p-3 bg-white rounded-md shadow-sm">
      <h3 className="text-sm font-medium mb-2">Estados:</h3>
      <div className="flex flex-wrap gap-2">
        {filteredStates.map((state) => (
          <div key={state.code} className="flex items-center">
            <div
              className="w-3 h-3 mr-1 rounded-sm"
              style={{ backgroundColor: state.color || '#3B82F6' }}
            ></div>
            <span className="text-xs">{state.label}</span>
          </div>
        ))}
      </div>
      <h3 className="text-sm font-medium mt-3 mb-2">Estado de citas:</h3>
        <div className="flex flex-wrap gap-2">

          {
            Object.entries(colorStatusMap).map(([status, colorObj]) => (
              <div key={status} className="flex items-center">
                <div
                  // className="w-3 h-1 mr-1 rounded-sm"
                  className={cn(
                    'w-3 h-1 mr-1 rounded-full',
                    colorObj.className
                  )}
                  style={{ backgroundColor: colorObj.color }}
                ></div>
                <span className="text-xs">{(colorObj as any).longLabel || colorObj.label}</span>
              </div>
            ))
          }

      </div>
    </div>
  );
  } catch (error) {
    console.error("Error rendering StateColorLegend:", error);
    // Return a simplified version if there's an error
    return (
      <div className="mb-4 p-3 bg-white rounded-md shadow-sm">
        <h3 className="text-sm font-medium mb-2">Estado de citas:</h3>
        <div className="flex flex-wrap gap-2">
          {
            Object.entries(colorStatusMap).map(([status, colorObj]) => (
              <div key={status} className="flex items-center">
                <div
                  // className="w-3 h-1 mr-1 rounded-sm"
                  className={cn(
                    'w1-3 h-3 mr-1 rounded-full',
                    colorObj.className
                  )}
                  style={{ backgroundColor: colorObj.color }}
                ></div>
                <span className="text-xs">{colorObj.label}</span>
              </div>
            ))
          }
        </div>
      </div>
    );
  }
};
