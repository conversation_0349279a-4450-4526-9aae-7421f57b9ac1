export interface RootObject {
  data: Datum[];
  message: string;
}

export interface Datum {
  __v: number;
  _id: string;
  address: Address;
  arrivedAt?: Date;
  associate: Associate;
  associateId: string;
  city: City;
  cityId: CityID;
  companyId: CompanyID;
  companyProof: Proof;
  createdAt: Date;
  crew: Crew;
  crewId: CrewID;
  driverProof: Proof;
  endTime: Date;
  id: string;
  neighborhood: Crew;
  neighborhoodId: string;
  notifications: Notifications;
  rescheduleCount: number;
  reschedulingHistory: ReschedulingHistory[];
  startTime: Date;
  status: Status;
  stockId: string;
  stockVehicle: StockVehicle;
  updatedAt: Date;
}

export interface Address {
  colony: string;
  exteriorNumber: string;
  interiorNumber: string;
  references: string;
  street: string;
  zipCode: string;
}

export interface Associate {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
}

export interface City {
  _id: CityID;
  name: Name;
  state: State;
}

export enum CityID {
  The6802A0Ce0Fbabbb43E7279Aa = "6802a0ce0fbabbb43e7279aa",
  The6802A0F848322Ce38Db71971 = "6802a0f848322ce38db71971",
  The6802A1440Fbabbb43E727Ede = "6802a1440fbabbb43e727ede",
  The6802A47348322Ce38Db75Cec = "6802a47348322ce38db75cec",
  The6802A4820Fbabbb43E72Be42 = "6802a4820fbabbb43e72be42",
}

export enum Name {
  Cdmx = "CDMX",
  Gdl = "GDL",
  Mty = "MTY",
  Puebla = "Puebla ",
  Tijuana = "Tijuana ",
}

export enum State {
  BajaCalifornia = "Baja California",
  CiudadDeMéxico = "Ciudad de México",
  Jalisco = "Jalisco",
  NuevoLeón = "Nuevo León",
  Puebla = "Puebla",
}

export enum CompanyID {
  The68029Cf48947568F85Dd721F = "68029cf48947568f85dd721f",
}

export interface Proof {
  imagesUrls: string[];
  uploadedAt?: Date;
}

export interface Crew {
  _id: string;
  name: string;
}

export enum CrewID {
  The6802A4A20Fbabbb43E72C020 = "6802a4a20fbabbb43e72c020",
  The6802A4B448322Ce38Db76314 = "6802a4b448322ce38db76314",
  The6802A4D348322Ce38Db76885 = "6802a4d348322ce38db76885",
  The6802A4E048322Ce38Db76A4C = "6802a4e048322ce38db76a4c",
  The6802A5120Fbabbb43E72Cc50 = "6802a5120fbabbb43e72cc50",
  The6802A52448322Ce38Db76D72 = "6802a52448322ce38db76d72",
  The6802A5340Fbabbb43E72Cd2D = "6802a5340fbabbb43e72cd2d",
  The6802A54B48322Ce38Db77176 = "6802a54b48322ce38db77176",
  The6802A55348322Ce38Db7719E = "6802a55348322ce38db7719e",
  The6802A55C0Fbabbb43E72Ced0 = "6802a55c0fbabbb43e72ced0",
}

export interface Notifications {
  oneHourBefore: OneBefore;
  oneNightBefore: OneBefore;
}

export interface OneBefore {
  eventId?: string;
  scheduled: boolean;
  sent: boolean;
}

export interface ReschedulingHistory {
  _id: string;
  newStartTime: Date;
  previousStartTime: Date;
  rescheduledAt: Date;
}

export enum Status {
  completed = "completed",
  installed = "installed",
  scheduled = "scheduled",
}

export interface StockVehicle {
  _id: string;
  brand: Brand;
  carNumber: string;
  carPlates: CarPlates;
  contractNumber: string;
  model: Model;
  vin: string;
}

export enum Brand {
  Byd = "BYD",
}

export interface CarPlates {
  plates: string;
}

export enum Model {
  DolphinEv = "DOLPHIN EV",
}
