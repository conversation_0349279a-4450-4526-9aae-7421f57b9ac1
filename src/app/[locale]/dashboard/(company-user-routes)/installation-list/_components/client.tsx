'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { companyService } from '@/constants/companyService';
import { InstallationTable } from './installation-table';
import { InstallationFilters } from './installation-filters';
import { InstallationEventModal } from '../../installation-schedule/_components/InstallationEventModal';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';

export default function InstallationListClient() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const { toast } = useToast();

  // Estado para el modal
  const [selectedEvent, setSelectedEvent] = useState<any>(null);

  // Obtener parámetros de búsqueda
  const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1;
  const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 10;
  const query = searchParams.get('query') || undefined;
  const startDate = searchParams.get('startDate') || undefined;
  const endDate = searchParams.get('endDate') || undefined;
  const status = searchParams.get('status') || undefined;

  // Función para actualizar los parámetros de búsqueda
  const updateSearchParams = (params: Record<string, string | undefined>) => {
    const newParams = new URLSearchParams(searchParams.toString());

    // Actualizar o eliminar parámetros
    Object.entries(params).forEach(([key, value]) => {
      if (value === undefined || value === '') {
        newParams.delete(key);
      } else {
        newParams.set(key, value);
      }
    });

    // Resetear a la página 1 si cambian los filtros
    if (Object.keys(params).some(key => key !== 'page')) {
      newParams.set('page', '1');
    }

    router.push(`${pathname}?${newParams.toString()}`);
  };

  // Consulta para obtener las citas de instalación
  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ['installation-appointments', page, limit, query, startDate, endDate, status],
    queryFn: async () => {
      try {
        return await companyService.searchInstallationAppointments({
          page,
          limit,
          query,
          startDate,
          endDate,
          status
        });
      } catch (error) {
        console.error('Error fetching installation appointments:', error);
        toast({
          title: 'Error',
          description: 'No se pudieron cargar las citas de instalación',
          variant: 'destructive'
        });
        throw error;
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutos
  });

  // Manejar la actualización después de cerrar el modal
  const handleModalClose = () => {
    setSelectedEvent(null);
    refetch(); // Refrescar los datos después de cerrar el modal
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Listado de Instalaciones</h1>
      </div>

      <InstallationFilters
        initialQuery={query}
        initialStartDate={startDate}
        initialEndDate={endDate}
        initialStatus={status}
        onFilterChange={updateSearchParams}
      />

      {isLoading ? (
        <div className="flex justify-center items-center py-10">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Cargando instalaciones...</span>
        </div>
      ) : isError ? (
        <div className="text-center py-10 text-red-500">
          Error al cargar las instalaciones. Por favor, intenta de nuevo.
        </div>
      ) : (
        <InstallationTable
          data={data?.data || []}
          pagination={data?.pagination}
          onPageChange={(newPage) => updateSearchParams({ page: newPage.toString() })}
          onRowClick={(appointment) => {
            // Convertir la cita al formato esperado por el modal
            const event = {
              _id: appointment._id,
              title: `${appointment.associate.firstName} ${appointment.associate.lastName}`,
              start: new Date(appointment.startTime),
              end: new Date(appointment.endTime),
              status: appointment.status,
              appointment: appointment
            };
            setSelectedEvent(event);
          }}
        />
      )}

      {selectedEvent && (
        <InstallationEventModal
          isOpen={!!selectedEvent}
          onClose={handleModalClose}
          event={selectedEvent}
          setSelectedEvent={setSelectedEvent}
          updateEvent={() => {
            // Convertir refetch a una función que acepta los parámetros esperados
            return refetch();
          }}
        />
      )}
    </div>
  );
}
