'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CalendarIcon, Search, X } from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { cn } from '@/lib/utils';
// import { useDebounce } from '@/hooks/useDebounce';
import useDebounce from '@/hooks/useDebounce';

interface InstallationFiltersProps {
  initialQuery?: string;
  initialStartDate?: string;
  initialEndDate?: string;
  initialStatus?: string;
  onFilterChange: (filters: Record<string, string | undefined>) => void;
}

export function InstallationFilters({
  initialQuery = '',
  initialStartDate,
  initialEndDate,
  initialStatus,
  onFilterChange
}: InstallationFiltersProps) {
  const [query, setQuery] = useState(initialQuery);
  const [startDate, setStartDate] = useState<Date | undefined>(
    initialStartDate ? new Date(initialStartDate) : undefined
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    initialEndDate ? new Date(initialEndDate) : undefined
  );
  const [status, setStatus] = useState(initialStatus || '');

  // Debounce para la búsqueda por texto
  const debouncedSearch = useDebounce((value: string) => {
    onFilterChange({ query: value || undefined });
  }, 500);

  // Efecto para manejar cambios en la búsqueda
  useEffect(() => {
    debouncedSearch(query);
  }, [query, debouncedSearch]);

  // Manejar cambios en las fechas
  const handleDateChange = (type: 'start' | 'end', date?: Date) => {
    if (type === 'start') {
      setStartDate(date);
      onFilterChange({
        startDate: date ? date.toISOString().split('T')[0] : undefined,
        endDate: endDate ? endDate.toISOString().split('T')[0] : undefined
      });
    } else {
      setEndDate(date);
      onFilterChange({
        startDate: startDate ? startDate.toISOString().split('T')[0] : undefined,
        endDate: date ? date.toISOString().split('T')[0] : undefined
      });
    }
  };

  // Manejar cambios en el estado
  const handleStatusChange = (value: string) => {
    setStatus(value);
    onFilterChange({ status: value === 'all' ? undefined : value });
  };

  // Limpiar todos los filtros
  const handleClearFilters = () => {
    setQuery('');
    setStartDate(undefined);
    setEndDate(undefined);
    setStatus('');
    onFilterChange({
      query: undefined,
      startDate: undefined,
      endDate: undefined,
      status: undefined
    });
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border space-y-4">
      <div className="flex flex-col md:flex-row gap-4">
        {/* Búsqueda por texto */}
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="text"
              placeholder="Buscar por placas, VIN, nombre..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="pl-9"
            />
            {query && (
              <button
                onClick={() => setQuery('')}
                className="absolute right-2.5 top-2.5 text-gray-500 hover:text-gray-700"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>

        {/* Selector de fecha inicial */}
        <div className="w-full md:w-48">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !startDate && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {startDate ? format(startDate, 'PP', { locale: es }) : "Fecha inicial"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={startDate}
                onSelect={(date) => handleDateChange('start', date)}
                initialFocus
                locale={es}
              />
              {startDate && (
                <div className="p-2 border-t border-border">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDateChange('start', undefined)}
                    className="w-full"
                  >
                    Limpiar
                  </Button>
                </div>
              )}
            </PopoverContent>
          </Popover>
        </div>

        {/* Selector de fecha final */}
        <div className="w-full md:w-48">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !endDate && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {endDate ? format(endDate, 'PP', { locale: es }) : "Fecha final"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={endDate}
                onSelect={(date) => handleDateChange('end', date)}
                initialFocus
                locale={es}
                disabled={(date) => startDate ? date < startDate : false}
              />
              {endDate && (
                <div className="p-2 border-t border-border">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDateChange('end', undefined)}
                    className="w-full"
                  >
                    Limpiar
                  </Button>
                </div>
              )}
            </PopoverContent>
          </Popover>
        </div>

        {/* Selector de estado */}
        <div className="w-full md:w-48">
          <Select value={status} onValueChange={handleStatusChange}>
            <SelectTrigger>
              <SelectValue placeholder="Estado" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos</SelectItem>
              <SelectItem value="scheduled">Programada</SelectItem>
              <SelectItem value="rescheduled">Reagendada</SelectItem>
              <SelectItem value="completed">Completada</SelectItem>
              <SelectItem value="cancelled">Cancelada</SelectItem>
              <SelectItem value="not-attended">No asistió</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Botón para limpiar filtros */}
        <Button
          variant="outline"
          onClick={handleClearFilters}
          className="w-full md:w-auto"
        >
          Limpiar filtros
        </Button>
      </div>
    </div>
  );
}