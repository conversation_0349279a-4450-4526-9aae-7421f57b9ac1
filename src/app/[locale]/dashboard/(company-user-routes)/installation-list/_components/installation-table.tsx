'use client';

import { useState } from 'react';
import { DataTableV2 } from '@/components/DataTableV2';
import { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Eye, ChevronLeft, ChevronRight } from 'lucide-react';
import { formatDate } from '@/lib/utils';

// Tipo para la respuesta de la API
export interface InstallationAppointment {
  _id: string;
  startTime: string;
  endTime: string;
  status: string;
  address: {
    street: string;
    exteriorNumber: string;
    interiorNumber?: string;
    colony: string;
    zipCode: string;
    references?: string;
  };
  city: {
    name: string;
    state: string;
  };
  crew: {
    name: string;
  };
  neighborhood: {
    name: string;
  };
  associate: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  stockVehicle: {
    carNumber: string;
    model: string;
    brand: string;
    vin: string;
    carPlates: {
      plates: string;
    };
  };
}

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

interface InstallationTableProps {
  data: InstallationAppointment[];
  pagination: PaginationInfo;
  onPageChange: (page: number) => void;
  onRowClick: (appointment: InstallationAppointment) => void;
}

export function InstallationTable({
  data,
  pagination,
  onPageChange,
  onRowClick
}: InstallationTableProps) {
  // Función para obtener el badge según el estado
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Programada</Badge>;
      case 'rescheduled':
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">Reagendada</Badge>;
      case 'in-progress':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">En progreso</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Completada</Badge>;
      case 'cancelled':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Cancelada</Badge>;
      case 'not-attended':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">No asistió</Badge>;
      case 'installed':
        return <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">Instalada</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Definir las columnas de la tabla
  const columns: ColumnDef<InstallationAppointment, any>[] = [
    {
      accessorKey: 'stockVehicle.carPlates.plates',
      header: 'Placas',
      cell: ({ row }) => {
        const plates = row.original.stockVehicle?.carPlates?.plates || 'N/A';
        return <div className="font-medium">{plates}</div>;
      },
    },
    {
      accessorKey: 'stockVehicle.brand',
      header: 'Vehículo',
      cell: ({ row }) => {
        const brand = row.original.stockVehicle?.brand || '';
        const model = row.original.stockVehicle?.model || '';
        return <div>{`${brand} ${model}`}</div>;
      },
    },
    {
      accessorKey: 'associate.firstName',
      header: 'Cliente',
      cell: ({ row }) => {
        const firstName = row.original.associate?.firstName || '';
        const lastName = row.original.associate?.lastName || '';
        return <div>{`${firstName} ${lastName}`}</div>;
      },
    },
    {
      accessorKey: 'startTime',
      header: 'Fecha',
      cell: ({ row }) => {
        return <div>{formatDate(new Date(row.original.startTime))}</div>;
      },
    },
    {
      accessorKey: 'address',
      header: 'Dirección',
      cell: ({ row }) => {
        const address = row.original.address;
        const street = address?.street || '';
        const exteriorNumber = address?.exteriorNumber || '';
        const colony = address?.colony || '';
        return (
          <div className="max-w-xs truncate" title={`${street} ${exteriorNumber}, ${colony}`}>
            {`${street} ${exteriorNumber}, ${colony}`}
          </div>
        );
      },
    },
    {
      accessorKey: 'city.name',
      header: 'Ciudad',
      cell: ({ row }) => {
        return <div>{row.original.city?.name || 'N/A'}</div>;
      },
    },
    {
      accessorKey: 'crew.name',
      header: 'Cuadrilla',
      cell: ({ row }) => {
        return <div>{row.original.crew?.name || 'N/A'}</div>;
      },
    },
    {
      accessorKey: 'status',
      header: 'Estado',
      cell: ({ row }) => {
        return getStatusBadge(row.original.status);
      },
    },
    {
      id: 'actions',
      header: 'Acciones',
      cell: ({ row }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              onRowClick(row.original);
            }}
          >
            <Eye className="h-4 w-4 mr-1" />
            Ver
          </Button>
        );
      },
    },
  ];

  return (
    <div className="space-y-4">
      <DataTableV2
        columns={columns}
        data={data}
        total={pagination?.total || 0}
        allRecords={pagination?.total || 0}
        onPageChange={async () => { }}
        lenguage="es"
      />

      {pagination && pagination.pages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Mostrando {data.length} de {pagination.total} resultados
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                // Mostrar páginas alrededor de la página actual
                let pageNumber: number;

                if (pagination.pages <= 5) {
                  // Si hay 5 o menos páginas, mostrar todas
                  pageNumber = i + 1;
                } else if (pagination.page <= 3) {
                  // Si estamos en las primeras páginas
                  pageNumber = i + 1;
                } else if (pagination.page >= pagination.pages - 2) {
                  // Si estamos en las últimas páginas
                  pageNumber = pagination.pages - 4 + i;
                } else {
                  // Si estamos en el medio
                  pageNumber = pagination.page - 2 + i;
                }

                return (
                  <Button
                    key={pageNumber}
                    variant={pagination.page === pageNumber ? "default" : "outline"}
                    size="sm"
                    onClick={() => onPageChange(pageNumber)}
                    className="w-8 h-8 p-0"
                  >
                    {pageNumber}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(pagination.page + 1)}
              disabled={pagination.page >= pagination.pages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}


