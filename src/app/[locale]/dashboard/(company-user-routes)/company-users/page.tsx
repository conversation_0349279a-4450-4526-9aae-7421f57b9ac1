import { Suspense } from "react"
import { getUsers } from "@/lib/user-actions copy"
import { CreateUserButton } from "@/app/_components/company-users/create-user-button"
import { UserList } from "@/app/_components/company-users/user-list"
import getUserById, { getSession } from "@/actions/getUserById"
import { redirect } from 'next/navigation'

export default async function UsersPage() {
  try {
    const session = await getSession();

    if (!session?.user) {
      console.error("No session found. Redirecting to login.");
      redirect('/login');
    }

    const user = await getUserById();

    if (!user) {
      console.error("User not found. Using session data instead.");

      const organizationId = (session.user as any).organizationId || '';
      const users = await getUsers(organizationId);

      return (
        <div className="container mx-auto p-4">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">Usuarios</h1>
            <CreateUserButton companyId={organizationId} />
          </div>

          <Suspense fallback={<div>Cargando usuarios...</div>}>
            <UserList users={users} companyId={organizationId} />
          </Suspense>
        </div>
      );
    }

    const organizationId = user.organizationId;
    const companyId = (user as any).companyId || organizationId;


  return (
    <div className="md:container mx-auto md:py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Usuarios</h1>
        <CreateUserButton companyId={companyId} />
      </div>

      <Suspense fallback={<div>Cargando usuarios...</div>}>
        <Users companyId={companyId} />
      </Suspense>
    </div>
  )
  } catch (error) {
    console.error("Error in UsersPage:", error);
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold text-red-500">Error al cargar usuarios</h1>
        <p>Ha ocurrido un error al cargar la página. Por favor, intenta de nuevo más tarde.</p>
      </div>
    );
  }
}

async function Users({ companyId }: { companyId: string }) {
  const users = await getUsers(companyId);
  return <UserList users={users} companyId={companyId} />

}
