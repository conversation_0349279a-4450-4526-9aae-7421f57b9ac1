import SideNavbar from '@/components/Navbar/SideNavbar';
import CheckSession from '../checkSession';
import LogoutButton from '@/components/LogoutButton';
import CurrentUserProvider from '../../../Providers/CurrentUserProvider';
import { getI18n } from '@/i18n/server';
import getUserById from '@/actions/getUserById';
import getCurrentUser, { getSession } from '@/actions/getCurrentUser';
import { UserResponse } from '@/types';
import { redirect } from 'next/navigation';
import { deleteCookie } from 'cookies-next';
import { Toaster } from '@/components/ui/toaster';

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const t = await getI18n();
  const session = await getSession();
  const user = session?.user;

  if (!user) return redirect('/');

  const userDetails = await getUserById();

  // console.log('user', userDetails);
  // console.log('userType', userDetails?.userType);

  // if (!userDetails) {
  //   console.log('No user details');
  //   return redirect('/');
  // }


  const currentUser = {
    ...user,
    ...userDetails,
  } as any as UserResponse;

  if (!user)
    return (
      <CheckSession user={user}>
        <div className="w-[100vw] h-[100vh] flex">
          <div className="m-auto flex flex-col items-center gap-3">
            <p className="text-[24px] font-bold ">Tu sesión ha expirado, por favor inicia sesión de nuevo</p>
            <LogoutButton />
          </div>
        </div>
      </CheckSession>
    );

  return (
    <CheckSession user={user}>
      <CurrentUserProvider currentUser={currentUser}>
        <SideNavbar>
          {children}
        </SideNavbar>
        <Toaster />
      </CurrentUserProvider>
    </CheckSession>
  );
}
