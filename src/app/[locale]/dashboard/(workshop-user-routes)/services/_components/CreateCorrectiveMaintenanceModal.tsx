'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, Wrench } from 'lucide-react';
import { IService } from '../../vehicles/[stockId]/_actions/getServicesByVehicleId';
import { createCorrectiveFromPreventive } from '../_actions/createCorrectiveFromPreventive';
import { getWorkshops } from '../../corrective-maintenance/_actions/getWorkshops';

const createCorrectiveSchema = z.object({
  failureType: z.enum(['known', 'unknown']),
  arrivalMethod: z.enum(['driving', 'tow-truck']),
  customerDescription: z.string().min(10, 'La descripción debe tener al menos 10 caracteres'),
  canVehicleDrive: z.boolean(),
  needsTowTruck: z.boolean(),
  approvalType: z.enum(['fleet', 'customer']),
});

type CreateCorrectiveFormData = z.infer<typeof createCorrectiveSchema>;

interface Workshop {
  _id: string;
  name: string;
}

interface CreateCorrectiveMaintenanceModalProps {
  service: IService;
}

export function CreateCorrectiveMaintenanceModal({ service }: CreateCorrectiveMaintenanceModalProps) {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [workshops, setWorkshops] = useState<Workshop[]>([]);
  const [loadingData, setLoadingData] = useState(false);

  const form = useForm<CreateCorrectiveFormData>({
    resolver: zodResolver(createCorrectiveSchema),
    defaultValues: {
      failureType: 'unknown',
      arrivalMethod: 'driving',
      canVehicleDrive: true,
      needsTowTruck: false,
      approvalType: 'customer',
      customerDescription: `Problema reportado para el vehículo relacionado con el servicio ${service._id.substring(0, 8)}. `,
    },
  });

  // Load workshops when modal opens
  useEffect(() => {
    if (isOpen) {
      loadWorkshops();
    }
  }, [isOpen]);

  const loadWorkshops = async () => {
    setLoadingData(true);
    try {
      const workshopsData = await getWorkshops();
      if (workshopsData && workshopsData.success) {
        setWorkshops(workshopsData.data);
      } else {
        toast({
          title: 'Error',
          description: 'Error al cargar los talleres',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error loading workshops:', error);
      toast({
        title: 'Error',
        description: 'Error inesperado al cargar los talleres',
        variant: 'destructive',
      });
    } finally {
      setLoadingData(false);
    }
  };

  const onSubmit = async (data: CreateCorrectiveFormData) => {
    setIsSubmitting(true);
    try {
      const orderData = {
        stockId: service.stockId,
        associateId: service.stock?.associates?.[service.stock.associates.length - 1]?._id || '',
        workshopId: service.workshopId,
        type: 'preventive-detected' as const,
        preventiveServiceId: service._id,
        ...data,
      };

      const response = await createCorrectiveFromPreventive(orderData);

      if (response.success) {
        toast({
          title: 'Éxito',
          description: 'Orden de mantenimiento correctivo creada exitosamente',
        });
        form.reset();
        setIsOpen(false);
        // Optionally refresh the page or update the UI
        window.location.reload();
      } else {
        // Handle specific error for vehicle already having active corrective maintenance
        const specificError = (response.data as any)?.error || '';
        const errorMessage = response.message || '';

        const isVehicleAlreadyActive = specificError === 'Vehicle already has an active corrective maintenance order' ||
                                      specificError.includes('Vehicle already has an active corrective maintenance order') ||
                                      errorMessage.includes('Vehicle already has an active corrective maintenance order');

        if (isVehicleAlreadyActive) {
          // Close modal first, then show notification
          setIsOpen(false);
          setTimeout(() => {
            toast({
              title: 'Vehículo con Orden Activa',
              description: 'Este vehículo ya tiene una orden de mantenimiento correctivo activa. Complete o cancele la orden existente antes de crear una nueva.',
              variant: 'destructive',
            });
          }, 100);
        } else {
          // Close modal first, then show notification
          setIsOpen(false);
          setTimeout(() => {
            toast({
              title: 'Error',
              description: response.message || 'Error al crear la orden',
              variant: 'destructive',
            });
          }, 100);
        }
      }
    } catch (error: any) {
      console.error('Error creating corrective maintenance order:', error);

      // Check if it's the specific error about vehicle already having active order
      const errorMessage = error?.message || '';
      const isVehicleAlreadyActive = errorMessage.includes('Vehicle already has an active corrective maintenance order');

      if (isVehicleAlreadyActive) {
        // Close modal first, then show notification
        setIsOpen(false);
        setTimeout(() => {
          toast({
            title: 'Vehículo con Orden Activa',
            description: 'Este vehículo ya tiene una orden de mantenimiento correctivo activa. Complete o cancele la orden existente antes de crear una nueva.',
            variant: 'destructive',
          });
        }, 100);
      } else {
        // Close modal first, then show notification
        setIsOpen(false);
        setTimeout(() => {
          toast({
            title: 'Error',
            description: 'Error inesperado al crear la orden',
            variant: 'destructive',
          });
        }, 100);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    form.reset();
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <p className="flex items-center gap-2 w-full text-left cursor-pointer">
          <Wrench className="h-4 w-4" />
          Crear Mantenimiento Correctivo
        </p>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Crear Mantenimiento Correctivo</DialogTitle>
          <p className="text-sm text-gray-600">
            Crear orden de mantenimiento correctivo para este vehículo
          </p>
        </DialogHeader>

        {loadingData ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Cargando datos...</span>
          </div>
        ) : (
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Vehicle Information (Read-only) */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-2">Información del Vehículo</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Placa:</span> {service.stock?.carPlates?.plates || 'N/A'}
                </div>
                <div>
                  <span className="font-medium">Taller:</span> {service.workshop?.name || 'N/A'}
                </div>
                <div>
                  <span className="font-medium">Servicio Preventivo:</span> {service._id.substring(0, 8)}
                </div>
                <div>
                  <span className="font-medium">Estado del Servicio:</span> {service.status}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Failure Type */}
              <div className="space-y-2">
                <Label htmlFor="failureType">Tipo de Falla *</Label>
                <Select
                  value={form.watch('failureType') || undefined}
                  onValueChange={(value) => form.setValue('failureType', value as 'known' | 'unknown')}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona el tipo de falla" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="known">Falla Conocida</SelectItem>
                    <SelectItem value="unknown">Falla Desconocida</SelectItem>
                  </SelectContent>
                </Select>
                {form.formState.errors.failureType && (
                  <p className="text-sm text-red-500">{form.formState.errors.failureType.message}</p>
                )}
              </div>

              {/* Arrival Method */}
              <div className="space-y-2">
                <Label htmlFor="arrivalMethod">Método de Llegada *</Label>
                <Select
                  value={form.watch('arrivalMethod') || undefined}
                  onValueChange={(value) => form.setValue('arrivalMethod', value as 'driving' | 'tow-truck')}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="¿Cómo llegó el vehículo?" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="driving">Conduciendo</SelectItem>
                    <SelectItem value="tow-truck">Grúa</SelectItem>
                  </SelectContent>
                </Select>
                {form.formState.errors.arrivalMethod && (
                  <p className="text-sm text-red-500">{form.formState.errors.arrivalMethod.message}</p>
                )}
              </div>
            </div>

            {/* Customer Description */}
            <div className="space-y-2">
              <Label htmlFor="customerDescription">Descripción del Problema *</Label>
              <Textarea
                {...form.register('customerDescription')}
                placeholder="Describe detalladamente el problema reportado por el cliente o detectado en el vehículo..."
                rows={4}
              />
              {form.formState.errors.customerDescription && (
                <p className="text-sm text-red-500">{form.formState.errors.customerDescription.message}</p>
              )}
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-primaryBtn text-white hover:bg-purple-700"
              >
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Crear Orden Correctiva
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
