'use server';

import { URL_API } from '@/constants';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
import { ApiResponse, CorrectiveMaintenanceOrder } from '../../corrective-maintenance/types';

export interface CreateCorrectiveFromPreventiveRequest {
  stockId: string;
  associateId: string;
  workshopId: string;
  type: 'preventive-detected';
  preventiveServiceId: string;
  failureType: 'known' | 'unknown';
  arrivalMethod: 'driving' | 'tow-truck';
  customerDescription: string;
  canVehicleDrive: boolean;
  needsTowTruck: boolean;
  approvalType: 'fleet' | 'customer';
}

export async function createCorrectiveFromPreventive(
  orderData: CreateCorrectiveFromPreventiveRequest
): Promise<ApiResponse<CorrectiveMaintenanceOrder>> {
  const user = await getCurrentUser();

  if (!user) {
    return {
      success: false,
      data: null as any,
      message: 'Usuario no autenticado',
    };
  }

  try {

    // Prepare the order data for the API
    const apiOrderData = {
      stockId: orderData.stockId,
      associateId: orderData.associateId,
      workshopId: orderData.workshopId,
      type: orderData.type,
      failureType: orderData.failureType,
      arrivalMethod: orderData.arrivalMethod,
      customerDescription: orderData.customerDescription,
      canVehicleDrive: orderData.canVehicleDrive,
      needsTowTruck: orderData.needsTowTruck,
      approvalType: orderData.approvalType,
      // Additional metadata for tracking the source
      metadata: {
        sourceType: 'preventive-service',
        sourceServiceId: orderData.preventiveServiceId,
        isCorrectiveMaintenance: true,
      },
    };

    const response = await axios.post(
      `${URL_API}/vendor-platform/corrective-maintenance/orders`,
      apiOrderData,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );



    // Transform API response to match our expected format
    const apiResponse = response.data;

    return {
      success: true,
      data: apiResponse.data || apiResponse,
      message: apiResponse.message || 'Orden de mantenimiento correctivo creada exitosamente',
    };
  } catch (error: any) {

    // Handle different types of errors
    if (error.response) {
      // Server responded with error status
      const errorData = error.response.data;
      const errorMessage = errorData?.message || 'Error del servidor';
      const specificError = errorData?.error || '';



      // Check for specific error about vehicle already having active order
      const isVehicleAlreadyActive = specificError === 'Vehicle already has an active corrective maintenance order' ||
                                    specificError.includes('Vehicle already has an active corrective maintenance order') ||
                                    errorMessage.includes('Vehicle already has an active corrective maintenance order');

      return {
        success: false,
        data: errorData || null,
        message: isVehicleAlreadyActive
          ? 'Vehicle already has an active corrective maintenance order'
          : errorMessage,
      };
    } else if (error.request) {
      // Request was made but no response received
      return {
        success: false,
        data: null as any,
        message: 'Error de conexión con el servidor',
      };
    } else {
      // Something else happened
      return {
        success: false,
        data: null as any,
        message: 'Error inesperado al crear la orden',
      };
    }
  }
}
