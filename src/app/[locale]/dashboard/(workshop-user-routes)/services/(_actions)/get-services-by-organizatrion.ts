'use server';

import axios from 'axios';
import { IService } from '../../vehicles/[stockId]/_actions/getServicesByVehicleId';
import { URL_API } from '@/constants';
import getUserById, { getSession } from '@/actions/getUserById';


export const getServicesByOrganizationId = async ({ page, limit }: { page: number, limit: number } = { page: 0, limit: 10 }) => {
  try {
    // Try to get the session first
    const session = await getSession();
    let token = '';
    let organizationId = '';

    if (session?.user?.accessToken) {
      console.log("Using session token for services query");
      token = session.user.accessToken;
      organizationId = (session.user as any).organizationId || '';
    } else {
      // If no session, try to get the user
      const user = await getUserById();
      if (!user) {
        console.error("No user found for services query");
        return null;
      }

      console.log("Using user token for services query");
      token = user.accessToken;
      organizationId = user.organizationId;
    }

    if (!organizationId) {
      console.error("No organizationId found for services query");
      return null;
    }

    const url = new URL(`${URL_API}/vendor-platform/organizations/${organizationId}/services`);

    url.searchParams.append('page', page.toString());
    url.searchParams.append('limit', limit.toString());

    console.log("Fetching services from:", url.toString());
    const res = await axios.get(url.toString(), {
      headers: {
        Authorization: 'Bearer ' + token,
        'Content-Type': 'application/json'
      },
    });
    return res.data as {
      data: IService[];
      totalPages: number;
      totalResults: number;
    }
  } catch (error: any) {
    console.error('Error getting services', error.response?.data || error);
    return null;
  }
}