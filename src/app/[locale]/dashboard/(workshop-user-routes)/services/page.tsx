import { Pagination } from '@/components/Pagination';
import { getServicesByOrganizationId } from './(_actions)/get-services-by-organizatrion'
import ServicesClientPage from './client-page'
import PaginationV2 from '@/components/PaginationV2';

export const metadata = {
  title: 'Servicios',
  description: 'Página de servicios',
}

interface ServicesPageProps {
  searchParams: {
    page: number;
    limit: number;
  }
}


export default async function ServicesPage({ searchParams }: ServicesPageProps) {

  const page = Number(searchParams.page) || 0;
  const limit = Number(searchParams.limit) || 10;

  const result = await getServicesByOrganizationId({ page, limit });

  if (!result) {
    return (
      <div>
        <h1>Something went wrong</h1>
      </div>
    )
  }


  return (
    <>
      <ServicesClientPage services={result.data} />

      <PaginationV2
        totalRecords={result.totalResults}
        limit={limit}
      />

    </>
  )
}
