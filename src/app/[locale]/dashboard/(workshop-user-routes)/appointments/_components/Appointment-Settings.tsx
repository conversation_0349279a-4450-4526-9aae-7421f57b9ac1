'use client'

import { use<PERSON><PERSON>, Controller } from "react-hook-form"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Clock, Plus, RotateCcw, X } from 'lucide-react'
import { useQuery } from '@tanstack/react-query'
import { useCurrentUser } from '@/Providers/CurrentUserProvider'
import axios from 'axios'
import { DAYS_OF_WEEK_FULL, URL_API } from '@/constants'
import { useEffect } from 'react'
import ScheduleOverrideForm from './ScheduleOverrideForm'
import { appointmentService } from '../_actions/appointmentService'
import { useToast } from '@chakra-ui/react'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
// import type { ScheduleConfig, TimeRange, WeeklySchedule, WorkshopCapacity } from "./types"

// const DAYS_OF_WEEK = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] as const


const ScheduleConfigSchema = z.object({
  weeklySchedule: z.object({
    monday: z.object({
      start: z.string(),
      end: z.string()
    }).optional(),
    tuesday: z.object({
      start: z.string(),
      end: z.string()
    }).optional(),
    wednesday: z.object({
      start: z.string(),
      end: z.string()
    }).optional(),
    thursday: z.object({
      start: z.string(),
      end: z.string()
    }).optional(),
    friday: z.object({
      start: z.string(),
      end: z.string()
    }).optional(),
    saturday: z.object({
      start: z.string(),
      end: z.string()
    }).optional(),
    sunday: z.object({
      start: z.string(),
      end: z.string()
    }).optional()
  }).optional(),
  timezone: z.string().optional(),
  bufferTime: z.number().optional(),

  // refine in a way that breaktime and capacity objects are optional,
  // and just if they are present, they should have the start and end properties as strings
  // or the totalBays and techniciansPerBay properties as numbers

  breakTime: z.object({
    start: z.string().optional(),
    end: z.string().optional()
  }).optional(),

  capacity: z.object({
    totalBays: z.number().optional(),
    techniciansPerBay: z.number().optional()
  }).optional()
})

interface AppointmentSettingsProps {
  data: any
  isLoading: boolean
}

export default function AppointmentSettings({ data, isLoading }: AppointmentSettingsProps) {

  const toast = useToast();
  const { user } = useCurrentUser();

  const defaultValues = {
    weeklySchedule: {
      monday: undefined,
      tuesday: undefined,
      wednesday: undefined,
      thursday: undefined,
      friday: undefined,
      saturday: undefined,
      sunday: undefined
    },

  }

  const { control, handleSubmit, watch, setValue, ...rest } = useForm<ScheduleConfig>({
    defaultValues,
    resolver: zodResolver(ScheduleConfigSchema)
  })

  const errors = rest.formState.errors

  useEffect(() => {
    if (data) {
      rest.reset(data); // Esto actualizará todo el formulario con los nuevos datos
    }
  }, [data, rest.reset]);

  const weeklySchedule = watch('weeklySchedule')

  const onSubmit = async (data: ScheduleConfig) => {
    // console.log(data)

    try {

      const response = await appointmentService.updateOrganizationSchedule(user.organizationId, data)

      console.log(response)

      toast({
        title: "Configuración guardada",
        description: "Recargando la página...",
        status: "success",
        duration: 3000,
        isClosable: true,
        position: 'top',
      })


      setTimeout(() => {

        return window.location.reload();

      }, 3100);

    } catch (error) {

      console.error(error)
      toast({
        title: "Error al guardar la configuración",
        description: "Por favor intenta de nuevo",
        status: "error",
        duration: 3000,
        isClosable: true,
        position: 'top',
      })

    }

  }

  const handleToggleDay = (day: keyof WeeklySchedule) => {
    if (weeklySchedule[day]) {
      setValue(`weeklySchedule.${day}`, undefined)
    } else {
      setValue(`weeklySchedule.${day}`, { start: "09:00", end: "17:00" })
    }
  }

  const handleReset = (day: keyof WeeklySchedule) => {
    setValue(`weeklySchedule.${day}`, { start: "09:00", end: "17:00" })
  }

  if (isLoading) {
    return <div>Loading...</div>
  }

  return (
    <>

      <div className="">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <h1 className="text-xl font-semibold">
              Configuración global de citas
            </h1>
          </div>
          <Button type="submit" onClick={handleSubmit(onSubmit)} className="bg-[#6C2BD9] hover:bg-[#5a24b5]">
            Guardar
          </Button>
        </div>

        <Card className="border-0 shadow-none p-6">
          <CardContent className="space-y-6 p-0">
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-neutral-900">
                Configura la disponibilidad de los talleres de forma global
              </h3>
              {DAYS_OF_WEEK_FULL.map(({ label, value: day }) => {
                return (
                  <div key={day} className="grid grid-cols-[120px_1fr] gap-4 items-center pl-6">
                    <Label className="capitalize text-sm font-normal">{label}</Label>
                    <div className="flex items-center gap-4">
                      {weeklySchedule?.[day]?.start ? (
                        <>
                          <div className="flex items-center gap-2">
                            <Controller
                              name={`weeklySchedule.${day}.start`}
                              control={control}
                              render={({ field }) => (
                                <Input
                                  {...field}
                                  type="time"
                                  className="w-32 border-neutral-200"
                                  placeholder="Start Time"
                                />
                              )}
                            />
                          </div>
                          <div className="flex items-center gap-2">
                            <Controller
                              name={`weeklySchedule.${day}.end`}
                              control={control}
                              render={({ field }) => (
                                <Input
                                  {...field}
                                  type="time"
                                  className="w-32 border-neutral-200"
                                  placeholder="End Time"
                                />
                              )}
                            />
                          </div>
                          <div className="flex gap-2">
                            <Button
                              type="button"
                              size="icon"
                              variant="ghost"
                              onClick={() => handleToggleDay(day)}
                              className="text-neutral-500 hover:text-neutral-900"
                            >
                              <X className="w-4 h-4" />
                            </Button>
                            <Button
                              type="button"
                              size="icon"
                              variant="ghost"
                              onClick={() => handleReset(day)}
                              className="text-neutral-500 hover:text-neutral-900"
                            >
                              <RotateCcw className="w-4 h-4" />
                            </Button>
                          </div>
                        </>
                      ) : (
                        <div className="flex gap-2">
                          <Button
                            type="button"
                            size="icon"
                            variant="ghost"
                            onClick={() => handleToggleDay(day)}
                            className="text-neutral-500 hover:text-neutral-900"
                          >
                            <Plus className="w-4 h-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>

            <div className="space-y-4">
              <h3 className="text-sm font-medium text-neutral-900">Tiempo de descanso/comida</h3>
              <div className="flex items-center gap-4 pl-6">
                <div className="flex items-center gap-2">
                  <Controller
                    name="breakTime.start"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="time"
                        className="w-32 border-neutral-200"
                        placeholder="Start Time"
                      />
                    )}
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Controller
                    name="breakTime.end"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="time"
                        className="w-32 border-neutral-200"
                        placeholder="End Time"
                      />
                    )}
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    size="icon"
                    variant="ghost"
                    className="text-neutral-500 hover:text-neutral-900"
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                  <Button
                    type="button"
                    size="icon"
                    variant="ghost"
                    className="text-neutral-500 hover:text-neutral-900"
                  >
                    <RotateCcw className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-sm font-medium text-neutral-900">Zona horaria</h3>
              <div className='pl-6'>

                <Controller
                  name="timezone"
                  control={control}
                  render={({ field }) => {

                    return (
                      <Select onValueChange={field.onChange} value={field.value}>
                        <SelectTrigger className="w-full border-neutral-200">
                          <SelectValue placeholder="Select timezone" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="America/Mexico_City">Mexico City</SelectItem>
                          <SelectItem value="America/New_York">New York</SelectItem>
                          <SelectItem value="America/Los_Angeles">Los Angeles</SelectItem>
                        </SelectContent>
                      </Select>
                    )
                  }}
                />
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-sm font-medium text-neutral-900">Capacidad de talleres en general</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-normal">Total de Bahías</Label>
                  <Controller
                    name="capacity.totalBays"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="number"
                        onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value, 10) : '')}
                        className="border-neutral-200"
                      />
                    )}
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-normal">
                    Técnicos por bahía
                  </Label>
                  <Controller
                    name="capacity.techniciansPerBay"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="number"
                        onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value, 10) : '')}
                        className="border-neutral-200"
                      />
                    )}
                  />
                </div>
              </div>
            </div>

          </CardContent>
        </Card>
      </div>

      <div className='my-20 '>

        <ScheduleOverrideForm />
      </div>

    </>
  )
}

