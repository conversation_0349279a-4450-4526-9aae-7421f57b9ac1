/* eslint-disable @typescript-eslint/no-explicit-any */
// src/components/AppointmentScheduler.tsx
import { useState, useEffect } from 'react';
import { Clock, ChevronLeft, ChevronRight } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { DateTime } from 'luxon';
import { Associate } from '@/actions/getDriverById';
import { VehicleResponse } from '@/actions/getVehicleData';
import { useToast } from '@chakra-ui/react';
import { useParams } from 'next/navigation';
import { appointmentService } from '../_actions/appointmentService';

interface ReescheduleAppointmentProps {
  workshopId: string;
  serviceType: ServiceType;
  onClose?: () => void;
  appointmentId: string;
}

export default function ReescheduleAppointment({
  workshopId,
  serviceType,
  appointmentId,
  onClose,
}: ReescheduleAppointmentProps) {
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [availableSlots, setAvailableSlots] = useState<string[]>([]);
  const [selectedSlot, setSelectedSlot] = useState<string | null>(null);
  const [step, setStep] = useState<1 | 2 | 3>(1);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');


  const DAYS: string[] = ['Dom', 'Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb'];
  const MONTHS: string[] = [
    'Enero',
    'Febrero',
    'Marzo',
    'Abril',
    'Mayo',
    'Junio',
    'Julio',
    'Agosto',
    'Septiembre',
    'Octubre',
    'Noviembre',
    'Diciembre',
  ];

  useEffect(() => {
    const fetchAvailableSlots = async (): Promise<void> => {
      try {
        setError('');
        const dateStr = selectedDate?.toISOString().split('T')[0] ?? '';

        const { data: slots } = await appointmentService.getAvailableSlots(
          workshopId,
          dateStr,
          serviceType._id
        );
        setAvailableSlots(slots);
      } catch (stackError: any) {
        console.error(stackError);
        setError('Error al cargar horarios disponibles');
      }
    };

    if (selectedDate) {
      fetchAvailableSlots();
    }
  }, [selectedDate, workshopId, serviceType._id]);

  const getDaysInMonth = (date: Date): DayInfo[] => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);

    const days: DayInfo[] = [];
    const firstDayIndex = firstDay.getDay();

    for (let i = 0; i < firstDayIndex; i++) {
      days.push({ date: null, disabled: true });
    }

    for (let i = 1; i <= lastDay.getDate(); i++) {
      const currentDate = new Date(year, month, i);
      const isToday = new Date().toDateString() === currentDate.toDateString();
      const isPast = currentDate < new Date(new Date().setHours(0, 0, 0, 0));

      days.push({
        date: currentDate,
        disabled: isPast,
        isToday,
      });
    }

    return days;
  };

  const handlePreviousMonth = (): void => {
    setCurrentMonth((prevMonth) => {
      const newMonth = new Date(prevMonth);
      newMonth.setMonth(newMonth.getMonth() - 1);
      return newMonth;
    });
  };

  const handleNextMonth = (): void => {
    setCurrentMonth((prevMonth) => {
      const newMonth = new Date(prevMonth);
      newMonth.setMonth(newMonth.getMonth() + 1);
      return newMonth;
    });
  };

  const handleDateSelect = (dayInfo: DayInfo): void => {
    if (!dayInfo.date || dayInfo.disabled) return;
    setSelectedDate(dayInfo.date);
    setStep(2);
  };

  const handleTimeSelect = (slot: string): void => {
    setSelectedSlot(slot);
    setStep(3);
  };

  const toast = useToast();

  const handleBookAppointment = async () => {
    try {
      setError('');
      if (!selectedSlot) {
        throw new Error('Por favor selecciona un horario');
      }


      await appointmentService.reescheduleAppointment(appointmentId, selectedSlot, serviceType._id);

      setSuccess('¡Cita reagendada exitosamente!');
      setStep(1);
      setSelectedDate(null);
      setSelectedSlot(null);

      if (onClose) onClose();

      return toast({
        title: '¡Cita agendada exitosamente!',
        status: 'success',
        position: 'top',
        duration: 3000,
        isClosable: true,
      });
    } catch (stackError: any) {
      setError(stackError instanceof Error ? stackError.message : 'Error al agendar la cita');
      return toast({
        title: 'Error al agendar la cita',
        status: 'error',
        position: 'top',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const renderCalendar = () => (
    <div className="">
      <div className="flex items-center justify-between mb-4">
        <Button variant="outline" size="icon" onClick={handlePreviousMonth}>
          <ChevronLeft className="w-4 h-4" />
        </Button>
        <h2 className="text-lg font-semibold">
          {MONTHS[currentMonth.getMonth()]} {currentMonth.getFullYear()}
        </h2>
        <Button variant="outline" size="icon" onClick={handleNextMonth}>
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>

      <div className="grid grid-cols-7 gap-1 text-center">
        {DAYS.map((day) => (
          <div key={day} className="font-medium text-sm py-2">
            {day}
          </div>
        ))}
      </div>

      <div className="grid grid-cols-7 gap-1">
        {getDaysInMonth(currentMonth).map((day, index) => (
          <Button
            key={index}
            variant={selectedDate?.toDateString() === day.date?.toDateString() ? 'default' : 'ghost'}
            disabled={day.disabled}
            className={`h-12 ${day.isToday ? 'border-2 border-primary' : ''} ${!day.date ? 'invisible' : ''}`}
            onClick={() => handleDateSelect(day)}
          >
            {day.date?.getDate()}
          </Button>
        ))}
      </div>
    </div>
  );

  const renderTimeSlots = () => {
    if (!selectedDate) return null;

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">
            Horarios disponibles para{' '}
            {selectedDate.toLocaleDateString('es-ES', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })}
          </h2>
          <Button
            variant="ghost"
            onClick={() => {
              setStep(1);
              setError('');
              setAvailableSlots([]);
            }}
          >
            Cambiar fecha
          </Button>
        </div>

        {/* MAKE A GRID AUTO ADJUSTABLE WITH WIDTH SIZE */}
        {/* <div className="grid grid-cols-3 gap-2"> */}
        <div className="grid gap-2 grid-cols-[repeat(auto-fit,minmax(120px,1fr))]">
          {availableSlots.map((slot) => {
            // Convertir el slot de la zona horaria del servidor a la zona horaria local
            const localTime = DateTime.fromISO(slot)
              .setZone('local') // Esto usa la zona horaria del navegador
              .toFormat('HH:mm'); // Formato 24 horas

            return (
              <Button key={slot} variant="outline" className="p-4" onClick={() => handleTimeSelect(slot)}>
                <Clock className="w-4 h-4 mr-2" />
                {localTime}
              </Button>
            );
          })}
        </div>
      </div>
    );
  };

  const renderCustomerForm = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">Datos</h2>
        <Button variant="ghost" onClick={() => setStep(2)}>
          Cambiar horario
        </Button>
      </div>


      <Button className="w-full" type="button" onClick={handleBookAppointment}>
        Confirmar Reagendar Cita
      </Button>
    </div>
  );

  return (
    <Card className="w-full max-w-xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">

        </CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="bg-green-50 mb-4">
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        {step === 1 && renderCalendar()}
        {step === 2 && renderTimeSlots()}
        {step === 3 && renderCustomerForm()}
      </CardContent>
    </Card>
  );
}
