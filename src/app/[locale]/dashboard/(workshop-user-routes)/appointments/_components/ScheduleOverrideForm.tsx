"use client"

import { useF<PERSON>, Controller, useWatch } from "react-hook-form"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Checkbox } from "@/components/ui/checkbox"
import { format } from "date-fns"
import { CalendarIcon, Clock, Plus, X } from "lucide-react"
import { useEffect, useState } from "react"
import { appointmentService } from '../_actions/appointmentService'
import { useCurrentUser } from '@/Providers/CurrentUserProvider'
import { useRouter } from 'next/navigation'
import { useToast } from '@chakra-ui/react'

export default function ScheduleOverrideForm() {
  const { control, handleSubmit, watch, setValue, reset } = useForm<ScheduleOverrideFormData>({
    defaultValues: {
      type: "MODIFIED",
      scope: "SINGLE",
      isDateRange: false,
      isWorkshop: false,
    },
  })

  const { user } = useCurrentUser();
  const router = useRouter();
  const toast = useToast();

  const [showWorkingHours, setShowWorkingHours] = useState(false)
  const [showBreakTime, setShowBreakTime] = useState(false)
  const [showCapacity, setShowCapacity] = useState(false)

  // const type = watch("type")
  // const isDateRange = watch("isDateRange")
  const type = useWatch({ control, name: "type" })
  const isDateRange = useWatch({ control, name: "isDateRange" })

  useEffect(() => {
    if (type === "BLOCKED") {
      setShowWorkingHours(false)
      setShowBreakTime(false)
      setShowCapacity(false)
      setValue("modifiedSchedule", undefined)
    }
  }, [type, setValue])

  const onSubmit = async (data: ScheduleOverrideFormData) => {
    if (!isDateRange && data.startDate) {
      data.endDate = data.startDate
    }
    if (data.type === "BLOCKED") {
      delete data.modifiedSchedule
    }

    try {

      if (data.isWorkshop) {
        const response = await appointmentService.createWorkshopScheduleOverride(selectedWorkshop._id, data);
      }

      const response = await appointmentService.createScheduleOverride(user.organizationId, data);


      toast({
        title: "Horario creado",
        description: "El horario ha sido creado exitosamente.",
        status: "success",
        duration: 3000,
        isClosable: true,
        position: 'top',
      })

      reset({
        name: "",
        description: "",
        isDateRange: false,
        startDate: null,
        endDate: null,
        type: "MODIFIED",
        scope: "SINGLE",
        modifiedSchedule: undefined
      })

      // return router.refresh();

      setTimeout(() => {
        return window.location.reload();
      }, 3100);


    } catch (error) {

      toast({
        title: "Error",
        description: "Ha ocurrido un error al crear el horario.",
        status: "error",
        duration: 3000,
        isClosable: true,
        position: 'top',
      })

    }
  }


  const [selectedWorkshop, setSelectedWorkshop] = useState<any | null>(null);
  const [workshops, setWorkshops] = useState<any[]>([]);

  useEffect(() => {

    // validate if isWorkshop is true to fetch workshops
    if (watch("isWorkshop")) {
      const fetchWorkshops = async () => {
        const { data } = await appointmentService.getOrganizationWorkshops(user.organizationId);
        setWorkshops(data);
      }

      fetchWorkshops();
    }
  }, [watch("isWorkshop")])



  return (
    <>
      <div className="">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Clock className="w-5 h-5" />
            <h1 className="text-xl font-semibold">Sobreescribir horario por día o rango de fechas</h1>
          </div>
          <Button type="submit" onClick={handleSubmit(onSubmit)} className="bg-[#6C2BD9] hover:bg-[#5a24b5]">
            Guardar
          </Button>
        </div>

        <Card className="border-0 shadow-none p-6">
          <CardContent className="space-y-6 p-0">

            <div className="flex items-center space-x-2">
              <Controller
                name="isWorkshop"
                control={control}
                render={({ field }) => (
                  // <Checkbox id="isDateRange" checked={field.value} onCheckedChange={field.onChange} />
                  <Checkbox id="isWorkshop" checked={field.value} onCheckedChange={field.onChange} />
                )}
              />
              <Label htmlFor="isWorkshop" className="text-sm font-medium text-neutral-900">
                {/* Para taller */}
                Crear para taller
                <span className="text-xs text-neutral-500 pl-3">
                  (Creado para la Organización por defecto)
                </span>
              </Label>
            </div>
            {
              watch("isWorkshop") && workshops.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-neutral-900">Taller</Label>
                  <Select onValueChange={setSelectedWorkshop}>
                    <SelectTrigger className="border-neutral-200">
                      <SelectValue>{selectedWorkshop ? selectedWorkshop.name : "Seleccionar taller"}</SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      {workshops.map((workshop) => (
                        <SelectItem key={workshop._id} value={workshop}>
                          {workshop.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )
            }

            <div className="grid gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-neutral-900">Nombre</Label>
                <Controller
                  name="name"
                  control={control}
                  rules={{ required: true }}
                  render={({ field }) => <Input {...field} className="border-neutral-200" />}
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-neutral-900">Descripción</Label>
                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => <Textarea {...field} className="border-neutral-200 min-h-[100px]" />}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Controller
                  name="isDateRange"
                  control={control}
                  render={({ field }) => (
                    <Checkbox id="isDateRange" checked={field.value} onCheckedChange={field.onChange} />
                  )}
                />
                <Label htmlFor="isDateRange" className="text-sm font-medium text-neutral-900">
                  Rango de fechas
                </Label>
              </div>

              <div className={`grid ${isDateRange ? "grid-cols-2" : "grid-cols-1"} gap-4`}>
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-neutral-900">
                    {isDateRange ? "Fecha de Inicio" : "Fecha"}
                  </Label>
                  <Controller
                    name="startDate"
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={`w-full justify-start text-left font-normal border-neutral-200 ${!field.value && "text-muted-foreground"}`}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {field.value ? format(field.value, "PPP") : "Seleccionar fecha"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar mode="single" selected={field.value ?? undefined} onSelect={field.onChange} initialFocus />
                        </PopoverContent>
                      </Popover>
                    )}
                  />
                </div>
                {isDateRange && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-neutral-900">Fecha de Fin</Label>
                    <Controller
                      name="endDate"
                      control={control}
                      rules={{ required: isDateRange }}
                      render={({ field }) => (
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className={`w-full justify-start text-left font-normal border-neutral-200 ${!field.value && "text-muted-foreground"}`}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {field.value ? format(field.value, "PPP") : "Seleccionar fecha"}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <Calendar mode="single" selected={field.value ?? undefined} onSelect={field.onChange} initialFocus />
                          </PopoverContent>
                        </Popover>
                      )}
                    />
                  </div>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-neutral-900">Tipo</Label>
                  <Controller
                    name="type"
                    control={control}
                    render={({ field }) => (
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger className="border-neutral-200">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="BLOCKED">Bloqueado (Bloquea el día o rango de fechas seleccionado)</SelectItem>
                          <SelectItem value="MODIFIED">Modificado (Modifica el día o rango de fechas seleccionado) </SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-neutral-900">Alcance</Label>
                  <Controller
                    name="scope"
                    control={control}
                    render={({ field }) => (
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger className="border-neutral-200">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="SINGLE">Una vez</SelectItem>
                          <SelectItem value="YEARLY">Anual</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              </div>

              {type === "MODIFIED" && (
                <>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="text-sm font-medium text-neutral-900">Horas de trabajo</h3>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setShowWorkingHours(!showWorkingHours)
                          if (!showWorkingHours) {
                            setValue("modifiedSchedule.workingHours", { start: "", end: "" })
                          } else {
                            setValue("modifiedSchedule.workingHours", undefined)
                          }
                        }}
                      >
                        {showWorkingHours ? <X className="h-4 w-4" /> : <Plus className="h-4 w-4" />}
                      </Button>
                    </div>
                    {showWorkingHours && (
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4 text-neutral-500" />
                          <Controller
                            name="modifiedSchedule.workingHours.start"
                            control={control}
                            render={({ field }) => <Input {...field} type="time" className="w-32 border-neutral-200" />}
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4 text-neutral-500" />
                          <Controller
                            name="modifiedSchedule.workingHours.end"
                            control={control}
                            render={({ field }) => <Input {...field} type="time" className="w-32 border-neutral-200" />}
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="text-sm font-medium text-neutral-900">Tiempo de descanso</h3>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setShowBreakTime(!showBreakTime)
                          if (!showBreakTime) {
                            setValue("modifiedSchedule.breakTime", { start: "", end: "" })
                          } else {
                            setValue("modifiedSchedule.breakTime", undefined)
                          }
                        }}
                      >
                        {showBreakTime ? <X className="h-4 w-4" /> : <Plus className="h-4 w-4" />}
                      </Button>
                    </div>
                    {showBreakTime && (
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4 text-neutral-500" />
                          <Controller
                            name="modifiedSchedule.breakTime.start"
                            control={control}
                            render={({ field }) => <Input {...field} type="time" className="w-32 border-neutral-200" />}
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4 text-neutral-500" />
                          <Controller
                            name="modifiedSchedule.breakTime.end"
                            control={control}
                            render={({ field }) => <Input {...field} type="time" className="w-32 border-neutral-200" />}
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="text-sm font-medium text-neutral-900">Capacidad</h3>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setShowCapacity(!showCapacity)
                          if (!showCapacity) {
                            setValue("modifiedSchedule.capacity", { totalBays: 0, techniciansPerBay: 0 })
                          } else {
                            setValue("modifiedSchedule.capacity", undefined)
                          }
                        }}
                      >
                        {showCapacity ? <X className="h-4 w-4" /> : <Plus className="h-4 w-4" />}
                      </Button>
                    </div>
                    {showCapacity && (
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label className="text-sm font-normal">Total de Bahías</Label>
                          <Controller
                            name="modifiedSchedule.capacity.totalBays"
                            control={control}
                            render={({ field }) => <Input {...field} type="number" className="border-neutral-200" />}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label className="text-sm font-normal">Técnicos por Bahía</Label>
                          <Controller
                            name="modifiedSchedule.capacity.techniciansPerBay"
                            control={control}
                            render={({ field }) => <Input {...field} type="number" className="border-neutral-200" />}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
      <ListScheduleOrganizationOverrides />

      <ListScheduleWorkshopOverrides />

    </>
  )
}

interface IOverride {
  _id: string;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  active: boolean;
}

function ListScheduleOrganizationOverrides() {

  const { user } = useCurrentUser();

  const [overrides, setOverrides] = useState<IOverride[]>([]);

  useEffect(() => {
    const fetchOverrides = async () => {
      const { data } = await appointmentService.getScheduleOverrides(user.organizationId);
      setOverrides(data);
    }

    fetchOverrides();
  }, [])



  return (
    <div className="space-y-6 my-20">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Clock className="w-5 h-5" />
          <h1 className="text-xl font-semibold">Horarios sobreescritos (Organización)</h1>
        </div>
      </div>

      <Card className="border-0 shadow-none p-6">
        <CardContent className="space-y-6 p-0">
          <div className="grid gap-4">
            {overrides?.map((override) => (
              <div key={override._id} className="flex flex-col lg:flex-row lg:items-center justify-between gap-y-3 border border-neutral-200 p-4 rounded-lg">
                <div className="flex flex-col lg:flex-row lg:items-center  gap-4">
                  <div className="flex items-center gap-2">
                    <CalendarIcon className="w-5 h-5" />
                    <div>
                      <h3 className="text-sm font-medium text-neutral-900">{override.name}</h3>
                      <p className="text-xs text-neutral-500">{override.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    <div className='flex gap-4'>
                      <div>
                        <p className="text-xs text-neutral-500">Inicio</p>
                        <p className="text-xs text-neutral-500">{override.startDate.split('T')[0]}</p>
                      </div>
                      <div>
                        <p className="text-xs text-neutral-500">Fin</p>
                        <p className="text-xs text-neutral-500">{override.endDate.split('T')[0]}</p>
                      </div>
                    </div>



                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <Button variant="ghost" size="sm" className="text-neutral-500">
                    Editar
                  </Button>

                  {/* {
                    override.active && (
                      <Button variant="ghost" size="sm" className="text-red-500">
                        Desactivar
                      </Button>
                    )
                  }

                  {
                    !override.active && (
                      <Button variant="ghost" size="sm" className="text-green-500">
                        Activar
                      </Button>
                    )
                  } */}

                  <Button variant="ghost" size="sm" className="text-red-500">
                    Eliminar
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )

}


function ListScheduleWorkshopOverrides() {

  const { user } = useCurrentUser();

  const [workshops, setWorkshops] = useState<any[]>([]);
  const [selectedWorkshop, setSelectedWorkshop] = useState<any | null>(null);
  const [overrides, setOverrides] = useState<IOverride[]>([]);


  useEffect(() => {
    if (selectedWorkshop) {
      const fetchOverrides = async () => {
        const { data } = await appointmentService.getScheduleWorkshopOverrides(selectedWorkshop._id);
        setOverrides(data);
      }

      fetchOverrides();
    }
  }, [selectedWorkshop])

  useEffect(() => {
    const fetchWorkshops = async () => {
      const { data } = await appointmentService.getOrganizationWorkshops(user.organizationId);
      setWorkshops(data);
    }

    fetchWorkshops();
  }, [])

  return (
    <div className="space-y-6 my-20">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Clock className="w-5 h-5" />
          <h1 className="text-xl font-semibold">Horarios sobreescritos (Taller)</h1>
        </div>
      </div>

      <Card className="border-0 shadow-none p-6">
        <CardContent className="space-y-6 p-0">
          <div className="grid gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-neutral-900">Taller</Label>
              <Select onValueChange={setSelectedWorkshop}>
                <SelectTrigger className="border-neutral-200">
                  <SelectValue>{selectedWorkshop ? selectedWorkshop.name : "Seleccionar taller"}</SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {workshops?.map((workshop) => (
                    <SelectItem key={workshop._id} value={workshop}>
                      {workshop.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {selectedWorkshop && (
              <div className="grid gap-4 mt-8">
                {overrides.map((override) => (
                  <div key={override._id} className="flex flex-col lg:flex-row lg:items-center justify-between gap-y-3 border border-neutral-200 p-4 rounded-lg">
                    <div className="flex flex-col lg:flex-row lg:items-center gap-4">
                      <div className="flex items-center gap-2">
                        <CalendarIcon className="w-5 h-5" />
                        <div>
                          <h3 className="text-sm font-medium text-neutral-900">{override.name}</h3>
                          <p className="text-xs text-neutral-500">{override.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="w-5 h-5" />
                        <div className='flex gap-4'>
                          <div>
                            <p className="text-xs text-neutral-500">Inicio</p>
                            <p className="text-xs text-neutral-500">{override.startDate.split('T')[0]}</p>
                          </div>
                          <div>
                            <p className="text-xs text-neutral-500">Fin</p>
                            <p className="text-xs text-neutral-500">{override.endDate.split('T')[0]}</p>
                          </div>

                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <Button variant="ghost" size="sm" className="text-neutral-500">
                        Editar
                      </Button>
                      <Button variant="ghost"
                        size="sm"
                        onClick={async () => {
                          // await appointmentService.deleteScheduleOverride(selectedWorkshop.id, override.id);
                          // setOverrides(overrides.filter((o) => o.id !== override.id));
                        }}
                        className="text-red-500">
                        Eliminar
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}