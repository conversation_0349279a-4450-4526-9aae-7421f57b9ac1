import { Associate } from '@/actions/getDriverById';
import { VehicleResponse } from '@/actions/getVehicleData';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { DateTime } from "luxon";
import Swal from 'sweetalert2';
import { appointmentService } from '../_actions/appointmentService';
import { useToast } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { useAppointmentModal } from '@/zustand/modalStates';
import FormikContainer from '@/components/Formik/FormikContainer';
import SelectInput from '@/components/Inputs/SelectInput';
import ReescheduleAppointment from './RescheduleAppointment';
import { useParams } from 'next/navigation';
import ModalContainer from '@/components/Modals/ModalContainer';
import { statusColorMap } from './CalendarPreview';
import { cn } from '@/lib/utils';

export const MapStatus = {
  scheduled: 'Agendada',
  completed: 'Completada',
  canceled: 'Cancelada',
  'not-attended': 'No asistida',
  rescheduled: 'Reagendada',
}

export interface EventModalProps {
  isOpen: boolean;
  onClose: () => void;
  organizationTimezone?: string;
  event: {
    id: string;
    status: string
    title: string;
    start: Date;
    end: Date;
    associate: Associate;
    stock: VehicleResponse;
    workshop: any;
    service: {
      name: string;
      description?: string;
      duration: number;
      color: string;
    };
    data: any;
  } | null;
}

export function EventModal({ isOpen, onClose, event, organizationTimezone }: EventModalProps) {
  const [open, setOpen] = useState(isOpen);
  const onOpen = () => setOpen(true);
  const onClose2 = () => setOpen(false);
  const appointmentModal = useAppointmentModal();
  const params = useParams<{ locale: string }>();
  const toast = useToast();

  const onOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      onClose();
    }
  }

  useEffect(() => {
    setOpen(isOpen);
  }, [isOpen]);

  if (!event) return null;

  // Convert Dates to Luxon DateTime objects
  const startDateTime = DateTime.fromJSDate(event.start).setLocale("es");
  const endDateTime = DateTime.fromJSDate(event.end).setLocale("es");

  const stockId = event.stock._id;

  const nowDevice = DateTime.now().setZone('utc');

  const appointmentStart = DateTime.fromJSDate(event.start).setZone(organizationTimezone);

  const isPastAppointment = nowDevice.toMillis() > appointmentStart.toMillis();

  // const isDisabledEvent = event.status === 'canceled' || event.status === 'not-attended';
  const isDisabledEvent = statusColorMap[event.status] ? true : false;

  console.log('color text: ', `text-[${statusColorMap[event.status]}]`)
  // console.log('event', event);
  return (
    <>

      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className='  sm:w-3/4 lg:w-3/5 xl:w-1/3'>
        <DialogHeader>
            <DialogTitle>
              Detalles de la Cita
              {isDisabledEvent && (
                <span className={cn(
                  `ml-2 text-sm font-normal`,
                )}
                  style={{
                    color: statusColorMap[event.status]
                  }}
                >
                  {event.status === 'canceled' ? '(Cancelada)' : '(No asistida)'}
                </span>
              )}
            </DialogTitle>
        </DialogHeader>
          <div className={`grid gap-4 pt-4 grid-cols-1 ${isDisabledEvent ? 'opacity-60 pointer-events-none' : ''}`}>
          <div className="grid grid-cols-4 items-center gap-4">
            <div className="col-span-4 space-y-4">
              <div>
                  <h3 className="font-medium">
                    Servicio en taller {event.workshop?.name}
                  </h3>
                <p style={{
                  color: event.service?.color || '#3B82F6'

                }}>{event.service.name}</p>
                {event.service.description && (
                  <p className="text-sm text-gray-500">{event.service.description}</p>
                )}
              </div>
                {/* SHOW STATUS OF THE APPOINTMENT */}

                <div className='flex gap-4'>
                  <div>
                    <h3 className="font-medium">Estado de la cita</h3>
                    <p className="text-sm text-gray-500">
                      {
                        MapStatus[event.status as keyof typeof MapStatus] || event.status
                      }
                      {/* {isPastAppointment ? 'Pasada' : 'Pendiente'} */}
                    </p>
                  </div>
                  <div>
                    <h3 className="font-medium">ID de la cita</h3>
                    <p className="text-sm text-gray-500">{event.id}</p>
                  </div>
                </div>


              <div>
                <h3 className="font-medium">Cliente</h3>
                  <p className="text-sm text-gray-500">{event.associate.firstName + ' ' + event.associate.lastName}</p>
                <p className="text-sm text-gray-500">{event.associate.email}</p>
                <p className="text-sm text-gray-500">{event.associate.phone}</p>
              </div>

              <div>
                <h3 className="font-medium">Vehículo</h3>
                <p className="text-sm text-gray-500">
                  {event.stock.brand + event.stock.model} | {event.stock.carNumber}
                  {event.stock.extensionCarNumber ? `- ${event.stock.extensionCarNumber}` : ''}
                </p>
                <p className="text-sm text-gray-500">Placas: {event.stock.carPlates.plates}</p>
                <p className="text-sm text-gray-500">Vin: {event.stock.vin}</p>
                <p className="text-sm text-gray-500">
                  Kilometraje registrado: {event.data.registeredKm} km
                </p>
              </div>

              <div>
                <h3 className="font-medium">Horario</h3>
                <p className="text-sm text-gray-500">
                  {startDateTime.toLocaleString({ weekday: 'long', day: 'numeric', month: 'long', year: 'numeric' })}
                </p>
                <p className="text-sm text-gray-500">
                  {startDateTime.toFormat('HH:mm')} - {endDateTime.toFormat('HH:mm')}
                </p>
                <p className="text-sm text-gray-500">
                  Duración: {event.service.duration} minutos
                </p>
              </div>
            </div>
          </div>

            <div className="col-span-1 flex flex-col md:flex-row gap-x-3 justify-end space-y-4 md:space-y-0">
              {event.status !== 'completed' && (
                <>
                  {!isPastAppointment && (
                    <>
                      <Button
                        className='bg-red-500 hover:bg-red-600 text-white'
                        onClick={() => {
                          appointmentModal.onOpen();
                          onClose2();
                        }}
                      >
                        Reagendar
                      </Button>
                      <CancelAppointmentModal appointmentId={event.id} onClose={onClose2} onOpen={onOpen} />
                    </>
                  )}
                  {isPastAppointment && event.status !== 'not-attended' && event.status !== 'completed' && (
                    <Button
                      className='bg-red-500 hover:bg-red-600 text-white'
                      onClick={async () => {

                        onClose2();
                        const { isConfirmed } = await Swal.fire({
                          title: '¿Estás seguro  de marcar la cita como no asistida?',
                          text: 'Esta acción no se puede deshacer',
                          icon: 'warning',
                          showCancelButton: true,
                          confirmButtonColor: '#d33',
                          cancelButtonColor: '#3085d6',
                          confirmButtonText: 'Sí, marcar como no asistida',
                          cancelButtonText: 'Cancelar',

                        });

                        if (isConfirmed) {
                          try {
                            await appointmentService.notAttendedAppointment(event.id);

                            toast({
                              title: 'Cita marcada como no asistida con éxito',
                              description: 'Actualizando la página',
                              status: 'success',
                              position: 'top',
                              duration: 3000,
                              isClosable: true,
                            });

                            onClose();

                            setTimeout(() => {
                              window.location.reload();
                            }, 3100);

                          } catch (error: any) {
                            console.error(error);
                            toast({
                              title: 'Error al cancelar la cita',
                              description: error?.message || 'Ocurrió un error al cancelar la cita',
                              status: 'error',
                              position: 'top',
                              duration: 3000,
                              isClosable: true,
                            });
                          }
                          onOpen();
                          return null;
                        } else {
                          onOpen();
                        }

                      }}
                    >
                      No asistió
                    </Button>
                  )}

                  {
                    (event.status === 'scheduled' || event.status === 'rescheduled') && (
                      <Button
                        className='bg-primaryPurple hover:bg-primaryBtnHover text-white'
                        onClick={() => {
                          window.open(`/${params.locale}/dashboard/vehicles/${stockId}?addService=true`, '_blank');
                          const serviceKeyName = `service-${stockId}`;
                          localStorage.setItem(serviceKeyName, JSON.stringify(event));
                        }}
                      >
                        Crear Servicio
                      </Button>
                    )
                  }
                </>
              )}
            </div>
        </div>
      </DialogContent>
    </Dialog>
      <CreateNextAppointmentModal event={event} onCloseEventModal={onClose2} onOpenEventModal={onOpen} />

    </>
  );
}

interface CreateNextAppointmentModalProps {
  event: NonNullable<EventModalProps['event']>;
  onCloseEventModal: () => void;
  onOpenEventModal: () => void;
}
/**
 * CreateNextAppointmentModal - Componente para reagendar citas
 * 
 * Este componente permite al usuario reagendar una cita existente seleccionando:
 * 1. Un taller (workshop) de la lista de talleres disponibles
 * 2. Un tipo de servicio para la nueva cita
 * 
 * Una vez seleccionados estos datos, muestra el componente ReescheduleAppointment
 * que permite seleccionar una nueva fecha y hora para la cita.
 * 
 * @param event - Datos de la cita actual que se va a reagendar
 * @param onCloseEventModal - Función para cerrar el modal de evento principal
 * @param onOpenEventModal - Función para abrir el modal de evento principal
 */
function CreateNextAppointmentModal({ event, onCloseEventModal, onOpenEventModal }: CreateNextAppointmentModalProps) {

  const { onOpen, onClose, isOpen } = useAppointmentModal();

  const [workshopOptions, setWorkshopOptions] = useState([]);
  const [serviceTypes, setServiceTypes] = useState([]);
  const [selectedWorkshop, setSelectedWorkshop] = useState<any>();
  const [selectedServiceType, setSelectedServiceType] = useState<any>();

  useEffect(() => {
    if (isOpen) {
      const fetchWorkshops = async () => {
        try {
          const { data } = await appointmentService.getAllWorkshops();
          setWorkshopOptions(data);
        } catch (error: any) {
          console.error(error);
        }
      };

      fetchWorkshops();
    }
  }, [isOpen]);

  useEffect(() => {
    if (selectedWorkshop && selectedWorkshop.name) {
      const fetchServiceTypes = async () => {
        try {
          const { data } = await appointmentService.getServiceTypes(selectedWorkshop.organizationId);
          setServiceTypes(data);
        } catch (error: any) {
          console.error(error);
        }
      };

      fetchServiceTypes();
    }
  }, [selectedWorkshop]);

  if (!isOpen) {
    return null;
  }

  return (
    <>
      <Button
        className='bg-primaryPurple hover:bg-primaryBtnHover text-white'
        onClick={() => {
          onOpen();
        }}
      >
        Reagendar
      </Button>
      <ModalContainer title="Calendario de disponibilidad" onClose={() => {
        onClose();
        onOpenEventModal();
      }}>
        <FormikContainer
          hideFooter
          onSubmit={async (values) => {
            // console.log(values);
          }}
          initialValues={{
            workshop: selectedWorkshop
              ? { label: selectedWorkshop.name, value: selectedWorkshop._id }
              : { label: 'Selecciona', value: '' },
            serviceType: selectedServiceType
              ? { label: selectedServiceType.name, value: selectedServiceType._id }
              : { label: 'Selecciona', value: '' },
          }}
        >
          <div className="flex flex-col gap-4">
            <SelectInput
              label="Taller"
              name="workshop"
              options={workshopOptions.map((workshop: any) => ({
                label: workshop.name,
                value: workshop._id,
              }))}
              onChange={(option, form) => {
                const findWorkshop = workshopOptions.find((workshop: any) => workshop._id === option.value);
                if (findWorkshop) {
                  setSelectedWorkshop(findWorkshop);
                }
                setSelectedServiceType(null);
                form.setFieldValue('serviceType', { label: 'Selecciona', value: '' });
              }}
            />

            <SelectInput
              label="Tipo de servicio"
              name="serviceType"
              options={serviceTypes.map((serviceType: any) => ({
                label: serviceType.name,
                value: serviceType._id,
              }))}
              onChange={(option) => {
                // setSelectedServiceType(option);
                const findServiceType = serviceTypes.find(
                  (serviceType: any) => serviceType._id === option.value
                );
                if (findServiceType) {
                  setSelectedServiceType(findServiceType);
                }
              }}
            />

            {selectedWorkshop?._id && selectedServiceType?._id && (
              <div /* className='absolute top-0 right-0 w-full h-full' */ >

                <ReescheduleAppointment
                  workshopId={selectedWorkshop._id}
                  serviceType={selectedServiceType}
                  appointmentId={event.id}
                  onClose={() => {
                    onClose();
                    onCloseEventModal();
                  }}
                />
              </div>
            )}
          </div>
        </FormikContainer>
      </ModalContainer>
    </>
  );
}

interface CancelAppointmentModalProps {
  appointmentId: string;
  onClose: () => void;
  onOpen: () => void;
}

// cancel appointment modal, this shows a confirmation message to cancel the appointment
function CancelAppointmentModal({ appointmentId, onClose, onOpen }: CancelAppointmentModalProps) {

  const toast = useToast();

  const confirmCancel = async () => {

    onClose();

    const { isConfirmed } = await Swal.fire({
      title: '¿Estás seguro de cancelar la cita?',
      text: 'Esta acción no se puede deshacer',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Sí, cancelar cita',
      cancelButtonText: 'Cancelar',
      // show this modal in z-index 9999

    });

    if (isConfirmed) {
      try {
        const response = await appointmentService.cancelAppointment(appointmentId);
        onClose();

        toast({
          title: 'Cita cancelada con éxito',
          status: 'success',
          duration: 3000,
          isClosable: true,
          position: 'top',
        });

        const removeReaload = localStorage.getItem('removeReaload') === 'true';
        if (removeReaload) {
          console.log('Response of cancel appointment', response);
          return;
        }
        setTimeout(() => {
          window.location.reload();
        }, 3100);

      } catch (error: any) {

        toast({
          title: 'Error al cancelar la cita',
          description: error.message || 'Ocurrió un error al cancelar la cita',
          status: 'error',
          duration: 3000,
          isClosable: true,
          position: 'top',
        });
      }
    } else {
      onOpen();
    }
  };

  return (
    <Button
      className='bg-red-500 hover:bg-red-600 text-white'
      onClick={confirmCancel}
    >
      Cancelar Cita
    </Button>
  );





}
