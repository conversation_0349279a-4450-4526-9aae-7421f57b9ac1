/* eslint-disable @typescript-eslint/no-explicit-any */
import { Calendar, Formats, luxonLocalizer, Messages, Event, View, NavigateAction } from 'react-big-calendar';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { DateTime } from 'luxon';
import { useEffect, useState, useRef, useCallback } from 'react';
import { EventModal } from './EventModal';
import { appointmentService } from '../_actions/appointmentService';
import { useCurrentUser } from '@/Providers/CurrentUserProvider';
import getUserById, { getSession } from '@/actions/getUserById';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import AppointmentSettings from './Appointment-Settings';
import { useRouter, useSearchParams } from 'next/navigation';
import { convertTimeToZoneToday, shouldRefetchByMonth } from '../_utils/dates';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { debounce } from 'lodash';
import { Button } from "@/components/ui/button"
import { RefreshCw } from "lucide-react"

const months = [
  'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
  'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
];

const messages: Messages = {
  date: 'Fecha',
  time: 'Hora',
  event: 'Evento',
  allDay: 'Todo el día',
  week: 'Semana',
  work_week: 'Semana laboral',
  day: 'Día',
  month: 'Mes',
  previous: 'Anterior',
  next: 'Siguiente',
  yesterday: 'Ayer',
  tomorrow: 'Mañana',
  today: 'Hoy',
  agenda: 'Agenda',

  showMore: (total: number) => `+${total} más`,

  noEventsInRange: 'No hay citas en este rango.',
};

const formats: Formats = {
  dateFormat: 'dd',
  dayFormat: (date: Date) => {
    const day = DateTime.fromJSDate(date).setLocale('es').toFormat('dd ccc');
    const [dayNum, dayName] = day.split(' ');
    return `${dayNum} ${dayName.charAt(0).toUpperCase() + dayName.slice(1)}`;
  },
  dayHeaderFormat: (date: Date) => {
    const day = DateTime.fromJSDate(date).setLocale('es').toFormat('cccc d');
    const [dayName, dayNum] = day.split(' ');
    const month = DateTime.fromJSDate(date).setLocale('es').toFormat('MMMM');
    const monthFirstUpper = month.charAt(0).toUpperCase() + month.slice(1);
    return `${dayName.charAt(0).toUpperCase() + dayName.slice(1)} ${dayNum} de ${monthFirstUpper}`;
  },
  monthHeaderFormat: (date: Date) => {
    const month = DateTime.fromJSDate(date).setLocale('es').toFormat('MMMM yyyy');
    const [monthName, year] = month.split(' ');
    return `${monthName.charAt(0).toUpperCase() + monthName.slice(1)} ${year}`;
  },
  dayRangeHeaderFormat: ({ start, end }: any) => {


    const startDate = DateTime.fromJSDate(start);
    const endDate = DateTime.fromJSDate(end);

    // Si es el mismo mes
    if (startDate.month === endDate.month) {
      return `${startDate.day} - ${endDate.day} de ${months[startDate.month - 1]}`;
    }

    // Si son meses diferentes
    return `${startDate.day} de ${months[startDate.month - 1]} - ${endDate.day} de ${months[endDate.month - 1]}`;
  },

  agendaDateFormat: (date: Date) => DateTime.fromJSDate(date).setLocale('es').toFormat('ccc d'),
  agendaHeaderFormat: ({ start, end }: { start: Date; end: Date }) => {
    const startDate = DateTime.fromJSDate(start).setLocale('es').toFormat('ccc d');
    const endDate = DateTime.fromJSDate(end).setLocale('es').toFormat('ccc d');

    const [startDay, startNum] = startDate.split(' ');
    const [endDay, endNum] = endDate.split(' ');
    const startMonth = months[DateTime.fromJSDate(start).month - 1];
    const endMonth = months[DateTime.fromJSDate(end).month - 1];

    return `${startDay} ${startNum}, ${startMonth} - ${endDay} ${endNum}, ${endMonth}`;


  },
  agendaTimeFormat: 'hh:mm a',
  agendaTimeRangeFormat: ({ start, end }: any) =>
    `${DateTime.fromJSDate(start).toFormat('HH:mm')} - ${DateTime.fromJSDate(end).toFormat('HH:mm')}`,

};



const mapAppointmentsToEvents = (appointments: any[]) => {
  return appointments.map((appointment: any) => {
    return {
      id: appointment._id,
      status: appointment.status,
      title: `${appointment.associate.firstName} - ${appointment.associate.email}`,
      start: new Date(appointment.startTime),
      end: new Date(appointment.endTime),
      data: appointment.data,
      associate: appointment.associate,
      workshop: appointment.workshop,
      stock: appointment.stock,
      service: appointment.service,
    };
  });
}


function checkIfMinAndMaxAreDifferentDays(min: Date, max: Date) {
  const minDate = DateTime.fromJSDate(min);
  const maxDate = DateTime.fromJSDate(max);

  return minDate.hasSame(maxDate, 'day');
}

export const statusColorMap: Record<string, string> = {
  canceled: '#EF4444',
  'not-attended': '#F59E0B',
}


export default function CalendarPreview({ view, setView }: { view: View, setView: (view: View) => void }) {
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [locale] = useState('es-MX');
  const params = useSearchParams();
  const router = useRouter();
  const [min, setMin] = useState(new Date("2025-01-02T09:00:00"));
  const [max, setMax] = useState(new Date("2025-01-02T18:00:00"));
  const deviceTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const queryClient = useQueryClient();
  const lastRefetchRef = useRef<AbortController | null>(null);

  const { user } = useCurrentUser();
  const organizationId = user.organizationId;

  const currentDate = new Date();
  const start = params.get('start');
  const end = params.get('end');

  // Inicialización de rangeDates similar a InstallationScheduleClientPage
  const [rangeDates, setRangeDates] = useState<{ start: Date, end: Date }>(() => {
    if (start && end) {
      return {
        start: new Date(start),
        end: new Date(end)
      };
    }

    const initialDate = DateTime.fromJSDate(currentDate);

    // Especificar que la semana comienza en domingo (weekday: 7)
    const startOfWeek = initialDate.startOf('week').minus({ days: 1 });
    const endOfWeek = startOfWeek.plus({ days: 6 }).endOf('day');

    return {
      start: startOfWeek.toJSDate(),
      end: endOfWeek.toJSDate()
    };
  });

  const getQueryKey = (start: Date, end: Date) => ['appointments', organizationId, start.toISOString(), end.toISOString()];

  const debouncedRefetch = useCallback(
    debounce((start: Date, end: Date) => {
      if (lastRefetchRef.current) {
        lastRefetchRef.current.abort();
      }

      const abortController = new AbortController();
      lastRefetchRef.current = abortController;

      queryClient.invalidateQueries({
        queryKey: getQueryKey(start, end),
        exact: true
      });
    }, 500),
    [queryClient]
  );

  const { data: events = [], isLoading } = useQuery({
    queryKey: getQueryKey(rangeDates.start, rangeDates.end),
    queryFn: async ({ signal }) => {
      const start = DateTime.fromJSDate(rangeDates.start).toISO()!;
      const end = DateTime.fromJSDate(rangeDates.end).toISO()!;

      const { data } = await appointmentService.getOrganizationAppointments(
        organizationId,
        start.split('T')[0],
        end.split('T')[0],
      );

      return mapAppointmentsToEvents(data);
    },
    staleTime: 1000 * 60 * 5, // 5 minutos
  });

  useEffect(() => {
    return () => {
      if (lastRefetchRef.current) {
        lastRefetchRef.current.abort();
      }
    };
  }, []);

  const handleNavigate = async (date: Date, view: View, action: NavigateAction) => {
    let start: Date;
    let end: Date;

    try {
      switch (view) {
        case 'agenda':
          start = DateTime.fromJSDate(date).toJSDate();
          end = DateTime.fromJSDate(date).plus({ days: 30 }).toJSDate();
          break;
        case 'week':
          start = DateTime.fromJSDate(date).startOf('week').toJSDate();
          end = DateTime.fromJSDate(date).endOf('week').toJSDate();
          break;
        case 'day':
          start = DateTime.fromJSDate(date).startOf('day').toJSDate();
          end = DateTime.fromJSDate(date).endOf('day').toJSDate();
          break;
        case 'month':
        default:
          start = DateTime.fromJSDate(date).startOf('month').toJSDate();
          end = DateTime.fromJSDate(date).endOf('month').toJSDate();
          break;
      }

      if (!DateTime.fromJSDate(start).isValid || !DateTime.fromJSDate(end).isValid) {
        console.error('Invalid date generated');
        return;
      }

      setRangeDates({ start, end });
      debouncedRefetch(start, end);
    } catch (error) {
      console.error('Error handling navigation:', error);
    }
  };

  const handleRangeChange = (range: any, view?: View) => {
    if (!range) return;

    try {
      let start: Date;
      let end: Date;

      if (Array.isArray(range)) {
        start = range[0];
        end = range[range.length - 1];
      } else {
        start = range.start;
        end = range.end;
      }

      // Actualizar inmediatamente el rango visual
      setRangeDates({ start, end });

      // Debounce el refetch
      debouncedRefetch(start, end);
    } catch (error) {
      console.error('Error handling range change:', error);
    }
  };


  const eventStyleGetter = (event: any) => {
    // const isNotAttended = event.status === 'not-attended';

    const statusColor = statusColorMap[event.status];
    return {
      style: {
        '--event-color': event.workshop?.color || '#3B82F6',
        borderRadius: '4px',
        borderColor: '#000',
        borderWidth: '.13em',
        // opacity: isCanceled || isNotAttended ? '0.6' : '1',
        // textDecoration: isCanceled ? 'line-through' : 'none',
        // backgroundColor: isCanceled ? '#f3f4f6' : undefined,
        // borderLeft: isCanceled ? '5px solid #EF4444' : undefined,
        borderLeft: statusColor ? `5px solid ${statusColor}` : undefined,
      }
    };
  };


  const { data, isLoading: isLoadingScheduleConfig } = useQuery({
    queryKey: ['schedule-config', user.organizationId],
    queryFn: async () => {
      try {

        const { data } = await appointmentService.getOrganizationSchedule(user.organizationId)
        return data
      } catch (error) {

        return null;

      }
    },
  })
  // const deviceTimezone = 'Asia/Karachi'; // Hardcoded for testing purposes

  // Settings.defaultZone = deviceTimezone;

  useEffect(() => {
    if (data) {
      const mondayStartAt = data.weeklySchedule.monday.start;
      const mondayEndAt = data.weeklySchedule.monday.end;
      const configTimezone = data.timezone;


      const newMin = convertTimeToZoneToday(mondayStartAt, configTimezone, deviceTimezone);
      const newMax = convertTimeToZoneToday(mondayEndAt, configTimezone, deviceTimezone);

      // add 1 hour to newMax
      newMax.setHours(newMax.getHours() + 1);

      setMin(newMin);
      setMax(newMax);

    }
  }, [data]);

  const handleRefresh = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: getQueryKey(rangeDates.start, rangeDates.end),
      exact: true
    });
  }, [queryClient, rangeDates]);

  return (
    <div className="space-y-4">
      <div className="flex justify-end mb-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={isLoading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Actualizar
        </Button>
      </div>

      <TabsContent value="calendar" className='w-full mt-8'>
        {view === 'month' && (
          <style>
            {`
              .rbc-row-content {
                height: 95px !important;
              }
            `}
          </style>
        )}

        <Calendar
          localizer={luxonLocalizer(DateTime)}
          events={events}
          defaultDate={currentDate}
          min={checkIfMinAndMaxAreDifferentDays(min, max) ? min : undefined}
          max={checkIfMinAndMaxAreDifferentDays(min, max) ? max : undefined}
          culture={locale}
          defaultView={view}
          onView={(view) => setView(view)}
          messages={messages}
          formats={formats}
          eventPropGetter={eventStyleGetter}
          onSelectEvent={(event: any) => setSelectedEvent(event)}
          onNavigate={handleNavigate}
          onRangeChange={handleRangeChange}
          components={{
            timeGutterHeader: () => {
              return (
                <>
                  {isLoading && (
                    <div className="calendar-loader">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
                    </div>
                  )}
                </>
              );
            }
          }}
        />
      </TabsContent>
      <TabsContent value="settings" className='w-full mt-8'>
        <AppointmentSettings data={data} isLoading={isLoadingScheduleConfig} />
      </TabsContent>

      {/* Modal del evento */}
      {selectedEvent && (
        <EventModal
          isOpen={!!selectedEvent}
          onClose={() => setSelectedEvent(null)}
          event={selectedEvent}
          organizationTimezone={data?.timezone}
        />
      )}
    </div>
  );
}
