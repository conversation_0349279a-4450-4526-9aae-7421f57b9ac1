import { URL_API } from '@/constants';
import axios from 'axios';


export const apiVendorPlatform = axios.create({
  baseURL: URL_API + '/vendor-platform',
  headers: {
    'Content-Type': 'application/json',
  }
});

export const appointmentService = {

  getServiceTypes: async (organizationId: string) => {
    const { data } = await apiVendorPlatform.get(`/organizations/${organizationId}/service-types`);
    return data;
  },

  getAvailableSlots: async (workshopId: string, date: string, serviceTypeId: string) => {
    // console.log('workshopId', workshopId, 'date', date);
    const { data } = await apiVendorPlatform.get(`/workshops/${workshopId}/available-slots/${date}/${serviceTypeId}`);
    // console.log('data', data);
    return data;
  },


  getWorkshopAppointments: async (workshopId: string, startDate?: string, endDate?: string) => {

    const url = new URL(`${apiVendorPlatform.defaults.baseURL}/workshops/${workshopId}/appointments`);

    if (startDate) {
      url.searchParams.append('startDate', startDate);
    } else {
      const current01Date = new Date();
      current01Date.setDate(1);
      startDate = current01Date.toISOString().split('T')[0];
    }

    if (endDate) {
      url.searchParams.append('endDate', endDate);
    } else {
      const currentLastDate = new Date();
      currentLastDate.setMonth(currentLastDate.getMonth() + 1);
      currentLastDate.setDate(0);
      endDate = currentLastDate.toISOString().split('T')[0];
    }


    const { data } = await apiVendorPlatform.get(url.toString());

    return data;
  },

  getOrganizationAppointments: async (organizationId: string, startDate?: string, endDate?: string) => {

    const url = new URL(`${apiVendorPlatform.defaults.baseURL}/organizations/${organizationId}/appointments`);
    if (startDate) {
      url.searchParams.append('startDate', startDate);
    } else {
      const current01Date = new Date();
      current01Date.setDate(1);
      startDate = current01Date.toISOString().split('T')[0];
    }

    if (endDate) {
      url.searchParams.append('endDate', endDate);
    } else {
      const currentLastDate = new Date();
      currentLastDate.setMonth(currentLastDate.getMonth() + 1);
      currentLastDate.setDate(0);
      endDate = currentLastDate.toISOString().split('T')[0];
    }

    const { data } = await apiVendorPlatform.get(url.toString());

    return data;
  },


  createScheduleOverride: async (organizationId: string, overrideData: any) => {
    const { data } = await apiVendorPlatform.post(`/organizations/${organizationId}/overrides`, overrideData);
    return data;
  },

  getScheduleOverrides: async (organizationId: string) => {
    const { data } = await apiVendorPlatform.get(`/organizations/${organizationId}/overrides`);
    return data;
  },

  createWorkshopScheduleOverride: async (workshopId: string, overrideData: any) => {
    const { data } = await apiVendorPlatform.post(`/workshops/${workshopId}/overrides`, overrideData);
    return data;
  },

  getScheduleWorkshopOverrides: async (workshopId: string) => {
    console.log('workshopId', workshopId);
    const { data } = await apiVendorPlatform.get(`/workshops/${workshopId}/overrides`);
    return data;
  },

  getOrganizationWorkshops: async (organizationId: string) => {
    // organizationId = '67645197aa94cae787d469d8'
    // console.log('organizationId', organizationId, '67645197aa94cae787d469d8' === organizationId);
    const { data } = await apiVendorPlatform.get(`/organizations/${organizationId}/workshops`);
    return data;

  },


  getOrganizationSchedule: async (organizationId: string) => {
    const { data } = await apiVendorPlatform.get(`/organizations/${organizationId}/schedule`);
    return data;
  },

  updateOrganizationSchedule: async (organizationId: string, scheduleData: any) => {
    const { data } = await apiVendorPlatform.put(`/organizations/${organizationId}/schedule`, scheduleData);
    return data;
  },


  getAllWorkshops: async () => {
    console.log('headers', apiVendorPlatform.defaults.headers);

    const { data } = await apiVendorPlatform.get(`/workshops`);
    return data;
  },

  getServiceTypesByOrganization: async (organizationId: string) => {
    const { data } = await apiVendorPlatform.get(`/organizations/${organizationId}/service-types`);
    return data;
  },


  reescheduleAppointment: async (appointmentId: string, startTime: string, serviceTypeId: string) => {
    const { data } = await apiVendorPlatform.put(`/appointments/${appointmentId}/reschedule`, { startTime, serviceTypeId });
    return data;
  },


  cancelAppointment: async (appointmentId: string) => {
    const { data } = await apiVendorPlatform.delete(`/appointments/${appointmentId}/cancel`);
    return data;
  },

  notAttendedAppointment: async (appointmentId: string) => {
    const { data } = await apiVendorPlatform.patch(`/appointments/${appointmentId}/not-attended`);
    return data;
  }

};