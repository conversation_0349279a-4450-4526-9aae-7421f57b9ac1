import { DateTime } from 'luxon';
import { format, utcToZonedTime, zonedTimeToUtc } from 'date-fns-tz';


export const shouldRefetchByMonth = (start1: string, end1: string, start2: string, end2: string): boolean => {
  const startMonth1 = DateTime.fromISO(start1).toFormat('yyyy-MM'); // Formato año-mes
  const endMonth1 = DateTime.fromISO(end1).toFormat('yyyy-MM'); // Formato año-mes
  const startMonth2 = DateTime.fromISO(start2).toFormat('yyyy-MM'); // Formato año-mes
  const endMonth2 = DateTime.fromISO(end2).toFormat('yyyy-MM'); // Formato año-mes

  // Comparamos los meses (año y mes) para decidir si necesitamos refetch
  return startMonth1 !== startMonth2 || endMonth1 !== endMonth2;
};



/**
 * Convierte una hora de una zona horaria a otra y crea una fecha con el día de hoy.
 * 
 * @param time - La hora en formato "HH:mm".
 * @param fromZone - Zona horaria original (por ejemplo, "America/Mexico_City").
 * @param toZone - Zona horaria destino (por ejemplo, "UTC").
 * @returns La fecha de hoy con la hora transformada en la zona horaria destino.
 */
export function convertTimeToZoneToday(
  time: string,
  fromZone: string,
  toZone: string
): Date {
  // Crear un objeto DateTime a partir de la hora y la zona de origen
  const [hours, minutes] = time.split(':').map(Number);

  const today = DateTime.now(); // Fecha actual en la zona local
  const sourceTime = DateTime.fromObject(
    {
      year: today.year,
      month: today.month,
      day: today.day,
      hour: hours,
      minute: minutes,
    },
    { zone: fromZone }
  );

  // Convertir a la zona horaria de destino
  const targetTime = sourceTime.setZone(toZone);

  // Retornar como una fecha nativa de JavaScript
  return targetTime.toJSDate();
}

export function convertTimeBetweenTimezones(time: string, sourceTimezone: string, targetTimezone: string): string {
  // Crear una fecha arbitraria (por ejemplo, el 1 de enero de 2025) con la hora proporcionada en la zona horaria de origen
  const [hours, minutes] = time.split(':').map(Number);
  const dateInSourceTZ = new Date(Date.UTC(2025, 0, 1, hours, minutes));

  // Obtener la diferencia de tiempo entre UTC y la zona horaria de origen
  const sourceOffset = new Intl.DateTimeFormat('en-US', {
    timeZone: sourceTimezone,
    hour: 'numeric',
    hour12: false,
  }).formatToParts(dateInSourceTZ).find(part => part.type === 'hour')?.value ?? 0 - hours;

  // Ajustar la fecha a UTC considerando la diferencia de la zona horaria de origen
  const utcDate = new Date(dateInSourceTZ.getTime() - Number(sourceOffset) * 60 * 60 * 1000);

  // Obtener la hora en la zona horaria de destino
  const targetTime = new Intl.DateTimeFormat('en-US', {
    timeZone: targetTimezone,
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  }).format(utcDate);

  return targetTime;
}
