'use client';
import { useState } from 'react';
import CalendarPreview from './_components/CalendarPreview';
import { View } from 'react-big-calendar';
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useRouter, useSearchParams } from 'next/navigation';
const availableTabs = ['calendar', 'settings'];

export default function AppointmentsClientPage() {
  const [view, setView] = useState<View>('week');
  const router = useRouter();
  const searchParams = useSearchParams();
  const tabValue = searchParams.get('tab');


  const tab = tabValue && availableTabs.includes(tabValue) ? tabValue : 'calendar';

  return (
    <>
      <div className={`flex flex-col gap-4 calendar-container ${view}-view`}>
        <Tabs defaultValue={tab} className='w-full h-full'
          onValueChange={(value) => {
            // console.log('value', value);
            if (value === 'calendar') {
              // remove tab from url
              router.push('/dashboard/appointments');
            }
            else {
              router.push('/dashboard/appointments?tab=' + value);
            }
          }}
        >
          <TabsList>
            <TabsTrigger value="calendar">Calendario</TabsTrigger>
            <TabsTrigger value="settings">Ajustes</TabsTrigger>
          </TabsList>
          <CalendarPreview view={view} setView={setView} />
        </Tabs>
      </div>
    </>
  )
}
