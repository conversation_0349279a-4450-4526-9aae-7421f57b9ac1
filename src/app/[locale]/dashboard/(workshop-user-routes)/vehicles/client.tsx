'use client';
import { useState } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { LayoutGrid, List, Filter } from "lucide-react";
import VehicleGrid from './(components)/vehicleGrid';
import VehicleList from './(components)/vehicleList';
import VehicleSearcher from './(components)/searcher';
import { VehicleDataCard } from './_actions/getVehiclesQuery';


export default function VehiclesClientPage({ vehicles }: { vehicles: VehicleDataCard[] }) {
  const [viewMode, setViewMode] = useState('grid');

  return (
    <div className="p-4 md:p-6 bg-white w-full min-h-[80vh] flex flex-col">
      <div className="flex flex-col gap-4 md:flex-row md:justify-between md:items-center mb-6">

        <VehicleSearcher
          hideTitle
          placeholder="Buscar por placa o vin"
        />


        <section id="vehicles-container" className="flex items-center gap-2">
          <div className="mr-4">
            <Button
              variant="outline"
              size="sm"
              className="text-gray-500 gap-2"
            >
              <Filter className="h-4 w-4" />
              Filter
            </Button>
          </div>
          <Button
            variant="outline"
            size="icon"
            className={`p-2 ${viewMode === 'list' ? 'bg-purple-600 text-white' : ''}`}
            onClick={() => setViewMode('list')}
          >
            <List className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className={`p-2 ${viewMode === 'grid' ? 'bg-purple-600 text-white' : ''}`}
            onClick={() => setViewMode('grid')}
          >
            <LayoutGrid className="h-4 w-4" />
          </Button>
        </section>
      </div>

      {viewMode === 'grid' ? (
        <VehicleGrid vehicles={vehicles} />
      ) : (
        <VehicleList vehicles={vehicles} />
      )}
    </div>
  );
}