import React from 'react';
import VehiclesClientPage from './client';
import { getSearchQuery } from './_actions/getVehiclesQuery';

export default async function VehiclePage({ searchParams: { q } }: { searchParams: { q: string } }) {
  const vehicles = await getSearchQuery(q, 'q');

  return (
    <section className='flex flex-col min-h-[90vh]' >
      <div className='pb-4'>

        <p className='font-bold text-3xl' >
          Vehicles
        </p>
      </div>

      <VehiclesClientPage vehicles={vehicles || []} />
    </section>
  );
};
