import React from 'react';
import { Box, Text, RadioGroup, VStack, Radio } from '@chakra-ui/react';

interface StatusOption {
  value: string;
  label: string;
}

interface StatusSelectionSectionProps {
  nextStepOptions: StatusOption[];
  selectedNextStatus: string | null;
  setSelectedNextStatus: (val: string) => void;
  translatedText: {
    selectNextStatus: string;
  };
  getModalOptionLabel: (statusValue: string | null) => string;
  borderColor: string;
  primaryButtonColorScheme: string;
  textColor: string;
}

const StatusSelectionSection: React.FC<StatusSelectionSectionProps> = ({
  nextStepOptions,
  selectedNextStatus,
  setSelectedNextStatus,
  translatedText,
  getModalOptionLabel,
  borderColor,
  primaryButtonColorScheme,
  textColor,
}) => (
  <Box mt={2} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
    <Text color={textColor} mb={3} fontWeight="medium">
      {translatedText.selectNextStatus}
    </Text>
    <RadioGroup
      onChange={setSelectedNextStatus}
      value={selectedNextStatus || undefined}
      colorScheme={primaryButtonColorScheme}
    >
      <VStack spacing={3} align="stretch">
        {nextStepOptions.map((option) => (
          <Radio
            key={option.value}
            value={option.value}
            colorScheme={primaryButtonColorScheme}
            size="md"
          >
            {getModalOptionLabel(option.value)}
          </Radio>
        ))}
      </VStack>
    </RadioGroup>
  </Box>
);

export default StatusSelectionSection; 