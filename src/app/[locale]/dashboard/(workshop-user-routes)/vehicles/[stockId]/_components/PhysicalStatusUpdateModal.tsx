'use client';

import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalBody,
  ModalCloseButton,
  Text,
  VStack,
  Spinner,
  Alert,
  AlertIcon,
  useColorModeValue,
  Flex,
  Button,
} from '@chakra-ui/react';
import { confirmQrStatusChange, uploadQrScanPhoto, getQrScanHistory } from '../../_actions/vehicleQrService';
import PhotoCaptureSection from './PhotoCaptureSection';
import StatusSelectionSection from './StatusSelectionSection';
import { PHYSICAL_STATUS_MAP, translatedText, photoTexts, PhysicalStatus } from '../../../../../../../constants/vehiclePhysicalStatus';

const getStatusLabel = (statusValue: PhysicalStatus | string | null): string => {
  if (!statusValue) return 'N/A';
  return PHYSICAL_STATUS_MAP[statusValue as PhysicalStatus] || statusValue;
};

// Custom function for modal option labels
const getModalOptionLabel = (statusValue: PhysicalStatus | string | null): string => {
  if (!statusValue) return 'N/A';
  if (statusValue === PhysicalStatus.AVAILABLE_IN_STOCK) {
    return 'Inspección Completa';
  } else if (statusValue === PhysicalStatus.VEHICLE_TO_BE_REPAIRED) {
    return 'Inspección Fallida';
  }
  return getStatusLabel(statusValue);
};

interface PhysicalStatusUpdateModalProps {
  isOpen: boolean;
  onClose: () => void;
  vehicleId: string;
  currentStatus: string;
  nextStatusToDisplay: string | null;
  nextStepOptions?: { value: string; label: string }[];
  confirmationToken: string | null;
  message: string | null;
  actionAvailable: boolean;
  onStatusConfirmed: (newStatus: string) => void;
}

const PhysicalStatusUpdateModal: React.FC<PhysicalStatusUpdateModalProps> = ({
  isOpen,
  onClose,
  vehicleId,
  currentStatus,
  nextStatusToDisplay,
  nextStepOptions,
  confirmationToken,
  actionAvailable,
  onStatusConfirmed,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedNextStatus, setSelectedNextStatus] = useState<PhysicalStatus | null>(null);
  
  // Camera and photo state variables
  const [photoPreviewUrl, setPhotoPreviewUrl] = useState<string | null>(null);
  const [photoPath, setPhotoPath] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [isCameraAvailable, setIsCameraAvailable] = useState(false);
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const streamRef = useRef<MediaStream | null>(null);

  // Style values
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const headerColor = useColorModeValue('gray.700', 'white');
  const textColor = useColorModeValue('gray.600', 'gray.200');
  const strongTextColor = useColorModeValue('gray.800', 'white');
  const primaryButtonColorScheme = 'purple';

  // Effect to set initial selected status if options are present
  useEffect(() => {
    if (isOpen) {
      setError(null); // Reset error on open
      if (nextStepOptions && nextStepOptions.length > 0) {
        setSelectedNextStatus(null); // Reset selection when options are present
      } else if (nextStatusToDisplay) {
        setSelectedNextStatus(nextStatusToDisplay as PhysicalStatus);
      } else {
        setSelectedNextStatus(null);
      }
    }
  }, [isOpen, nextStepOptions, nextStatusToDisplay]);

  // Cleanup media stream when component unmounts or modal closes
  useEffect(() => {
    return () => {
      if (streamRef.current) {
        const tracks = streamRef.current.getTracks();
        tracks.forEach(track => track.stop());
        streamRef.current = null;
      }
    };
  }, []);

  // Stop camera when modal closes
  useEffect(() => {
    if (!isOpen && streamRef.current) {
      const tracks = streamRef.current.getTracks();
      tracks.forEach(track => track.stop());
      streamRef.current = null;
      setIsCameraOpen(false);
      setIsCameraAvailable(false);
      setPhotoPreviewUrl(null);
      setPhotoPath(null);
    }
  }, [isOpen]);

  const openCamera = async () => {
    try {
      setIsCameraOpen(true); // Set camera open first to render the video element
      
      // Give time for the video element to be available in the DOM
      setTimeout(async () => {
        try {
          // Stop any existing stream
          if (streamRef.current) {
            streamRef.current.getTracks().forEach(track => track.stop());
            streamRef.current = null;
          }

          // Simple attempt to access camera
          // Detect if the device is mobile
          const isMobile = /Android|iPhone|iPad|iPod/i.test(navigator.userAgent);
          const videoConstraints = isMobile
            ? { facingMode: { exact: "environment" } }
            : true;

          const stream = await navigator.mediaDevices.getUserMedia({ 
            video: videoConstraints
          });
          
          streamRef.current = stream;
          
          if (videoRef.current) {
            videoRef.current.srcObject = stream;
            
            // Play when loaded and ready
            videoRef.current.onloadedmetadata = () => {
              if (videoRef.current) {
                videoRef.current.play()
                  .then(() => setIsCameraAvailable(true))
                  .catch(err => {
                    console.error('Failed to play video:', err);
                    setError('Could not start camera preview. Please try again.');
                    setIsCameraOpen(false);
                  });
              }
            };
          } else {
            throw new Error('Video element reference not available');
          }
        } catch (err) {
          console.error('Error accessing camera:', err);
          
          if (err instanceof DOMException && err.name === 'NotAllowedError') {
            setError('Camera access denied. Please check your browser permissions.');
          } else {
            setError('Could not access camera. Please ensure your device has a working camera and try again.');
          }
          
          // Clean up
          if (streamRef.current) {
            streamRef.current.getTracks().forEach(track => track.stop());
            streamRef.current = null;
          }
          
          setIsCameraOpen(false);
        }
      }, 100); // Small delay to ensure DOM is updated
    } catch (err) {
      console.error('Error in camera setup:', err);
      setError('Failed to initialize camera. Please try again.');
      setIsCameraOpen(false);
    }
  };

  const takePhoto = async () => {
    if (!videoRef.current) {
      setError('Camera not ready. Please try again.');
      return;
    }
    
    try {
      setIsUploading(true);
      
      const canvas = document.createElement('canvas');
      canvas.width = videoRef.current.videoWidth || 640;
      canvas.height = videoRef.current.videoHeight || 480;
      
      const context = canvas.getContext('2d');
      if (!context) {
        throw new Error('Could not create canvas context');
      }
      
      context.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);
      
      // Create preview
      setPhotoPreviewUrl(canvas.toDataURL('image/jpeg'));
      
      // Get blob for upload
      const blob = await new Promise<Blob>((resolve, reject) => {
        canvas.toBlob(
          b => b ? resolve(b) : reject(new Error('Could not create blob')),
          'image/jpeg',
          0.8
        );
      });
      
      // Create FormData for upload
      const formData = new FormData();
      const fileName = `qr_verification_${vehicleId}_${Date.now()}.jpg`;
      formData.append('file', blob, fileName);
      
      // Upload to server
      const response = await uploadQrScanPhoto(vehicleId, formData);
      
      setPhotoPath(response.filePath);
      
      // Stop camera
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
        streamRef.current = null;
      }
      
      setIsCameraOpen(false);
    } catch (err) {
      console.error('Error taking photo:', err);
      setError('Failed to capture photo. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  // Move removePhoto handler to a named function
  const handleRemovePhoto = React.useCallback(() => {
    setPhotoPreviewUrl(null);
    setPhotoPath(null);
  }, []);

  const handleConfirm = async () => {
    const statusToConfirm =
      nextStepOptions && nextStepOptions.length > 0
        ? selectedNextStatus
        : nextStatusToDisplay;

    if (!statusToConfirm || !confirmationToken) {
      setError(translatedText.incompleteInfo);
      return;
    }

    if (!photoPath) {
      setError(photoTexts.photoMissing);
      return;
    }
    
    setIsLoading(true);
    setError(null);
    try {
      // When the status is changing to IN_TRANSIT_TO_VENDOR_WORKSHOP, try to get workshop info
      // from QR scan history
      let vendorRegion: string | undefined = undefined;
      let vendorWorkshopName: string | undefined = undefined;
      const statuses: PhysicalStatus[] = [
        PhysicalStatus.IN_TRANSIT_TO_VENDOR_WORKSHOP,
        PhysicalStatus.RECEIVED_BY_VENDOR_WORKSHOP,
        PhysicalStatus.UNDER_REPAIR_AT_VENDOR_WORKSHOP,
        PhysicalStatus.REPAIR_COMPLETE_BY_VENDOR,
      ];
      if (statuses.includes(statusToConfirm as PhysicalStatus)) {
        try {
          // Get QR scan history to find workshop details
          const history = await getQrScanHistory(vehicleId);
          // Find the most recent entry that has vendor workshop information
          const latestEntry = history
            .filter(item => item.vendorRegion && item.vendorWorkshopName)
            .sort((a, b) => new Date(b.scanTime).getTime() - new Date(a.scanTime).getTime())[0];
          
          if (latestEntry) {
            vendorRegion = latestEntry.vendorRegion;
            vendorWorkshopName = latestEntry.vendorWorkshopName;
          } else {
            // Fallback values if not found in history
            vendorRegion = 'N/A';
            vendorWorkshopName = 'N/A';
          }
        } catch (historyError) {
          console.error('Error fetching QR scan history:', historyError);
          // Use fallback values if history fetch fails
          vendorRegion = 'N/A';
          vendorWorkshopName = 'N/A';
        }
      }

      const response = await confirmQrStatusChange({
        vehicleId,
        confirmedNextStatus: statusToConfirm,
        confirmationToken,
        photoPath,
        vendorRegion,
        vendorWorkshopName
      });
      if (response.success && response.newPhysicalStatus) {
        onStatusConfirmed(getStatusLabel(response.newPhysicalStatus));
        onClose();
      } else {
        setError(response.message || translatedText.updateError);
      }
    } catch (err: any) {
      setError(err.message || translatedText.genericError);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered size="lg">
      <ModalOverlay bg="blackAlpha.300" />
      <ModalContent bg={bgColor} borderRadius="lg" boxShadow="xl">
        <ModalHeader
          fontWeight="bold"
          color={headerColor}
          borderBottomWidth="1px"
          borderColor={borderColor}
          py={4}
        >
          {translatedText.modalTitle}
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody py={6} px={6}>
          <VStack spacing={4} align="stretch">
            {currentStatus && (
              <Flex justifyContent="space-between" alignItems="center" p={3} bg="gray.50" borderRadius="md">
                <Text color={textColor} fontWeight="medium">
                  {translatedText.currentStatus}
                </Text>
                <Text fontWeight="semibold" color={strongTextColor}>
                  {getStatusLabel(currentStatus as PhysicalStatus)}
                </Text>
              </Flex>
            )}

            {actionAvailable && nextStatusToDisplay && !(nextStepOptions && nextStepOptions.length > 0) && (
              <Flex justifyContent="space-between" alignItems="center" p={3} bg="gray.50" borderRadius="md">
                <Text color={textColor} fontWeight="medium">
                  {translatedText.proposedStatus}
                </Text>
                <Text fontWeight="semibold" color={strongTextColor}>
                  {getStatusLabel(nextStatusToDisplay as PhysicalStatus)}
                </Text>
              </Flex>
            )}

            {actionAvailable && nextStepOptions && nextStepOptions.length > 0 && (
              <StatusSelectionSection
                nextStepOptions={nextStepOptions}
                selectedNextStatus={selectedNextStatus as string | null}
                setSelectedNextStatus={(val: string) => setSelectedNextStatus(val as PhysicalStatus)}
                translatedText={translatedText}
                getModalOptionLabel={getModalOptionLabel}
                borderColor={borderColor}
                primaryButtonColorScheme={primaryButtonColorScheme}
                textColor={textColor}
              />
            )}

            {/* Camera Section */}
            <PhotoCaptureSection
              isCameraOpen={isCameraOpen}
              isCameraAvailable={isCameraAvailable}
              isUploading={isUploading}
              photoPreviewUrl={photoPreviewUrl}
              photoPath={photoPath}
              openCamera={openCamera}
              takePhoto={takePhoto}
              removePhoto={handleRemovePhoto}
              videoRef={videoRef}
              photoTexts={photoTexts}
              borderColor={borderColor}
              textColor={textColor}
            />

            {error && (
              <Alert status="error" borderRadius="md" variant="left-accent">
                <AlertIcon />
                {error}
              </Alert>
            )}
          </VStack>
        </ModalBody>
        <ModalFooter borderTopWidth="1px" borderColor={borderColor} py={4}>
          <Button
            onClick={onClose}
            mr={3}
            isDisabled={isLoading}
            variant="outline"
            borderWidth="2px"
            borderColor="#5800F7"
            color="#5800F7"
            h="40px"
            _hover={{ bg: 'rgba(88, 0, 247, 0.1)' }}
          >
            {translatedText.cancel}
          </Button>

          {actionAvailable && confirmationToken && (
            <Button
              onClick={handleConfirm}
              isLoading={isLoading}
              isDisabled={isLoading || !photoPath || (nextStepOptions && nextStepOptions.length > 0 && !selectedNextStatus)}
              spinner={<Spinner size="sm" color="white" />}
              loadingText={translatedText.confirming}
              className={`
                text-white rounded-md h-[40px] px-4
                ${
                  isLoading || !photoPath || (nextStepOptions && nextStepOptions.length > 0 && !selectedNextStatus)
                    ? 'bg-[#9CA3AF] cursor-not-allowed'
                    : 'bg-[#5800F7] hover:bg-[#4A00D1]'
                }
              `}
            >
              {!isLoading &&
                (nextStepOptions && nextStepOptions.length > 0
                  ? selectedNextStatus
                    ? translatedText.confirm
                    : translatedText.selectOption
                  : translatedText.confirm)}
            </Button>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default PhysicalStatusUpdateModal; 