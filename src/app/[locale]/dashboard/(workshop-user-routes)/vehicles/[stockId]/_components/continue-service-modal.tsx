import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { zodResolver } from '@hookform/resolvers/zod';
import { Select } from '@radix-ui/react-select';
import { useForm } from 'react-hook-form';
import { continueServiceSchema, IContinueService } from '../validators';
import { createDefaultValuesBasedOnSchema } from '@/utils/createDefaultValues';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import ArrayInputField from '@/components/ReactHookFormComponents/ArrayInputField';
import FileInput from '@/components/ReactHookFormComponents/FileInput';
import InputPrice from '@/components/ReactHookFormComponents/InputPrice';
import { useToast } from '@chakra-ui/react';
import { useState } from 'react';
import { useCurrentUser } from '@/Providers/CurrentUserProvider';
import axios from 'axios';
import { URL_API } from '@/constants';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';
import Spinner from '@/components/Loading/Spinner';
import SignatureInput from '@/components/ReactHookFormComponents/SignatureInput';
import { dataUrlToFile } from '@/utils/dataUrlToFile';

interface ContinueServiceProps {
  serviceId: string;
}

export function ContinueServiceModal({ serviceId }: ContinueServiceProps) {

  if (!serviceId) {
    return null;
  }

  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const toast = useToast();

  const defaultValues = createDefaultValuesBasedOnSchema(continueServiceSchema);

  const { user } = useCurrentUser();

  const form = useForm<IContinueService>({
    resolver: zodResolver(continueServiceSchema),
    defaultValues,
    mode: "all"
  });

  const onSubmit = async (data: IContinueService) => {
    setIsSubmitting(true);
    try {

      const date = new Date();

      date.setMonth(date.getMonth() + parseInt(data.pendingServices.nextServiceDate));
      const nextServiceDate = date.toISOString().split('T')[0];

      data.pendingServices.nextServiceDate = nextServiceDate;

      const signatureNameFile = `${user._id}-${serviceId}.png`;

      const signature = dataUrlToFile(data.signature, signatureNameFile);

      data.signature = signature as unknown as string;

      const { data: response } = await axios.patch(`${URL_API}/vendor-platform/service/${serviceId}/complete`, {
        ...data,
      }, {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${user.accessToken}`,
          organizationId: user.organizationId,
        },
      });
      form.reset();
      setIsOpen(false);

      toast({
        title: "Servicio completado",
        description: response.message,
        status: "success",
        position: 'top',
        duration: 2900,
        isClosable: true,
      })

      return setTimeout(() => {
        window.location.reload();
      }, 3000);

    } catch (error: any) {
      return toast({
        title: "No se pudo completar el servicio",
        description: error.message || 'Something went wrong',
        status: "error",
        position: 'top',
        duration: 9000,
        isClosable: true,
      })
    } finally {
      setIsSubmitting(false);
    }


  }

  const hasErrors = Object.keys(form.formState.errors).length > 0;


  return (
    <>
      <Dialog open={isOpen} onOpenChange={setIsOpen} >
        <DialogTrigger asChild  >
          {/* <Button>Agregar Servicio</Button> */}
          <p>
            Continuar Servicio
          </p>
        </DialogTrigger>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Completar Servicio</DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">

              <Accordion type="multiple" defaultValue={["detalleServicios"]}>
                {/* Detalle de Servicios */}
                <AccordionItem value="detalleServicios" className='border-b border-purple-600'>
                  <AccordionTrigger className='data-[state=open]:bg-gray-200 rounded-md px-2' >
                    1. Detalle de Servicios Realizados
                  </AccordionTrigger>
                  <AccordionContent className='pl-6 space-y-4 mt-4 '>
                    <FormField
                      control={form.control}
                      // name="serviceDetail.serviceType"
                      name="serviceDetail.serviceType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>1.- Tipo de Servicio</FormLabel>
                          <Select
                            onValueChange={(value: Exclude<IContinueService["serviceDetail"]["serviceType"], undefined>) => {
                              form.reset();
                              form.setValue('serviceDetail.serviceType', value)
                            }}
                            value={field.value}
                          // value={form.watch('serviceDetail.serviceType')}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Selecciona un tipo" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="preventive">Preventivo</SelectItem>
                              <SelectItem value="corrective">Correctivo</SelectItem>
                              <SelectItem value="other">Otro</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )}
                    />


                    <div
                      className='space-y-4'
                    >

                      <p>2.- Especificaciones del servicio:</p>

                      {
                        form.watch('serviceDetail.serviceType') === 'other' && (
                          <>
                            <ArrayInputField
                              control={form.control}
                              name="serviceDetail.specifications.otherServices"
                              label="Otros Servicios"
                              placeholder="Escribe y presiona Enter"
                            />
                          </>
                        )}

                      {
                        form.watch('serviceDetail.serviceType') === 'preventive' && (
                          <>
                            <div id='oilChange' className='flex gap-2 items-center'>
                              <FormField
                                control={form.control}
                                name="serviceDetail.specifications.oilChange"
                                render={({ field }) => {
                                  return (
                                    <FormItem className='pl-6'>
                                      <div className="flex items-center gap-3">
                                        <Checkbox
                                          checked={field.value}
                                          onCheckedChange={field.onChange}
                                        />
                                        <FormLabel

                                        >Cambio de Aceite</FormLabel>
                                      </div>
                                    </FormItem>
                                  )
                                }}
                              />

                            </div>


                            {
                              form.watch('serviceDetail.specifications.oilChange') && (
                                <FileInput
                                  control={form.control}
                                  name="serviceDetail.specifications.oilImages"
                                  label="Imagenes del cambio de aceite"
                                />
                              )
                            }

                            <div
                              id='filterChange'
                            >
                              <div className='flex gap-2 items-center'>
                                <FormField
                                  control={form.control}
                                  name="serviceDetail.specifications.filterChange"
                                  render={({ field }) => {
                                    return (
                                      <FormItem className='pl-6'>
                                        <div className="flex items-center gap-3">
                                          <Checkbox
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                          />
                                          <FormLabel

                                          >Cambio de Filtro</FormLabel>
                                        </div>
                                      </FormItem>
                                    )
                                  }}
                                />



                              </div>
                              {
                                  form.watch('serviceDetail.specifications.filterChange') && (
                                  <div className='flex flex-col gap-4 pl-6 my-4' id='filterType'>

                                      <FormField
                                        control={form.control}
                                      name="serviceDetail.specifications.filterType.air"
                                      render={({ field }) => {
                                        return (
                                          <FormItem className='pl-6'>
                                            <div className="flex items-center gap-3">
                                              <Checkbox
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                              />
                                              <FormLabel

                                              >
                                                Filtro de Aire
                                              </FormLabel>
                                            </div>
                                          </FormItem>
                                        )
                                      }}
                                    />

                                    <FormField
                                      control={form.control}
                                      name="serviceDetail.specifications.filterType.oil"
                                      render={({ field }) => {
                                        return (
                                          <FormItem className='pl-6'>
                                            <div className="flex items-center gap-3">
                                              <Checkbox
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                              />
                                              <FormLabel

                                              >
                                                Filtro de Aceite
                                              </FormLabel>
                                            </div>
                                          </FormItem>
                                        )
                                      }}
                                      />




                                    </div>
                                  )
                              }
                              {
                                form.watch('serviceDetail.specifications.filterChange') && (
                                  <FileInput
                                    control={form.control}
                                    name="serviceDetail.specifications.filterImages"
                                    label="Imagenes del filtro"
                                  />
                                )
                              }
                            </div>


                            <div id='tuneUp' className='flex flex-col gap-2' >

                              <FormField
                                control={form.control}
                                name="serviceDetail.specifications.tuneUp"
                                render={({ field }) => {
                                  return (
                                    <FormItem className='pl-6'>
                                      <div className="flex items-center gap-3">
                                        <Checkbox
                                          checked={field.value}
                                          onCheckedChange={field.onChange}
                                        />
                                        <FormLabel

                                        >Cambio de bujias</FormLabel>
                                      </div>
                                    </FormItem>
                                  )
                                }}
                              />
                              {
                                form.watch('serviceDetail.specifications.tuneUp') && (
                                  <>
                                    <FileInput
                                      control={form.control}
                                      name="serviceDetail.specifications.tuneUpImages"
                                      label="Imagenes de las bujias"
                                    />
                                  </>
                                )
                              }



                            </div>
                          </>
                        )}




                      {
                        form.watch('serviceDetail.serviceType') === 'corrective' && (
                          <>
                            <FormField
                              control={form.control}
                              name="serviceDetail.specifications.batteryChange"
                              render={({ field }) => {
                                return (
                                  <FormItem className='pl-6'>
                                    <div className="flex items-center gap-3">
                                      <Checkbox
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                      />
                                      <FormLabel

                                      >Cambio de Batería</FormLabel>
                                    </div>
                                  </FormItem>
                                )
                              }}
                            />
                            <div id='brakes' className='flex flex-col gap-2 mt-4' >

                              <FormField
                                control={form.control}
                                name="serviceDetail.specifications.brakes"
                                render={({ field }) => {
                                  return (
                                    <FormItem className='pl-6'>
                                      <div className="flex items-center gap-3">
                                        <Checkbox
                                          checked={field.value}
                                          onCheckedChange={field.onChange}
                                        />
                                        <FormLabel

                                        >
                                          Cambio de frenos
                                        </FormLabel>
                                      </div>
                                    </FormItem>
                                  )
                                }}
                              />

                              {
                                form.watch('serviceDetail.specifications.brakes') && (
                                  <div className='flex flex-col gap-3 pl-6 mt-2' id='brakesDetail'>
                                    <FormField
                                      control={form.control}
                                      name="serviceDetail.specifications.brakesDetail.padChange"
                                      render={({ field }) => {
                                        return (
                                          <FormItem className='pl-6'>
                                            <div className="flex items-center gap-3">
                                              <Checkbox
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                              />
                                              <FormLabel

                                              >Cambio de Pastillas</FormLabel>
                                            </div>
                                          </FormItem>
                                        )
                                      }}
                                    />
                                    <FormField
                                      control={form.control}
                                      name="serviceDetail.specifications.brakesDetail.discService"
                                      render={({ field }) => {
                                        return (
                                          <FormItem className='pl-6'>
                                            <div className="flex items-center gap-3">
                                              <Checkbox
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                              />
                                              <FormLabel

                                              >Servicio de Discos</FormLabel>
                                            </div>
                                          </FormItem>
                                        )
                                      }}
                                    />
                                    <div className='flex gap-3 items-center'>
                                      <FormField
                                        control={form.control}
                                        name="serviceDetail.specifications.brakesDetail.fluidChange"
                                        render={({ field }) => {
                                          return (
                                            <FormItem className='pl-6'>
                                              <div className="flex items-center gap-3">
                                                <Checkbox
                                                  checked={field.value}
                                                  onCheckedChange={field.onChange}
                                                />
                                                <FormLabel

                                                >Cambio de Líquido</FormLabel>
                                              </div>
                                            </FormItem>
                                          )
                                        }}
                                      />
                                      {
                                        form.watch('serviceDetail.specifications.brakesDetail.fluidChange') && (
                                          <div className='flex flex-col gap-4'>
                                            <FormField
                                              control={form.control}
                                              name="serviceDetail.specifications.brakesDetail.fluidType"
                                              render={({ field }) => (
                                                <FormItem>
                                                  <FormLabel className='hidden' >Tipo de Líquido</FormLabel>
                                                  <Select onValueChange={field.onChange} value={field.value}>
                                                    <SelectTrigger>
                                                      <SelectValue placeholder="Selecciona un tipo" />
                                                    </SelectTrigger>
                                                    <SelectContent className='w-[max-content">'>

                                                      {/* Unselect option */}
                                                      <SelectItem value={null as unknown as string}>Selecciona un tipo</SelectItem>

                                                      <SelectItem value="DOT3">DOT3</SelectItem>
                                                      <SelectItem value="DOT4">DOT4</SelectItem>
                                                      <SelectItem value="DOT5">DOT5</SelectItem>
                                                    </SelectContent>
                                                  </Select>
                                                </FormItem>
                                              )}
                                            />

                                          </div>
                                        )
                                      }
                                    </div>
                                  </div>
                                )}




                            </div>

                            <div id='suspension' className='flex flex-col gap-2 mt-4' >

                              <FormField
                                control={form.control}
                                name="serviceDetail.specifications.suspension"
                                render={({ field }) => {
                                  return (
                                    <FormItem className='pl-6'>
                                      <div className="flex items-center gap-3">
                                        <Checkbox
                                          checked={field.value}
                                          onCheckedChange={field.onChange}
                                        />
                                        <FormLabel

                                        >
                                          Alineación y Balanceo
                                        </FormLabel>
                                      </div>
                                    </FormItem>
                                  )
                                }}
                              />

                              {
                                form.watch('serviceDetail.specifications.suspension') && (
                                  <div className='flex flex-col gap-3 pl-6 mt-2' id='suspensionDetail'>
                                    <FormField
                                      control={form.control}
                                      name="serviceDetail.specifications.suspensionDetail.shockAbsorbers"
                                      render={({ field }) => {
                                        return (
                                          <FormItem className='pl-6'>
                                            <div className="flex items-center gap-3">
                                              <Checkbox
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                              />
                                              <FormLabel

                                              >Cambio de Amortiguadores</FormLabel>
                                            </div>
                                          </FormItem>
                                        )
                                      }}
                                    />
                                    <FormField
                                      control={form.control}
                                      name="serviceDetail.specifications.suspensionDetail.springsAndBushings"
                                      render={({ field }) => {
                                        return (
                                          <FormItem className='pl-6'>
                                            <div className="flex items-center gap-3">
                                              <Checkbox
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                              />
                                              <FormLabel

                                              >Reivisión de Muelles y Bujes</FormLabel>
                                            </div>
                                          </FormItem>
                                        )
                                      }}
                                    />
                                    <FormField
                                      control={form.control}
                                      name="serviceDetail.specifications.suspensionDetail.boltsTightening"
                                      render={({ field }) => {
                                        return (
                                          <FormItem className='pl-6'>
                                            <div className="flex items-center gap-3">
                                              <Checkbox
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                              />
                                              <FormLabel

                                              >
                                                Ajuste de Tornillos y Lubricación
                                              </FormLabel>
                                            </div>
                                          </FormItem>
                                        )
                                      }}
                                    />
                                  </ div>
                                )
                              }
                            </div>
                          </>
                        )}

                    </div>

                  </AccordionContent>
                </AccordionItem>


                {/* Servicios Pendientes */}

                <AccordionItem value="serviciosPendientes" className='border-b border-purple-600'>
                  <AccordionTrigger className='data-[state=open]:bg-gray-200 rounded-md px-2' >
                    2. Próximo servicio
                  </AccordionTrigger>

                  <AccordionContent className="pl-6 flex flex-col gap-4 mt-4">
                    <FormField
                      control={form.control}
                      name="pendingServices.nextServiceDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Próxima fecha de servicio</FormLabel>
                          {/* <Input type="date" {...field} /> */}
                          <div style={{ display: "flex", gap: "1rem" }}>
                            {["3", "6", "9", "12"].map((month) => (
                              <Button
                                key={month}
                                type='button'
                                onClick={() => {
                                  field.onChange(month);

                                  // from now to selected month

                                  // field.onChange(date.toISOString().split('T')[0]);

                                }}
                                // colorScheme={field.value?.includes(months) ? "blue" : "gray"} // Cambiar el color si está seleccionado
                                style={{
                                  padding: "0.5rem 1rem",
                                  border: "1px solid",
                                  borderRadius: "5px",
                                  backgroundColor: field.value === month ? "#3182ce" : "#e2e8f0", // Azul si está seleccionado, gris si no
                                  color: field.value === month ? "#fff" : "#000",
                                  cursor: "pointer",
                                }}
                              >
                                {month} meses
                              </Button>
                            ))}
                          </div>

                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="pendingServices.nextServiceKm"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Kilometraje para el próximo servicio</FormLabel>
                          {/* <Input type="date" {...field} /> */}
                          <div style={{ display: "flex", gap: "1rem" }}>
                            {["5", "10", "15"].map((km) => (
                              <Button
                                key={km}
                                type='button'
                                onClick={() => {
                                  field.onChange(km);
                                }}
                                // colorScheme={field.value?.includes(months) ? "blue" : "gray"} // Cambiar el color si está seleccionado
                                style={{
                                  padding: "0.5rem 1rem",
                                  border: "1px solid",
                                  borderRadius: "5px",
                                  backgroundColor: field.value === km ? "#3182ce" : "#e2e8f0", // Azul si está seleccionado, gris si no
                                  color: field.value === km ? "#fff" : "#000",
                                  cursor: "pointer",
                                }}
                              >
                                {km} km
                              </Button>
                            ))}
                          </div>

                        </FormItem>
                      )}
                    />


                    <ArrayInputField
                      control={form.control}
                      name="pendingServices.recommendations"
                      label="Recomendaciones"
                      placeholder="Escribe y presiona Enter"
                    />

                  </AccordionContent>
                </AccordionItem>

                {/* Costos y Tiempos */}

                <AccordionItem value="costosTiempos" className='border-b border-purple-600'>
                  <AccordionTrigger className='data-[state=open]:bg-gray-200 rounded-md px-2' >
                    3. Costos y Tiempos
                  </AccordionTrigger>
                  <AccordionContent className="pl-6 flex flex-col gap-4 mt-4">

                    <InputPrice
                      control={form.control}
                      name="costsAndTimes.totalCost"
                      label="Costo total"
                      placeholder="Ingrese el precio"
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="costosTiempos" className='border-b border-purple-600'>
                  <AccordionTrigger className='data-[state=open]:bg-gray-200 rounded-md px-2' >
                    4. Firma
                  </AccordionTrigger>
                  <AccordionContent className="pl-6 flex flex-col gap-4 mt-4">
                    <SignatureInput
                      control={form.control}
                      name="signature"
                      label="Firma"
                    />
                  </AccordionContent>
                </AccordionItem>

              </Accordion>

              <div className="flex justify-end space-x-2">
                <DialogTrigger asChild>
                  <Button variant="outline">Cancelar</Button>
                </DialogTrigger>

                <Button
                  type='submit'
                  disabled={isSubmitting}
                  // className={cn(
                  //   'bg-primaryBtn text-white',
                  //   isSubmitting && 'bg-gray-300 text-gray-500',
                  // )}
                  className='bg-primaryBtn text-white hover:bg-purple-700'
                >
                  {
                    isSubmitting ? <Spinner /> : 'Guardar'
                  }
                </Button>

              </div>

              {
                hasErrors && (
                  <div className='bg-red-100 text-red-600 p-4 rounded-md'>
                    <p>Por favor completa los campos requeridos</p>
                  </div>
                )
              }

            </form>
          </Form>
        </DialogContent>
      </Dialog>


    </>
  )

}