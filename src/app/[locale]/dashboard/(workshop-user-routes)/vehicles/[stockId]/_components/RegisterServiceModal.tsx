import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import FileInput from '@/components/ReactHookFormComponents/FileInput';
import { useParams, usePathname, useRouter, useSearchParams } from 'next/navigation';
import axios from 'axios';
import { URL_API } from '@/constants';
import { Spinner, useToast } from '@chakra-ui/react';
import { useCurrentUser } from '@/Providers/CurrentUserProvider';
import { IRegisterService, IServiceFormType, registerServiceSchema } from '../validators';
import { createDefaultValuesBasedOnSchema } from '@/utils/createDefaultValues';
import { VehicleDetail } from '../../_actions/getVehicleDetailById';
import { Select } from '@/components/ui/select';
import SelectInput from '@/components/Inputs/SelectInput';
import { Input } from '@/components/ui/input';
// import { cn } from '@/lib/utils';


type BodyWorkKeys = keyof IRegisterService['bodyWorkDetail'];

type BodyWorkTypes = 'bodyWork' | `${"bodyWorkDetail"}.${BodyWorkKeys}`
type EmergencyKeys = keyof IRegisterService['emergency'];

type FirstStepKeys = (keyof IRegisterService) | BodyWorkTypes | `${"emergency"}.${EmergencyKeys}`

type InspectionItem = {
  label: string;
  name: FirstStepKeys;
  type?: 'file'
  // add a custom validator callback to be able to display or not the field depending on the callback
  // and the callback should be able to return a children component to be able to display the field, based on the callback result

  validator?: (data: IRegisterService) => boolean;

}


const inspectionItems: InspectionItem[] = [
  { label: 'Carrocería', name: 'bodyWork' },
  { label: 'Pintura', name: 'bodyWorkDetail.paintCondition' },
  { label: 'Imágenes de carrocería', name: 'bodyWorkDetail.paintConditionImages', type: 'file' },
  { label: 'Golpes', name: 'bodyWorkDetail.blows' },
  {
    label: 'Imágenes de los golpes',
    name: 'bodyWorkDetail.blowsImages',
    type: 'file',
    // shouldn't display if the blows doesn't have a value or if it's good condition
    validator: (data) => {
      // console.log('blows: ', data.bodyWorkDetail?.blows);
      return Boolean(data.bodyWorkDetail?.blows) && data.bodyWorkDetail?.blows !== 'good condition'
      // return true;
    }

  },
  { label: 'Cristales y Espejos', name: 'crystalsAndMirrors' },
  { label: 'Llantas', name: 'tires' },
  { label: 'Luces', name: 'lights' },
  { label: 'Asientos', name: 'seats' },
  { label: 'Imagénes del Interior', name: 'seatsImages', type: 'file' },
  { label: 'Tablero', name: 'dashboard' },
  { label: 'Imágenes del tablero', name: 'dashboardImages', type: 'file' },
  { label: 'Sistema de Control', name: 'controlSystem' },
  { label: 'Sistema Eléctrico', name: 'electronicSystems' },
  { label: 'Motor', name: 'engine' },
  { label: 'Imágenes del motor', name: 'engineImages', type: 'file' },
  { label: 'Transmisión', name: 'transmission' },
  { label: 'Batería', name: 'battery' },
  { label: 'Imagénes de la batería', name: 'batteryImages', type: 'file' },
  { label: 'KW de la batería', name: 'batteryKW' },
  { label: 'Frenos', name: 'brakes' },
  { label: 'Suspensión', name: 'suspension' },
  { label: 'Sistema Eléctrico', name: 'electricalSystem' },
  { label: 'Sistema de Escape', name: 'exhaustSystem' },
  { label: 'Refrigerante', name: 'refrigerant' },
  { label: 'Líquido de Frenos', name: 'brakeFluid' },
  { label: 'Líquido de Dirección', name: 'powerSteeringFluid' },
  { label: 'Líquido Limpiaparabrisas', name: 'windshieldWasherFluid' },
];


interface ServiceFormProps {
  vehicle: VehicleDetail
}

export function RegisterServiceModal({ vehicle }: ServiceFormProps) {
  const [isOpen, setIsOpen] = React.useState(false);

  const searchParams = useSearchParams();
  const router = useRouter();

  const { stockId } = useParams<{ stockId: string }>();
  const toast = useToast();
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const [workshops, setWorkshops] = React.useState<any[]>([]);
  const [selectedWorkshop, setSelectedWorkshop] = React.useState<{ label: string, value: string } | undefined>(undefined);

  const defaultValues = createDefaultValuesBasedOnSchema(registerServiceSchema);

  // console.log('defaultValues', defaultValues);
  const form = useForm<IRegisterService>({
    resolver: zodResolver(registerServiceSchema),
    defaultValues,
    // defaultValues: {
    //   emergencyTools: undefined,
    //   emergencyToolsImages: [],
    // },
    mode: "all"
  });

  const { user } = useCurrentUser();


  const onSubmit = async (data: IRegisterService) => {

    if (!selectedWorkshop || !selectedWorkshop.value) {
      return toast({
        position: 'top',
        title: "No existe ningun taller registrado",
        description: 'Por favor registra un taller para continuar',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    }

    try {
      const parseData = {
        ...data,
        stockId,
        // contractNumber: '1008'
        contractNumber: vehicle.contractNumber,
        workshopId: selectedWorkshop.value,
      }

      setIsSubmitting(true);

      await axios.post(`${URL_API}/vendor-platform/service`, /* formData */ {
        ...parseData,
      }, {
        headers: {
          'Content-Type': 'multipart/form-data',
          // 'Authorization': user.accessToken,
          'Authorization': 'Bearer ' + user.accessToken,
          organizationId: user.organizationId,
        }
      });

      setIsOpen(false);
      toast({
        position: 'top',
        title: 'Servicio registrado',
        description: 'El servicio ha sido registrado exitosamente',
        status: 'success',
        duration: 5000,
        isClosable: true,
      })
      // return router.refresh();

      return window.location.reload();

    } catch (error: any) {

      console.error('error', error.response.data);
      return toast({
        position: 'top',
        title: 'Error al registrar el servicio',
        description: error.response.data.message || 'Ha ocurrido un error al registrar el servicio',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    } finally {
      setIsSubmitting(false);
    }

  };


  useEffect(() => {

    const key = 'service-' + stockId;
    const savedService = localStorage.getItem(key);

    const fetchWorkshops = async () => {

      if (savedService) {
        const parsedService = JSON.parse(savedService);
        const option = { label: parsedService.workshop.name, value: parsedService.workshop._id };
        console.log('option', option);
        setSelectedWorkshop(option);
      }

      try {
        const url = `${URL_API}/vendor-platform/organizations/${user.organizationId}/workshops`;
        const { data } = await axios.get(`${url}`, {
          headers: {
            'Authorization': 'Bearer ' + user.accessToken,
            organizationId: user.organizationId,
          }
        });

        const servicesResponse = data.data as any[];

        // servicesResponse.push(...[{ name: 'Taller 1', _id: '64721HFB21H4' }, { name: 'Taller 2', _id: '64721HFB21H5', }]); // Mock data

        setWorkshops(servicesResponse as any[]);

        if (servicesResponse?.length > 0 && !savedService) {
          console.log('servicesResponse', servicesResponse);
          setSelectedWorkshop({ label: servicesResponse[0].name, value: servicesResponse[0]._id });
        }

      } catch (error) {
        console.error('error', error);
      }
    }

    fetchWorkshops();
  }, [user.accessToken, user.organizationId]);

  const pathname = usePathname();
  useEffect(() => {
    const addServiceParam = searchParams.get('addService')
    const addService = addServiceParam === 'true'

    // if addService is true, open the modal automatically and remove the query param from the url to avoid opening the modal again if the user refreshes the page
    if (addService) {
      setIsOpen(true);
      router.replace(pathname);
    }
  }, [searchParams, pathname, router]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild  >
        <Button>Agregar Servicio</Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Registro de Servicio</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <>

              <StepOne form={form} />

              <SelectInput
                isFormik={false}
                name="workshop"
                label="Selecciona un taller"
                options={workshops.map(workshop => ({ label: workshop.name, value: workshop._id }))}
                defaultOption={workshops.length > 0 ? { label: workshops[0].name, value: workshops[0].id } : selectedWorkshop || undefined}

                onChange={(option) => {
                  const selectedWorkshop = workshops.find(workshop => workshop._id === option.value);
                  if (!selectedWorkshop) return;
                  setSelectedWorkshop({ label: selectedWorkshop.name, value: selectedWorkshop._id });
                }}

              />


              <div className="flex justify-end space-x-2">
                <DialogTrigger asChild>
                  <Button variant="outline"
                    onClick={() => {
                      setIsSubmitting(false);
                    }}
                  >Cancelar</Button>
                </DialogTrigger>

                <Button
                  type='submit'
                  disabled={isSubmitting}
                  // className={cn(
                  //   'bg-primaryBtn text-white',
                  //   isSubmitting && 'bg-gray-300 text-gray-500',
                  // )}
                  className='bg-primaryBtn text-white hover:bg-purple-700'
                >
                  {
                    isSubmitting ? <Spinner /> : 'Guardar'
                  }
                </Button>

              </div>
            </>

          </form>
        </Form>
      </DialogContent>
    </Dialog >
  );
}

function StepOne({ form }: { form: IServiceFormType }) {

  const hasErrors = Object.keys(form.formState.errors).length > 0;
  const watchedValues = form.watch();

  // console.log('errors', form.formState.errors);

  return (
    <>

      <Accordion type="multiple" defaultValue={["exterior", "interior", "mechanical", "fluids", "extras"]}>
        {/* Exterior */}
        <AccordionItem value="exterior" className='border-b border-purple-600'>
          <AccordionTrigger className='data-[state=open]:bg-gray-200 hover:bg-gray-200 rounded-md px-2 hover:no-underline ' >Exterior del vehiculo</AccordionTrigger>
          <AccordionContent className='pl-6 space-y-4 mt-4  '>

            {/* Pedir el KM actual en un input tipo number */}

            <FormField
              control={form.control}
              name="arrivalKm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>KM Actual</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      {...field}
                      placeholder="Kilometraje actual"
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <div id='bodyWorkDetail' className='flex flex-col gap-4'>
              <h2>
                Tiene alguno de los siguientes
              </h2>
              <FormField
                control={form.control}
                name="bodyWorkDetail.reviewScratches.dents"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-3">
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                      <FormLabel>Abolladuras</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="bodyWorkDetail.reviewScratches.scratches"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-3">
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                      <FormLabel>Rayones</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="bodyWorkDetail.reviewScratches.corrosion"
                // name='bod'
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-3">
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                      <FormLabel>Corrosión</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
            </div>
            {/* HERE SHOULD BE A CHECKBOX WITH CHECKS IN FLEX-ROW WITH IT'S VALUES, IN THE TOP SHOULD BE THE "HEADER LIKE: Good condition | Needs atention | Critical Issue" */}

            <div className="flex mb-4">
              <div className="w-2/5 ">
                {/* Empty space for alignment with labels */}
              </div>
              <div className="flex flex-1 gap-4 relative ">
                <div className="flex-1 text-center py-2 bg-green-100 rounded font-medium text-green-800 ">
                  Buen estado
                </div>

                <div className="flex-1 text-center py-2 bg-yellow-100 rounded font-medium text-yellow-800">
                  Necesita atención
                </div>
                <div className="flex-1 text-center py-2 bg-red-100 rounded font-medium text-red-800">
                  Problema crítico
                </div>
              </div>

            </div>

            {/* Inspection items */}
            <div className="space-y-3 z-[100]">

              {inspectionItems.map((item, index) => {

                if (item.validator && !item.validator(watchedValues)) {
                  return null;
                }
                return <Item key={index} label={item.label} form={form} name={item.name} type={item.type} />
              })}
            </div>

            <div className="flex mb-4">
              <div className="w-2/5 ">
                {/* Empty space for alignment with labels */}
              </div>
              <div className="flex flex-1 gap-4 relative ">
                <div className="flex-1 text-center py-2 bg-green-100 rounded font-medium text-green-800 ">
                  Completo

                </div>

                <div className="flex-1 text-center py-2 bg-yellow-100 rounded font-medium text-yellow-800">
                  Incompleto
                </div>
                <div className="flex-1 text-center py-2 bg-red-100 rounded font-medium text-red-800">
                  Faltante
                </div>
              </div>

            </div>

            {/* Inspection items */}
            <div className="space-y-3 z-[100]">

              <ItemComponent label="Herramientas de Emergencia" form={form} name="emergency.emergencyTools" />
              <ItemComponent label="Imágenes de las herramientas de emergencia" form={form} name="emergency.emergencyToolsImages" type='file' />
              <ItemComponent label="Llanta de refacción" form={form} name="spareWheel" />
              <ItemComponent label="Imágenes de Llanta de refacción" form={form} name="spareWheelImages" type='file' />

            </div>


          </AccordionContent>
        </AccordionItem>

      </Accordion>


      {/* SHOW MESSAGE THAT THERE ARE MISSING VALUES: */}

      {
        hasErrors && (
          <div className='bg-red-100 text-red-600 p-4 rounded-md'>
            <p>Por favor completa los campos requeridos</p>
          </div>
        )
      }

    </>
  )
}


// function Item({ label, form, name }: { label: string, form: IServiceFormType, name: FirstStepKeys }) {
//   return (
//     <div className="flex items-center">

//       {/* CREATE THE SAME AS ABOVE BUT USING FORM AND INPUT COMPONENTS */}

//       <FormField
//         control={form.control}
//         name={`${name}`}
//         render={({ field }) => (
//           <FormItem className='flex items-center justify-between w-full'>

//             <FormLabel className=' w-2/5 '>{label}</FormLabel>
//             <div className="flex flex-1 gap-4">
//               <div className="flex-1 flex justify-center">
//                 <Checkbox
//                   checked={field.value === 'good condition'}
//                   onCheckedChange={() => field.onChange('good condition')}
//                   className='h-6 w-6 text-green-600 border-green-500 focus:ring-green-500 rounded-full'
//                 />
//               </div>
//               <div className="flex-1 flex justify-center">

//                 <Checkbox
//                   checked={field.value === 'needs atention'}
//                   onCheckedChange={() => field.onChange('needs atention')}
//                   className="h-6 w-6 text-yellow-600 border-yellow-500 focus:ring-yellow-500 rounded-full"

//                 />
//               </div>
//               <div className="flex-1 flex justify-center">

//                 <Checkbox
//                   checked={field.value === 'critical issue'}
//                   onCheckedChange={() => field.onChange('critical issue')}
//                   className="h-6 w-6 text-red-600 border-red-500 focus:ring-red-500 rounded-full"
//                 />
//               </div>
//             </div>
//           </FormItem>
//         )}
//       />




//     </div>
//   )
// }


interface ItemProps extends InspectionItem {
  form: IServiceFormType
}


function Item({ label, form, name, type }: ItemProps) {


  if (type && type === 'file') {
    return <FileInput
      control={form.control}
      name={`${name}`}
      label={label}
      accept="all-images"
    />
  }

  return (
    <>

      <div className="flex items-center">

        {/* CREATE THE SAME AS ABOVE BUT USING FORM AND INPUT COMPONENTS */}

        <FormField
          control={form.control}
          name={`${name}`}
          render={({ field }) => (
            <FormItem className='flex items-center justify-between w-full'>

              <FormLabel className=' w-2/5 '>{label}</FormLabel>
              <div className="flex flex-1 gap-4">
                <div className="flex-1 flex justify-center">
                  <Checkbox
                    checked={field.value === 'good condition'}
                    onCheckedChange={() => field.onChange('good condition')}
                    className='h-6 w-6 text-green-600 border-green-500 focus:ring-green-500 rounded-full'
                  />
                </div>
                <div className="flex-1 flex justify-center">

                  <Checkbox
                    checked={field.value === 'needs atention'}
                    onCheckedChange={() => field.onChange('needs atention')}
                    className="h-6 w-6 text-yellow-600 border-yellow-500 focus:ring-yellow-500 rounded-full"

                  />
                </div>
                <div className="flex-1 flex justify-center">

                  <Checkbox
                    checked={field.value === 'critical issue'}
                    onCheckedChange={() => field.onChange('critical issue')}
                    className="h-6 w-6 text-red-600 border-red-500 focus:ring-red-500 rounded-full"
                  />
                </div>
              </div>
            </FormItem>
          )}
        />


      </div>

    </>
  )
}


function ItemComponent({ label, form, name, type }: ItemProps) {


  if (type && type === 'file') {
    return <FileInput
      control={form.control}
      name={`${name}`}
      label={label}
      accept="all-images"
    />
  }

  return (
    <FormField
      control={form.control}
      name={`${name}`}
      render={({ field }) => (
        <FormItem className='flex items-center justify-between w-full'>

          <FormLabel className=' w-2/5 '>{label}</FormLabel>
          <div className="flex flex-1 gap-4">
            <div className="flex-1 flex justify-center">
              <Checkbox
                checked={field.value === 'complete'}
                onCheckedChange={() => field.onChange('complete')}
                className='h-6 w-6 text-green-600 border-green-500 focus:ring-green-500 rounded-full'
              />
            </div>
            <div className="flex-1 flex justify-center">

              <Checkbox
                checked={field.value === 'incomplete'}
                onCheckedChange={() => field.onChange("incomplete")}
                className="h-6 w-6 text-yellow-600 border-yellow-500 focus:ring-yellow-500 rounded-full"

              />
            </div>
            <div className="flex-1 flex justify-center">

              <Checkbox
                checked={field.value === 'missing'}
                onCheckedChange={() => field.onChange('missing')}
                className="h-6 w-6 text-red-600 border-red-500 focus:ring-red-500 rounded-full"
              />
            </div>
          </div>
        </FormItem>
      )}
    />
  )
}









// function convertArrayOfFilesToFileList(files: File[]) {
//   // return new FileList(files, 'images');

//   const fileList = new DataTransfer();
//   files.forEach(file => {
//     fileList.items.add(file);
//   });

//   return fileList.files;

// }

// function convertDataObjectToFormData(data: any) {
//   const formData = new FormData();

//   for (const key in data) {
//     if (data[key] instanceof FileList) {
//       Array.from(data[key]).forEach((file: File) => {
//         formData.append(key, file);
//       });
//     } else {
//       formData.append(key, data[key]);
//     }
//   }

//   return formData;
// }
