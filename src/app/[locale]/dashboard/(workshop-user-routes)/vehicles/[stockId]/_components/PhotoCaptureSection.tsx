import React, { useCallback } from 'react';
import { Box, VStack, Flex, Spinner, Button, Image, IconButton, Text } from '@chakra-ui/react';
import { CameraIcon, XCircleIcon } from 'lucide-react';

interface PhotoCaptureSectionProps {
  isCameraOpen: boolean;
  isCameraAvailable: boolean;
  isUploading: boolean;
  photoPreviewUrl: string | null;
  photoPath: string | null;
  openCamera: () => void;
  takePhoto: () => void;
  removePhoto: () => void;
  videoRef: React.RefObject<HTMLVideoElement>;
  photoTexts: {
    takePhoto: string;
    retakePhoto: string;
    photoRequired: string;
    photoInstruction: string;
    photoDescription: string;
    uploading: string;
  };
  borderColor: string;
  textColor: string;
}

const PhotoCaptureSection: React.FC<PhotoCaptureSectionProps> = ({
  isCameraOpen,
  isCameraAvailable,
  isUploading,
  photoPreviewUrl,
  photoPath,
  openCamera,
  takePhoto,
  removePhoto,
  videoRef,
  photoTexts,
  borderColor,
  textColor,
}) => {
  // Memoized handlers to avoid inline functions
  const handleTakePhoto = useCallback(() => {
    takePhoto();
  }, [takePhoto]);

  const handleRemovePhoto = useCallback(() => {
    removePhoto();
  }, [removePhoto]);

  const handleOpenCamera = useCallback(() => {
    openCamera();
  }, [openCamera]);

  return (
    <Box borderWidth="1px" borderRadius="md" p={4} borderColor={borderColor}>
      <Text fontWeight="medium" mb={3}>
        {photoTexts.photoRequired}
      </Text>
      {isCameraOpen ? (
        <VStack spacing={3} align="center" w="100%">
          <Box position="relative" width="100%" height="300px" bg="black" borderRadius="md" overflow="hidden">
            <video
              ref={videoRef}
              style={{ width: '100%', height: '100%', objectFit: 'cover' }}
              autoPlay
              playsInline
              muted
            />
            {!isCameraAvailable && (
              <Flex
                position="absolute"
                top="0"
                left="0"
                right="0"
                bottom="0"
                justifyContent="center"
                alignItems="center"
                bg="rgba(0,0,0,0.7)"
              >
                <Spinner color="white" size="lg" />
              </Flex>
            )}
          </Box>
          <Button 
            colorScheme="blue" 
            leftIcon={<CameraIcon size={18} />}
            onClick={handleTakePhoto}
            size="md"
            w="full"
            isLoading={isUploading}
            loadingText={photoTexts.uploading}
            isDisabled={!isCameraAvailable}
            className="text-white rounded-md h-[40px] px-4 bg-[#5800F7] hover:bg-[#4A00D1]"
          >
            {photoTexts.takePhoto}
          </Button>
        </VStack>
      ) : photoPreviewUrl ? (
        <VStack spacing={3} align="center">
          <Box position="relative" width="100%" borderRadius="md" overflow="hidden">
            <Image src={photoPreviewUrl} alt="Vehicle Photo" w="100%" borderRadius="md" />
            <IconButton
              aria-label="Remove photo"
              icon={<XCircleIcon />}
              position="absolute"
              top={2}
              right={2}
              colorScheme="red"
              size="sm"
              borderRadius="full"
              onClick={handleRemovePhoto}
            />
          </Box>
          <Button 
            variant="outline" 
            leftIcon={<CameraIcon size={18} />}
            onClick={handleOpenCamera}
            size="sm"
          >
            {photoTexts.retakePhoto}
          </Button>
        </VStack>
      ) : (
        <Flex 
          direction="column" 
          align="center" 
          justify="center" 
          p={4} 
          bg="gray.50" 
          _dark={{ bg: 'gray.700' }} 
          borderRadius="md"
          cursor="pointer"
          onClick={handleOpenCamera}
        >
          <CameraIcon size={48} />
          <Text mt={2} fontWeight="medium">
            {photoTexts.photoInstruction}
          </Text>
          <Text fontSize="sm" color={textColor} mt={1}>
            {photoTexts.photoDescription}
          </Text>
          <Button 
            mt={4} 
            colorScheme="blue" 
            leftIcon={<CameraIcon size={18} />}
            onClick={handleOpenCamera}
            className="text-white rounded-md h-[40px] px-4 bg-[#5800F7] hover:bg-[#4A00D1]"
          >
            {photoTexts.takePhoto}
          </Button>
        </Flex>
      )}
    </Box>
  );
};

export default PhotoCaptureSection; 