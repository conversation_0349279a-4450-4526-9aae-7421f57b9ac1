'use server';

import { URL_API } from '@/constants';
import { cache } from 'react';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
import { IContinueService, IRegisterService } from '../validators/index';

export interface IService {
  _id: string;
  stockId: string;
  stock: any;
  organizationId: string;
  workshopId: string;
  workshop: any;
  status: string;
  arrivalKm: number;
  firstStep: IRegisterService;
  secondStep: IContinueService;
  createdAt: string;
  updatedAt: string;
}


export const getServicesByVehicleId = cache(async (stockId: string) => {
  const user = await getCurrentUser();
  if (!user) return null;

  try {

    const url = new URL(`${URL_API}/vendor-platform/vehicles/${stockId}/services`);

    const res = await axios(url.toString(), {
      headers: {
        Authorization: 'Bearer ' + user.accessToken,
      },
    });
    return res.data.data as IService[];
  } catch (error) {
    console.error('Error getting vehicles', error);
    return null;
  }
});