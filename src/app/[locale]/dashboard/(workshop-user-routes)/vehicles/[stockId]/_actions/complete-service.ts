
'use server';

import getCurrentUser from '@/actions/getCurrentUser';
import { URL_API } from '@/constants';
import axios from 'axios';


export const completeService = async (data: any) => {

  const user = await getCurrentUser();

  if (!user) {
    return {
      status: 'error',
      message: 'User not found',
    }
  }

  const { serviceId } = data;

  console.log('data', data);

  try {

    const { data: response } = await axios.patch(`${URL_API}/vendor-platform/service/${serviceId}/complete`, {
      ...data,
    }, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });

    console.log('response', response);

    return {
      status: 'success',
      message: response.message,
      data: response.data,
    }

  } catch (error: any) {
    return {
      status: 'error',
      message: error.message || 'Something went wrong',
    }
  }

};