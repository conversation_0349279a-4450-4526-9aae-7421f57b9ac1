import { UseFormReturn } from 'react-hook-form';
import * as z from "zod";

export type IRegisterService = z.infer<typeof registerServiceSchema>;

export type IServiceFormType = UseFormReturn<IRegisterService>;

export const registerServiceSchema = z.object({

  arrivalKm: z.string({ required_error: "Los kilómetros actuales son obligatorios" }),

  // Exterior
  bodyWork: z.enum(["good condition", "needs atention", "critical issue"], {
    required_error: "El estado de la carrocería es obligatorio",
  }),

  bodyWorkDetail: z.object({

    reviewScratches: z.object({
      dents: z.boolean().default(false),
      scratches: z.boolean().default(false),
      corrosion: z.boolean().default(false),
    }),

    paintCondition: z.enum(["good condition", "needs atention", "critical issue"], {
      required_error: "La pintura es obligatoria",
    }),
    paintConditionImages: z.array(z.instanceof(File), {
      required_error: "Las imágenes de la pintura son obligatorias",
    }).min(1, { message: "Debe seleccionar al menos una imagen" }),

    // blows: z.boolean().default(false),
    blows: z.enum(["good condition", "needs atention", "critical issue"], {
      required_error: "El estado de los golpes es obligatorio",
    }),
    // blowsImages: z.array(z.string()),
    blowsImages: z.array(z.instanceof(File), { message: "Las imágenes de los golpes son obligatorias" })

  })
    .refine(
      (data) => {
        if (!data.blows || data.blows !== "good condition") {
          // Si no es "good condition", valida que existan imágenes
          return data.blowsImages && data.blowsImages.length > 0;
        }
        // Si es "good condition", no requiere imágenes
        return true;
      },
      {
        message: "Las imágenes de los golpes son obligatorias si el estado no es bueno",
        path: ["blowsImages"], // Apunta al campo correcto
      }
    ),
  crystalsAndMirrors: z.enum(["good condition", "needs atention", "critical issue"], {
    required_error: "El estado de los cristales y espejos es obligatorio",
  }),
  tires: z.enum(["good condition", "needs atention", "critical issue"], {
    required_error: "El estado de las llantas es obligatorio",
  }),

  lights: z.enum(["good condition", "needs atention", "critical issue"], {
    required_error: "El estado de las luces es obligatorio",
  }),

  // Interior
  seats: z.enum(["good condition", "needs atention", "critical issue"], {
    required_error: "El estado de los asientos es obligatorio",
  }),

  seatsImages: z.array(z.instanceof(File), {
    required_error: "Las imágenes de los asientos son obligatorias",
  }).min(1, { message: "Debe seleccionar al menos una imagen" }),

  dashboard: z.enum(["good condition", "needs atention", "critical issue"], {
    required_error: "El estado del tablero es obligatorio",
  }),

  dashboardImages: z.array(z.instanceof(File), {
    required_error: "Las imágenes del tablero son obligatorias",
  }).min(1, { message: "Debe seleccionar al menos una imagen" }),

  controlSystem: z.enum(["good condition", "needs atention", "critical issue"], {
    required_error: "El sistema de control es obligatorio",
  }),

  electronicSystems: z.enum(["good condition", "needs atention", "critical issue"], {
    required_error: "El sistema eléctrico es obligatorio",
  }),

  // Mechanical System

  engine: z.enum(["good condition", "needs atention", "critical issue"], {
    required_error: "El motor es obligatorio",
  }),

  engineImages: z.array(z.instanceof(File), {
    required_error: "Las imágenes del motor son obligatorias",
  }).min(1, { message: "Debe seleccionar al menos una imagen" }),

  // engineImages: z.instanceof(FileList)
  //   .refine((list) => list.length > 0, "No files selected")
  //   .refine((list) => list.length <= 5, "Maximum 5 files allowed")
  //   .transform((list) => Array.from(list))
  //   .refine(
  //     (files) => {
  //       const allowedTypes: { [key: string]: boolean } = {
  //         "image/jpeg": true,
  //         "image/png": true,
  //         "application/pdf": true,
  //         "application/msword": true,
  //         "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
  //           true,
  //       };
  //       return files.every((file) => allowedTypes[file.type]);
  //     },
  //     { message: "Invalid file type. Allowed types: JPG, PNG, PDF, DOC, DOCX" }
  //   )
  //   .refine(
  //     (files) => {
  //       return files.every((file) => file.size <= fileSizeLimit);
  //     },
  //     {
  //       message: "File size should not exceed 5MB",
  //     }
  //   ),

  transmission: z.enum(["good condition", "needs atention", "critical issue"], {
    required_error: "La transmisión es obligatoria",
  }),

  battery: z.enum(["good condition", "needs atention", "critical issue"], {
    required_error: "La batería es obligatoria",
  }),

  batteryImages: z.array(z.instanceof(File), {
    required_error: "Las imágenes de la batería son obligatorias",
  }).min(1, { message: "Debe seleccionar al menos una imagen" }),

  batteryKW: z.string({ required_error: "La capacidad de la batería es obligatoria" }),

  brakes: z.enum(["good condition", "needs atention", "critical issue"], {
    required_error: "El sistema de frenos es obligatorio",
  }),

  suspension: z.enum(["good condition", "needs atention", "critical issue"], {
    required_error: "La suspensión es obligatoria",
  }),

  electricalSystem: z.enum(["good condition", "needs atention", "critical issue"], {
    required_error: "El sistema eléctrico es obligatorio",
  }),

  exhaustSystem: z.enum(["good condition", "needs atention", "critical issue"], {
    required_error: "El sistema de escape es obligatorio",
  }),

  // Fluids

  refrigerant: z.enum(["good condition", "needs atention", "critical issue"], {
    required_error: "El refrigerante es obligatorio",
  }),

  brakeFluid: z.enum(["good condition", "needs atention", "critical issue"], {
    required_error: "El líquido de frenos es obligatorio",
  }),

  powerSteeringFluid: z.enum(["good condition", "needs atention", "critical issue"], {
    required_error: "El líquido de dirección es obligatorio",
  }),

  windshieldWasherFluid: z.enum(["good condition", "needs atention", "critical issue"], {
    required_error: "El líquido limpiaparabrisas es obligatorio",
  }),

  // Extras

  // emergencyTools: z.enum(["good condition", "needs atention", "critical issue"], {
  //   required_error: "Las herramientas de emergencia son obligatorias",
  // }),

  emergency: z.object({
    emergencyTools: z.enum(["complete", "incomplete", "missing"], {
      required_error: "Las herramientas de emergencia son obligatorias",
    }),


    emergencyToolsImages: z.array(z.instanceof(File), {
      required_error: "Las imágenes de las herramientas de emergencia son obligatorias",
    })
  }).refine((data) => {
    if (data.emergencyTools !== "complete") {
      // Si no es "good condition", valida que existan imágenes
      return data.emergencyToolsImages.length > 0;
    }
    return true;
  }, {
    message: "Las imágenes de las herramientas de emergencia son obligatorias si no están completas",
    path: ["emergencyToolsImages"], // Apunta al campo correcto
  })

  // emergencyTools: z.enum(["complete", "incomplete", "missing"], {
  //   required_error: "Las herramientas de emergencia son obligatorias",
  // }),


  // emergencyToolsImages: z.array(z.instanceof(File), {
  //   required_error: "Las imágenes de las herramientas de emergencia son obligatorias",
  // })
  // .min(1, { message: "Debe seleccionar al menos una imagen" }),
  ,
  spareWheel: z.enum(["complete", "incomplete", "missing"], {
    required_error: "La llanta de repuesto es obligatoria",
  }),

  spareWheelImages: z.array(z.instanceof(File), {
    required_error: "Las imágenes de la llanta de repuesto son obligatorias",
  }),


})
// .refine(
//   (data) => {
//     if (data.emergencyTools !== "complete") {
//       // Si no es "good condition", valida que existan imágenes
//       return data.emergencyToolsImages.length > 0;
//     }
//     return true;
//   },
//   {
//     message: "Las imágenes de los golpes son obligatorias si el estado no es bueno",
//     path: ["emergencyToolsImages"], // Apunta al campo correcto
//   }
// )
// .superRefine((data, ctx) => {
//   // Validar que si emergencyTools no es "complete", se requieran imágenes
//   console.log('data', data);
//   if (data.emergencyTools !== "complete" && data.emergencyToolsImages.length === 0) {
//     ctx.addIssue({
//       code: "custom",
//       message: "Las imágenes de las herramientas de emergencia son obligatorias si no están completas",
//       path: ["emergencyToolsImages"], // Especifica el campo con el problema
//     });
//   }
// });


export type IContinueService = z.infer<typeof continueServiceSchema>;
export type IContinueServiceFormType = UseFormReturn<IContinueService>;


export const continueServiceSchema = z.object({
  serviceDetail: z.object({
    serviceType: z.enum(["preventive", "corrective", "other"], {
      required_error: "El tipo de servicio es obligatorio",
    }),
    specifications: z.object({
      otherServices: z.array(z.string()).default([]),
      oilChange: z.boolean().optional(),
      // oilChangeType: z.enum(["synthetic", "semi-synthetic", "mineral"]).optional(),
      oilChangeType: z.object({
        synthetic: z.boolean().optional(),
        semiSynthetic: z.boolean().optional(),
        mineral: z.boolean().optional(),
      }).optional(),
      oilImages: z.array(z.instanceof(File)).optional(),
      filterChange: z.boolean().optional(),
      // filterType: z.enum(["air", "oil"]).optional(),

      // Filter Type should be multiple selections if filterChange is true
      filterType: z.object({
        air: z.boolean().optional(),
        oil: z.boolean().optional(),
      }).optional(),

      filterImages: z.array(z.instanceof(File)).optional(),
      tuneUp: z.boolean().optional(),
      tuneUpImages: z.array(z.instanceof(File)).optional(),
      // tuneUpImages: z.array(z.string()).optional(),
      // tuneUpDetail: z.object({
      //   sparkPlugs: z.boolean().optional(),
      //   sparkPlugsType: z.enum(["iridium", "platinum", "standard"]).optional(),
      //   injectorsCleaning: z.boolean().optional(),
      //   valveAdjustment: z.boolean().optional(),
      // }).optional(),
      batteryChange: z.boolean().optional(),
      brakes: z.boolean().optional(),
      brakesDetail: z.object({
        padChange: z.boolean().optional(),
        discService: z.boolean().optional(),
        fluidChange: z.boolean().optional(),
        fluidType: z.enum(["DOT3", "DOT4", "DOT5"]).optional(),
      }).optional(),
      suspension: z.boolean().optional(),
      suspensionDetail: z.object({
        shockAbsorbers: z.boolean().optional(),
        springsAndBushings: z.boolean().optional(),
        boltsTightening: z.boolean().optional(),
      }).optional(),
      // steering: z.boolean().optional(),
      // steeringDetail: z.object({
      //   replaceTerminals: z.boolean().optional(),
      //   powerSteering: z.boolean().optional(),
      //   electronicPowerReview: z.boolean().optional(),
      // }).optional(),
      // tiresDetail: z.object({
      //   brandSizeEspecs: z.string().optional(),
      //   balanceAndAlignment: z.boolean().optional(),
      // }).optional(),
      // tires: z.boolean().optional(),
    })

      // refine for the images, the if their boolean field is true, the images are required, if not, they are not required

      .refine((data) => {
        if (data.oilChange) {
          // return data.oilImages;
          return data.oilImages && data.oilImages.length > 0;
        }
        return true;
      }, { message: "Las imágenes del cambio de aceite son obligatorias", path: ["oilImages"] })
      .refine((data) => {
        if (data.filterChange) {
          return data.filterImages && data.filterImages.length > 0;
        }
        return true;
      }, { message: "Las imágenes del cambio de filtro son obligatorias", path: ["filterImages"] })
      .refine((data) => {
        if (data.tuneUp) {
          return data.tuneUpImages && data.tuneUpImages.length > 0;
        }
        return true;
      }, { message: "Las imágenes de la afinación son obligatorias", path: ["tuneUpImages"] })
      // check if filterChange is true, then filterImages are required and filterType is required

      .superRefine((data, ctx) => {
        if (data.filterChange && !data.filterType) {
          ctx.addIssue({
            code: "custom",
            message: "El tipo de filtro es obligatorio",
            // path: ["serviceDetail", "specifications", "filterType"],
            path: ["filterType"],
          });
        }

        if (data.filterChange && (!data.filterImages || data.filterImages.length === 0)) {
          ctx.addIssue({
            code: "custom",
            message: "Las imágenes del cambio de filtro son obligatorias",
            path: ["filterImages"],
          });
        }

      })

  }),
  pendingServices: z.object({
    // Next service month should be 3, 6, 9 or 12 months from the current date
    nextServiceDate: z.string(),
    nextServiceKm: z.string(),
    criticalComponents: z.array(z.string()).optional(),
    recommendations: z.array(z.string()).optional(),
  }),
  costsAndTimes: z.object({
    totalCost: z.number().min(1, { message: "El costo total debe ser mayor a 0" }),
  }),
  signature: z.string().trim().min(1, { message: "La firma es obligatoria" }),
})
