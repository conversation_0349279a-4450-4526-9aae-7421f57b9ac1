import React from 'react';
// import type { Vehicle } from '../types/vehicle';
// import VehicleSVG from './VehicleSVG';
import { Button } from "@/components/ui/button";
import VehicleSVGV2 from '@/svgsComponents/VehicleSVG_V2';
import { VehicleDataCard } from '../_actions/getVehiclesQuery';

type VehicleListProps = {
  vehicles: VehicleDataCard[];
}

const statusMap: {
  [key: string]: string
} = {
  'pending': 'Pendiente',
  'completed': 'Completado',
}

// export const VehicleList = ({ vehicles }: VehicleListProps) => (
export default function VehicleList({ vehicles }: VehicleListProps) {
  return <div className="rounded-lg border overflow-hidden">
    <table className="w-full">
      <thead className="bg-gray-50 border-b">
        <tr>
          <th className="text-left px-4 py-3 text-sm font-medium">Vehicle</th>
          <th className="text-left px-4 py-3 text-sm font-medium">Contract #</th>
          <th className="text-left px-4 py-3 text-sm font-medium">VIN</th>
          <th className="text-left px-4 py-3 text-sm font-medium">Vehicle Status</th>
          <th className="text-left px-4 py-3 text-sm font-medium">Date Added</th>
          <th className="text-left px-4 py-3 text-sm font-medium">Action</th>
        </tr>
      </thead>
      <tbody>
        {vehicles.map((vehicle) => (
          <tr key={vehicle._id} className="border-b">
            <td className="px-4 py-3">
              <div className="flex items-center gap-3">
                <div className="w-12 h-8">
                  <VehicleSVGV2 color={vehicle.color} />
                </div>
                <div>
                  <p className="font-medium text-sm">{vehicle.brand}</p>
                  <p className="text-purple-600 text-sm">#{vehicle.contractNumber || vehicle.carNumber}</p>
                </div>
              </div>
            </td>
            <td className="px-4 py-3 text-sm">
              <span className="text-gray-500">{vehicle.carNumber || 'N/A'}</span>
            </td>
            <td className="px-4 py-3 text-sm">{vehicle.vin}</td>
            <td className="px-4 py-3">
              <span className="text-xs px-2 py-0.5 rounded bg-yellow-100 text-yellow-800">
                {/* {vehicle.status} */}
                {
                  // vehicle.service?.status
                  vehicle.service && statusMap[vehicle.service.status]
                }
              </span>
            </td>
            {/* <td className="px-4 py-3 text-sm">{vehicle.dateAdded}</td> */}
            <td className="px-4 py-3">
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 3.33337V12.6667" stroke="#667085" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round" />
                  <path d="M3.3335 8H12.6668" stroke="#667085" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </Button>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  </div>
};
