import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import useDebounce from '@/hooks/useDebounce';
import SearchInput from '@/components/Inputs/SearchInput';

interface VehicleSearcherProps {
  hideTitle?: boolean;
  placeholder?: string;
}

export default function VehicleSearcher({ hideTitle = false, placeholder }: VehicleSearcherProps) {
  const searchParams = useSearchParams();

  const pathname = usePathname();
  const router = useRouter();

  const onChange = useDebounce((term: string, param: string) => {
    const params = new URLSearchParams(searchParams);

    if (term && term.trim().length > 0) {
      params.set(param, term);
    } else {
      params.delete(param);
    }
    router.replace(`${pathname}?${params.toString()}`);
  }, 500);

  return (
    <>
      <div className="flex  items-center  gap-3 flex-wrap ">
        {/* <h1 className="font-bold text-interBold32 font-inter ">Busquedas</h1> */}
        {
          !hideTitle && <h1 className="font-bold text-interBold32 font-inter ">Busquedas</h1>
        }
        <SearchInput onChange={(term) => onChange(term, 'q')} placeholder={placeholder || 'Buscar'} param="q" />
      </div>
    </>
  )

}