import Link from 'next/link';
import { VehicleDataCard } from '../_actions/getVehiclesQuery';
import VehicleCard from './vehicleCard';

type VehicleGridProps = {
  vehicles: VehicleDataCard[];
}

export default function VehicleGrid({ vehicles }: VehicleGridProps) {
  return <div
    className="grid gap-4"
    style={{ gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))' }}
  >
    {vehicles.map((vehicle) => (
      <Link
        href={`/dashboard/vehicles/${vehicle._id}`}
        key={vehicle._id}
      >
        <VehicleCard key={vehicle._id} vehicle={vehicle} />
      </Link>
    ))}
  </div>
}
