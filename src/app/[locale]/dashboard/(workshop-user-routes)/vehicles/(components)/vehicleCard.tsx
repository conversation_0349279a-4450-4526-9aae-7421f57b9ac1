import VehicleSVGV2 from '@/svgsComponents/VehicleSVG_V2';
import React from 'react';
import { VehicleDataCard } from '../_actions/getVehiclesQuery';
import { svgColors } from '@/constants';

type VehicleCardProps = {
  vehicle: VehicleDataCard;
}

export default function VehicleCard({ vehicle }: VehicleCardProps) {
  return (
    <div className="border rounded-[12px] p-4 bg-white w-full">
      <div className="flex flex-col relative">
        <div className="flex justify-between items-center w-full">
          <h3 className="text-[#0F172A] text-base font-semibold">{vehicle.brand} {vehicle.model}</h3>
        </div>
        <div className=" mt-3 flex h-full">
          {vehicle.service?.status && (
            <span className="absolute top-1 right-0 text-xs  z-[5] rounded-full px-2 bg-[#E1FB45] text-yellow-800 font-medium">
              En taller
            </span>
          )}

          <div className="w-[150px] h-full">
            <VehicleSVGV2 color={svgColors[vehicle.color.toUpperCase()] || 'white'} />
          </div>

          <span className="text-[#7C3AED] absolute bottom-4 right-1 text-base font-medium">#{vehicle.contractNumber || vehicle.carNumber}</span>


        </div>
      </div>
    </div>
  )
};
