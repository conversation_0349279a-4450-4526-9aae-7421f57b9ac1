'use server';

import { URL_API } from '@/constants';
import { cache } from 'react';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
// import { VehicleDataCard } from './getVehiclesQuery';


export interface VehicleDetail {
  _id: string;
  brand: string;
  model: string;
  color: string;
  status: string;
  vin: string;
  km: string;
  carNumber: string;
  contractNumber: string;
  carPlates: {
    plates: string;
  }
  // service?: {
  //   status: string;
  // };
  createdAt: string;
  updatedAt: string;
}


export const getVehicleDetailById = cache(async (stockId: string) => {
  const user = await getCurrentUser();
  if (!user) return null;

  try {
    // const res = await axios(`${URL_API}/stock/search?${param}=${query}`, {
    const res = await axios(`${URL_API}/vendor-platform/vehicles/${stockId}`, {
      headers: {
        Authorization: 'Bearer ' + user.accessToken,
      },
    });
    return res.data.data as VehicleDetail;
  } catch (error) {
    console.error('Error getting vehicles', error);
    return null;
  }
});