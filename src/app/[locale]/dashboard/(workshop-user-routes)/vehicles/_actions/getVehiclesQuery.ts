'use server';

import { URL_API } from '@/constants';
import { cache } from 'react';
import axios from 'axios';
import getUserById, { getSession } from '@/actions/getUserById';
// import { VehicleCard } from './getAllVehicles';

export interface VehicleDataCard {
  _id: string;
  // name: string;
  brand: string;
  model: string;
  color: string;
  // status: string;
  vin: string;
  carNumber: string;
  contractNumber: string;
  // plates: string;
  carPlates: {
    plates: string;
    // state: string;
  },
  service?: {
    status: string | 'pending' | 'completed';
  };
}

export const getSearchQuery = cache(async (query: string, param: string) => {
  try {
    // Try to get the session first
    const session = await getSession();
    if (session?.user?.accessToken) {
      console.log("Using session token for vehicles query");
      const res = await axios(`${URL_API}/vendor-platform/vehicles?${param}=${query}`, {
        headers: {
          Authorization: 'Bearer ' + session.user.accessToken,
          'Content-Type': 'application/json'
        },
      });
      return res.data.data as VehicleDataCard[];
    }

    // If no session, try to get the user
    const user = await getUserById();
    if (!user) {
      console.error("No user found for vehicles query");
      return null;
    }

    console.log("Using user token for vehicles query");
    const res = await axios(`${URL_API}/vendor-platform/vehicles?${param}=${query}`, {
      headers: {
        Authorization: 'Bearer ' + user.accessToken,
        'Content-Type': 'application/json'
      },
    });
    return res.data.data as VehicleDataCard[];
  } catch (error) {
    console.error('Error getting vehicles', error);
    return null;
  }
});