'use server';

import getCurrentUser from "@/actions/getCurrentUser";

interface QrScanActionResponse {
  actionAvailable: boolean;
  currentPhysicalStatus?: string;
  nextPhysicalStatusToDisplay?: string;
  nextStepOptions?: { value: string; label: string }[];
  confirmationToken?: string;
  message?: string;
  currentStatusDealershipName?: string;
}

interface QrStatusChangeResponse {
  success: boolean;
  message: string;
  newPhysicalStatus?: string;
}

interface QrScanHistoryItem {
  _id: string;
  vehicleId: string;
  userId: {
    _id: string;
    name?: string;
    email?: string;
  };
  statusChangedFrom?: string;
  statusChangedTo?: string;
  deviceInfo?: string;
  actionToken?: string;
  actionType: string;
  notes?: string;
  photoDocId?: string;
  photo?: {
    _id: string;
    path: string;
    url?: string;
  };
  vendorRegion?: string;
  vendorWorkshopName?: string;
  scanTime: string;
}

interface QrStatusChangeOptions {
  vehicleId: string;
  confirmedNextStatus: string;
  confirmationToken: string;
  photoPath?: string;
  vendorRegion?: string;
  vendorWorkshopName?: string;
}

interface UploadPhotoResponse {
  success: boolean;
  message: string;
  filePath: string;
  documentId?: string;
}

// Function to get QR scan history for a vehicle
export async function getQrScanHistory(
  vehicleId: string
): Promise<QrScanHistoryItem[]> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/vendor-platform/vehicles/${vehicleId}/qr-scan-history`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${user.accessToken}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Error fetching QR scan history');
    }

    const data = await response.json();
    return data?.data || [];
  } catch (error) {
    console.error('Error fetching QR scan history:', error);
    return []; // Return empty array on error
  }
}

// Function to initiate QR scan action
export async function initiateQrScanAction(
  vehicleId: string,
  qrScanToken: string
): Promise<QrScanActionResponse> {
  try {
    const user = await getCurrentUser();
    if (!user) return { actionAvailable: false };
    // Fetch API endpoint with authorization
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/vendor-platform/vehicles/${vehicleId}/initiate-qr-scan-action`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${user.accessToken}`,
      },
      body: JSON.stringify({ qrScanToken, vendorUserName: user?.name, source: 'vendor-panel'}),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Error initiating QR scan action');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error initiating QR scan action:', error);
    throw error;
  }
}

// Function to upload QR scan photo
export async function uploadQrScanPhoto(
  vehicleId: string,
  formData: FormData
): Promise<UploadPhotoResponse> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/vendor-platform/vehicles/${vehicleId}/upload-qr-photo`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Error uploading photo');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error uploading QR scan photo:', error);
    throw error;
  }
}

// Function to confirm QR status change
export async function confirmQrStatusChange(
  options: QrStatusChangeOptions
): Promise<QrStatusChangeResponse> {
  const { vehicleId, confirmedNextStatus, confirmationToken, photoPath, vendorRegion, vendorWorkshopName } = options;
  const user = await getCurrentUser();
  if (!user) return { success: false, message: 'User not found' };
  try {
    // Prepare payload
    const payload: any = {
      confirmedNextStatus,
      confirmationToken,
      photoPath,
      vendorUserName: user?.name,
    };

    if (vendorRegion) payload.vendorRegion = vendorRegion;
    if (vendorWorkshopName) payload.vendorWorkshopName = vendorWorkshopName;

    // Fetch API endpoint with authorization
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/vendor-platform/vehicles/${vehicleId}/confirm-qr-status-change`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${user.accessToken}`,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Error confirming physical status change');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error confirming physical status change:', error);
    throw error;
  }
} 