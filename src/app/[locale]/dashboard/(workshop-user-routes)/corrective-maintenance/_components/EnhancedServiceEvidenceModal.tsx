'use client';

import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Upload, 
  X, 
  Camera, 
  Video, 
  FileText, 
  CheckCircle, 
  AlertTriangle,
  Loader2,
  Eye
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useRealTimeTracking } from '@/hooks/useRealTimeTracking';

interface EnhancedServiceEvidenceModalProps {
  isOpen: boolean;
  onClose: () => void;
  serviceId: string;
  serviceName: string;
  currentPhase: string;
  isCompletion?: boolean; // true if this is for service completion
  onSuccess?: () => void;
}

interface FilePreview {
  file: File;
  url: string;
  type: 'image' | 'video';
}

export default function EnhancedServiceEvidenceModal({
  isOpen,
  onClose,
  serviceId,
  serviceName,
  currentPhase,
  isCompletion = false,
  onSuccess,
}: EnhancedServiceEvidenceModalProps) {
  const [notes, setNotes] = useState('');
  const [evidenceNotes, setEvidenceNotes] = useState('');
  const [qualityCheckPassed, setQualityCheckPassed] = useState<boolean | undefined>(undefined);
  const [actualCost, setActualCost] = useState<string>('');
  
  const [completionPhotos, setCompletionPhotos] = useState<FilePreview[]>([]);
  const [completionVideos, setCompletionVideos] = useState<FilePreview[]>([]);
  const [qualityCheckPhotos, setQualityCheckPhotos] = useState<FilePreview[]>([]);
  const [phasePhotos, setPhasePhotos] = useState<FilePreview[]>([]);
  const [phaseVideos, setPhaseVideos] = useState<FilePreview[]>([]);

  const photoInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);
  const qcPhotoInputRef = useRef<HTMLInputElement>(null);

  const { toast } = useToast();
  const { isLoading, updateServicePhase, completeServiceWithEvidence } = useRealTimeTracking();

  const handleFileSelect = (
    files: FileList | null, 
    type: 'completion-photos' | 'completion-videos' | 'qc-photos' | 'phase-photos' | 'phase-videos'
  ) => {
    if (!files) return;

    const newFiles: FilePreview[] = [];
    
    Array.from(files).forEach((file) => {
      const url = URL.createObjectURL(file);
      const fileType = file.type.startsWith('image/') ? 'image' : 'video';
      
      newFiles.push({
        file,
        url,
        type: fileType,
      });
    });

    switch (type) {
      case 'completion-photos':
        setCompletionPhotos(prev => [...prev, ...newFiles]);
        break;
      case 'completion-videos':
        setCompletionVideos(prev => [...prev, ...newFiles]);
        break;
      case 'qc-photos':
        setQualityCheckPhotos(prev => [...prev, ...newFiles]);
        break;
      case 'phase-photos':
        setPhasePhotos(prev => [...prev, ...newFiles]);
        break;
      case 'phase-videos':
        setPhaseVideos(prev => [...prev, ...newFiles]);
        break;
    }
  };

  const removeFile = (
    index: number, 
    type: 'completion-photos' | 'completion-videos' | 'qc-photos' | 'phase-photos' | 'phase-videos'
  ) => {
    switch (type) {
      case 'completion-photos':
        setCompletionPhotos(prev => {
          URL.revokeObjectURL(prev[index].url);
          return prev.filter((_, i) => i !== index);
        });
        break;
      case 'completion-videos':
        setCompletionVideos(prev => {
          URL.revokeObjectURL(prev[index].url);
          return prev.filter((_, i) => i !== index);
        });
        break;
      case 'qc-photos':
        setQualityCheckPhotos(prev => {
          URL.revokeObjectURL(prev[index].url);
          return prev.filter((_, i) => i !== index);
        });
        break;
      case 'phase-photos':
        setPhasePhotos(prev => {
          URL.revokeObjectURL(prev[index].url);
          return prev.filter((_, i) => i !== index);
        });
        break;
      case 'phase-videos':
        setPhaseVideos(prev => {
          URL.revokeObjectURL(prev[index].url);
          return prev.filter((_, i) => i !== index);
        });
        break;
    }
  };

  const handleSubmit = async () => {
    try {
      if (isCompletion) {
        // Complete service with evidence
        const success = await completeServiceWithEvidence(serviceId, {
          notes,
          qualityCheckPassed,
          actualCost: actualCost ? parseFloat(actualCost) : undefined,
          evidenceNotes,
          completionPhotos: completionPhotos.map(p => p.file),
          completionVideos: completionVideos.map(v => v.file),
          qualityCheckPhotos: qualityCheckPhotos.map(p => p.file),
        });

        if (success) {
          onSuccess?.();
          handleClose();
        }
      } else {
        // Update phase with evidence
        const success = await updateServicePhase(serviceId, currentPhase, {
          photos: phasePhotos.map(p => p.file),
          videos: phaseVideos.map(v => v.file),
          notes,
        });

        if (success) {
          onSuccess?.();
          handleClose();
        }
      }
    } catch (error) {
      console.error('Error submitting evidence:', error);
      toast({
        title: 'Error',
        description: 'No se pudo enviar la evidencia',
        variant: 'destructive',
      });
    }
  };

  const handleClose = () => {
    // Clean up object URLs
    [...completionPhotos, ...completionVideos, ...qualityCheckPhotos, ...phasePhotos, ...phaseVideos]
      .forEach(file => URL.revokeObjectURL(file.url));
    
    // Reset form
    setNotes('');
    setEvidenceNotes('');
    setQualityCheckPassed(undefined);
    setActualCost('');
    setCompletionPhotos([]);
    setCompletionVideos([]);
    setQualityCheckPhotos([]);
    setPhasePhotos([]);
    setPhaseVideos([]);
    
    onClose();
  };

  const FilePreviewGrid = ({ 
    files, 
    onRemove, 
    title 
  }: { 
    files: FilePreview[]; 
    onRemove: (index: number) => void; 
    title: string;
  }) => (
    <div className="space-y-2">
      <Label className="text-sm font-medium">{title}</Label>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
        {files.map((file, index) => (
          <div key={index} className="relative group">
            <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
              {file.type === 'image' ? (
                <img 
                  src={file.url} 
                  alt={`Preview ${index}`}
                  className="w-full h-full object-cover"
                />
              ) : (
                <video 
                  src={file.url}
                  className="w-full h-full object-cover"
                  controls={false}
                />
              )}
            </div>
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 rounded-lg flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 flex gap-2">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => window.open(file.url, '_blank')}
                >
                  <Eye className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => onRemove(index)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="absolute top-1 right-1">
              <Badge variant="secondary" className="text-xs">
                {file.type === 'image' ? <Camera className="h-3 w-3" /> : <Video className="h-3 w-3" />}
              </Badge>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {isCompletion ? <CheckCircle className="h-5 w-5 text-green-500" /> : <FileText className="h-5 w-5" />}
            {isCompletion ? 'Completar Servicio' : 'Actualizar Fase del Servicio'}
          </DialogTitle>
          <p className="text-sm text-gray-600">
            Servicio: {serviceName} | Fase: {currentPhase}
          </p>
        </DialogHeader>

        <div className="space-y-6">
          {/* Notes Section */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notas del Servicio</Label>
            <Textarea
              id="notes"
              placeholder="Describe el trabajo realizado, observaciones, etc."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
            />
          </div>

          {/* Completion-specific fields */}
          {isCompletion && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="actualCost">Costo Real (opcional)</Label>
                  <input
                    id="actualCost"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    value={actualCost}
                    onChange={(e) => setActualCost(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Control de Calidad</Label>
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant={qualityCheckPassed === true ? "default" : "outline"}
                      onClick={() => setQualityCheckPassed(true)}
                      className="flex-1"
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Aprobado
                    </Button>
                    <Button
                      type="button"
                      variant={qualityCheckPassed === false ? "destructive" : "outline"}
                      onClick={() => setQualityCheckPassed(false)}
                      className="flex-1"
                    >
                      <AlertTriangle className="h-4 w-4 mr-2" />
                      Rechazado
                    </Button>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="evidenceNotes">Notas de Evidencia</Label>
                <Textarea
                  id="evidenceNotes"
                  placeholder="Describe las evidencias adjuntas..."
                  value={evidenceNotes}
                  onChange={(e) => setEvidenceNotes(e.target.value)}
                  rows={2}
                />
              </div>
            </>
          )}

          {/* File Upload Sections */}
          <div className="space-y-6">
            {isCompletion ? (
              <>
                {/* Completion Photos */}
                <Card>
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Label>Fotos de Finalización</Label>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => photoInputRef.current?.click()}
                        >
                          <Camera className="h-4 w-4 mr-2" />
                          Agregar Fotos
                        </Button>
                      </div>
                      
                      <input
                        ref={photoInputRef}
                        type="file"
                        accept="image/*"
                        multiple
                        className="hidden"
                        onChange={(e) => handleFileSelect(e.target.files, 'completion-photos')}
                      />
                      
                      {completionPhotos.length > 0 && (
                        <FilePreviewGrid
                          files={completionPhotos}
                          onRemove={(index) => removeFile(index, 'completion-photos')}
                          title="Fotos de Finalización"
                        />
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Quality Check Photos */}
                <Card>
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Label>Fotos de Control de Calidad</Label>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => qcPhotoInputRef.current?.click()}
                        >
                          <Camera className="h-4 w-4 mr-2" />
                          Agregar Fotos
                        </Button>
                      </div>
                      
                      <input
                        ref={qcPhotoInputRef}
                        type="file"
                        accept="image/*"
                        multiple
                        className="hidden"
                        onChange={(e) => handleFileSelect(e.target.files, 'qc-photos')}
                      />
                      
                      {qualityCheckPhotos.length > 0 && (
                        <FilePreviewGrid
                          files={qualityCheckPhotos}
                          onRemove={(index) => removeFile(index, 'qc-photos')}
                          title="Fotos de Control de Calidad"
                        />
                      )}
                    </div>
                  </CardContent>
                </Card>
              </>
            ) : (
              /* Phase Evidence */
              <Card>
                <CardContent className="p-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label>Evidencia de la Fase</Label>
                      <div className="flex gap-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => photoInputRef.current?.click()}
                        >
                          <Camera className="h-4 w-4 mr-2" />
                          Fotos
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => videoInputRef.current?.click()}
                        >
                          <Video className="h-4 w-4 mr-2" />
                          Videos
                        </Button>
                      </div>
                    </div>
                    
                    <input
                      ref={photoInputRef}
                      type="file"
                      accept="image/*"
                      multiple
                      className="hidden"
                      onChange={(e) => handleFileSelect(e.target.files, 'phase-photos')}
                    />
                    
                    <input
                      ref={videoInputRef}
                      type="file"
                      accept="video/*"
                      multiple
                      className="hidden"
                      onChange={(e) => handleFileSelect(e.target.files, 'phase-videos')}
                    />
                    
                    {(phasePhotos.length > 0 || phaseVideos.length > 0) && (
                      <FilePreviewGrid
                        files={[...phasePhotos, ...phaseVideos]}
                        onRemove={(index) => {
                          if (index < phasePhotos.length) {
                            removeFile(index, 'phase-photos');
                          } else {
                            removeFile(index - phasePhotos.length, 'phase-videos');
                          }
                        }}
                        title="Evidencia de la Fase"
                      />
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Validation Alert */}
          {isCompletion && qualityCheckPassed === false && (
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                El servicio ha sido marcado como rechazado en el control de calidad. 
                Asegúrate de incluir notas detalladas sobre los problemas encontrados.
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            Cancelar
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            {isCompletion ? 'Completar Servicio' : 'Actualizar Fase'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
