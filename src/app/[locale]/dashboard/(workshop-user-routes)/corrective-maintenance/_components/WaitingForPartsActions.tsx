'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import {
  Package,
  Clock,
  CheckCircle,
  AlertTriangle,
  Truck,
  RefreshCw,
  Play
} from 'lucide-react';
import { CorrectiveMaintenanceOrder, CorrectiveService } from '../types';

interface WaitingForPartsActionsProps {
  order: CorrectiveMaintenanceOrder;
  onManageParts: (serviceId: string) => void;
  onContinueWork?: () => void;
  onRefreshOrder?: () => void;
}

export default function WaitingForPartsActions({
  order,
  onManageParts,
  onContinueWork,
  onRefreshOrder
}: WaitingForPartsActionsProps) {
  const { toast } = useToast();

  // Analyze services and parts
  const servicesWithParts = order.services?.filter(service =>
    service.parts && service.parts.length > 0
  ) || [];

  const pendingParts = servicesWithParts.flatMap(service =>
    service.parts.filter(part => part.availability === 'pending')
  );

  const availableParts = servicesWithParts.flatMap(service =>
    service.parts.filter(part => part.availability === 'available')
  );

  const servicesWaitingForParts = order.services?.filter(service =>
    service.status === 'waiting-for-parts'
  ) || [];

  const servicesReadyToContinue = order.services?.filter(service =>
    service.status === 'not-started' ||
    (service.status === 'waiting-for-parts' &&
     service.parts &&
     service.parts.every(part => part.availability === 'available'))
  ) || [];

  const handleCheckPartsAvailability = () => {
    toast({
      title: 'Verificando disponibilidad',
      description: 'Consultando el estado actual de las refacciones...',
    });

    // Simulate checking parts availability
    setTimeout(() => {
      if (onRefreshOrder) {
        onRefreshOrder();
      }
      toast({
        title: 'Verificación completada',
        description: 'Estado de refacciones actualizado',
      });
    }, 1500);
  };

  const handleContinueAvailableServices = () => {
    if (onContinueWork) {
      onContinueWork();
    }
  };

  return (
    <div className="space-y-4">
      {/* Status Summary */}
      <Alert>
        <Package className="h-4 w-4" />
        <AlertDescription>
          <div className="flex items-center justify-between">
            <div>
              <strong>Orden esperando refacciones:</strong> {pendingParts.length} refacción{pendingParts.length !== 1 ? 'es' : ''} pendiente{pendingParts.length !== 1 ? 's' : ''},
              {servicesWaitingForParts.length} servicio{servicesWaitingForParts.length !== 1 ? 's' : ''} en pausa.
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCheckPartsAvailability}
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Verificar
            </Button>
          </div>
        </AlertDescription>
      </Alert>

      {/* Services Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Services waiting for parts */}
        {servicesWaitingForParts.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <Clock className="h-4 w-4 text-yellow-600" />
                Servicios en Pausa ({servicesWaitingForParts.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {servicesWaitingForParts.map(service => {
                const pendingPartsForService = service.parts?.filter(part => part.availability === 'pending') || [];
                return (
                  <div key={service._id} className="p-2 bg-yellow-50 rounded border">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium">{service.serviceName}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onManageParts(service._id)}
                      >
                        <Package className="h-3 w-3" />
                      </Button>
                    </div>
                    <div className="text-xs text-gray-600">
                      {pendingPartsForService.length} refacción{pendingPartsForService.length !== 1 ? 'es' : ''} pendiente{pendingPartsForService.length !== 1 ? 's' : ''}
                    </div>
                    {pendingPartsForService.length > 0 && (
                      <div className="mt-1 space-y-1">
                        {pendingPartsForService.slice(0, 2).map((part, index) => (
                          <div key={index} className="flex items-center justify-between text-xs">
                            <span>{part.name} (x{part.quantity})</span>
                            {part.eta && (
                              <div className="flex items-center gap-1 text-gray-500">
                                <Truck className="h-3 w-3" />
                                <span>{new Date(part.eta).toLocaleDateString('es-ES')}</span>
                              </div>
                            )}
                          </div>
                        ))}
                        {pendingPartsForService.length > 2 && (
                          <p className="text-xs text-gray-500">
                            +{pendingPartsForService.length - 2} más...
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </CardContent>
          </Card>
        )}

        {/* Services ready to continue */}
        {servicesReadyToContinue.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <CheckCircle className="h-4 w-4 text-green-600" />
                Servicios Listos ({servicesReadyToContinue.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {servicesReadyToContinue.map(service => (
                <div key={service._id} className="p-2 bg-green-50 rounded border">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium">{service.serviceName}</span>
                    <Badge className="bg-green-100 text-green-800">
                      Listo
                    </Badge>
                  </div>
                  <div className="text-xs text-gray-600">
                    {service.parts?.length ?
                      `${service.parts.length} refacción${service.parts.length !== 1 ? 'es' : ''} disponible${service.parts.length !== 1 ? 's' : ''}` :
                      'Sin refacciones requeridas'
                    }
                  </div>
                </div>
              ))}
              {servicesReadyToContinue.length > 0 && onContinueWork && (
                <Button
                  size="sm"
                  onClick={handleContinueAvailableServices}
                  className="w-full mt-2"
                >
                  <Play className="h-3 w-3 mr-1" />
                  Continuar Servicios Disponibles
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">Acciones Rápidas</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const firstServiceWithParts = servicesWithParts[0];
                if (firstServiceWithParts) {
                  onManageParts(firstServiceWithParts._id);
                }
              }}
              disabled={servicesWithParts.length === 0}
            >
              <Package className="h-3 w-3 mr-1" />
              Gestionar Todas las Refacciones
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleCheckPartsAvailability}
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Verificar Disponibilidad
            </Button>

            {availableParts.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  toast({
                    title: 'Notificación enviada',
                    description: `Se notificó al taller que ${availableParts.length} refacción${availableParts.length !== 1 ? 'es están' : ' está'} disponible${availableParts.length !== 1 ? 's' : ''}.`,
                  });
                }}
              >
                <CheckCircle className="h-3 w-3 mr-1" />
                Notificar Refacciones Disponibles
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Parts Summary */}
      <div className="grid grid-cols-3 gap-4 text-center">
        <div className="p-3 bg-yellow-50 rounded border">
          <div className="text-lg font-bold text-yellow-600">{pendingParts.length}</div>
          <div className="text-xs text-gray-600">Pendientes</div>
        </div>
        <div className="p-3 bg-green-50 rounded border">
          <div className="text-lg font-bold text-green-600">{availableParts.length}</div>
          <div className="text-xs text-gray-600">Disponibles</div>
        </div>
        <div className="p-3 bg-blue-50 rounded border">
          <div className="text-lg font-bold text-blue-600">{servicesReadyToContinue.length}</div>
          <div className="text-xs text-gray-600">Servicios Listos</div>
        </div>
      </div>
    </div>
  );
}
