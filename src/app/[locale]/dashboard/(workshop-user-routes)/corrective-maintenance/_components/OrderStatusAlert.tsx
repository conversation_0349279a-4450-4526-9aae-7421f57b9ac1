'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  AlertTriangle,
  Clock,
  Package,
  CheckCircle,
  Info,
  Wrench
} from 'lucide-react';
import { CorrectiveMaintenanceOrder } from '../types';

interface OrderStatusAlertProps {
  order: CorrectiveMaintenanceOrder;
  onStartWork?: () => void;
  onManageParts?: () => void;
}

export default function OrderStatusAlert({ order, onStartWork, onManageParts }: OrderStatusAlertProps) {
  const servicesWithParts = order.services?.filter(service =>
    service.parts && service.parts.length > 0
  ) || [];

  const pendingParts = servicesWithParts.flatMap(service =>
    service.parts.filter(part => part.availability === 'pending')
  );

  const waitingForPartsServices = order.services?.filter(service =>
    service.status === 'waiting-for-parts'
  ) || [];

  const inProgressServices = order.services?.filter(service =>
    service.status === 'in-progress'
  ) || [];

  const completedServices = order.services?.filter(service =>
    service.status === 'completed'
  ) || [];

  // Determine what alert to show based on order status and parts availability
  const getAlertInfo = () => {
    if (order.status === 'waiting-for-parts') {
      return {
        type: 'warning' as const,
        icon: <Package className="h-4 w-4" />,
        title: 'Orden esperando refacciones',
        description: `${pendingParts.length} refacción${pendingParts.length !== 1 ? 'es' : ''} pendiente${pendingParts.length !== 1 ? 's' : ''}. Los servicios relacionados están en pausa hasta que lleguen las piezas.`,
        actions: onManageParts ? [
          {
            label: 'Gestionar Refacciones',
            onClick: onManageParts,
            variant: 'outline' as const
          }
        ] : []
      };
    }

    if (order.status === 'in-progress') {
      if (waitingForPartsServices.length > 0) {
        return {
          type: 'info' as const,
          icon: <Info className="h-4 w-4" />,
          title: 'Trabajo en progreso con refacciones pendientes',
          description: `${inProgressServices.length} servicio${inProgressServices.length !== 1 ? 's' : ''} en progreso, ${waitingForPartsServices.length} esperando refacciones.`,
          actions: onManageParts ? [
            {
              label: 'Ver Refacciones',
              onClick: onManageParts,
              variant: 'outline' as const
            }
          ] : []
        };
      }

      return {
        type: 'success' as const,
        icon: <Wrench className="h-4 w-4" />,
        title: 'Trabajo en progreso',
        description: `${inProgressServices.length} servicio${inProgressServices.length !== 1 ? 's' : ''} en progreso. ${completedServices.length} completado${completedServices.length !== 1 ? 's' : ''}.`,
        actions: []
      };
    }

    if (order.status === 'approved' && pendingParts.length > 0) {
      return {
        type: 'warning' as const,
        icon: <AlertTriangle className="h-4 w-4" />,
        title: 'Refacciones pendientes detectadas',
        description: `Se detectaron ${pendingParts.length} refacción${pendingParts.length !== 1 ? 'es' : ''} pendiente${pendingParts.length !== 1 ? 's' : ''}. Al iniciar el trabajo, algunos servicios quedarán en espera.`,
        actions: [
          ...(onStartWork ? [{
            label: 'Iniciar Trabajo',
            onClick: onStartWork,
            variant: 'default' as const
          }] : []),
          ...(onManageParts ? [{
            label: 'Revisar Refacciones',
            onClick: onManageParts,
            variant: 'outline' as const
          }] : [])
        ]
      };
    }

    if (order.status === 'completed') {
      return {
        type: 'success' as const,
        icon: <CheckCircle className="h-4 w-4" />,
        title: 'Orden completada',
        description: `Todos los servicios han sido completados exitosamente.`,
        actions: []
      };
    }

    return null;
  };

  const alertInfo = getAlertInfo();

  if (!alertInfo) {
    return null;
  }

  const getAlertClassName = (type: string) => {
    switch (type) {
      case 'warning': return 'border-yellow-200 bg-yellow-50';
      case 'success': return 'border-green-200 bg-green-50';
      case 'info': return 'border-blue-200 bg-blue-50';
      default: return '';
    }
  };

  const getIconColor = (type: string) => {
    switch (type) {
      case 'warning': return 'text-yellow-600';
      case 'success': return 'text-green-600';
      case 'info': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <Alert className={getAlertClassName(alertInfo.type)}>
      <div className={getIconColor(alertInfo.type)}>
        {alertInfo.icon}
      </div>
      <AlertDescription>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h4 className="font-medium mb-1">{alertInfo.title}</h4>
            <p className="text-sm">{alertInfo.description}</p>

            {/* Additional details for waiting-for-parts status */}
            {order.status === 'waiting-for-parts' && pendingParts.length > 0 && (
              <div className="mt-3 space-y-2">
                <p className="text-xs font-medium">Refacciones pendientes:</p>
                <div className="space-y-1">
                  {pendingParts.slice(0, 3).map((part, index) => (
                    <div key={index} className="flex items-center justify-between text-xs">
                      <span>{part.name} (x{part.quantity})</span>
                      {part.eta && (
                        <Badge variant="outline">
                          ETA: {new Date(part.eta).toLocaleDateString('es-ES')}
                        </Badge>
                      )}
                    </div>
                  ))}
                  {pendingParts.length > 3 && (
                    <p className="text-xs text-gray-500">
                      +{pendingParts.length - 3} refacción{pendingParts.length - 3 !== 1 ? 'es' : ''} más...
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Service status breakdown for in-progress orders */}
            {order.status === 'in-progress' && (
              <div className="mt-3 flex gap-2 flex-wrap">
                {inProgressServices.length > 0 && (
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    {inProgressServices.length} en progreso
                  </Badge>
                )}
                {waitingForPartsServices.length > 0 && (
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                    {waitingForPartsServices.length} esperando refacciones
                  </Badge>
                )}
                {completedServices.length > 0 && (
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    {completedServices.length} completado{completedServices.length !== 1 ? 's' : ''}
                  </Badge>
                )}
              </div>
            )}
          </div>

          {/* Action buttons */}
          {alertInfo.actions.length > 0 && (
            <div className="flex gap-2 ml-4">
              {alertInfo.actions.map((action, index) => (
                <Button
                  key={index}
                  variant={action.variant}
                  size="sm"
                  onClick={action.onClick}
                >
                  {action.label}
                </Button>
              ))}
            </div>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
}
