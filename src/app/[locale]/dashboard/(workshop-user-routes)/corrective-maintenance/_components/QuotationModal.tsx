'use client';

import React, { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, Calculator, Plus, Trash2, Upload, X, Image, Package, AlertTriangle, CheckCircle, Clock } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { CorrectiveMaintenanceOrder, formatCurrency, formatDuration } from '../types';
import { shouldEnterInventoryFlow, getInventoryNextSteps, generateInventoryNotifications } from '../_utils/inventoryFlow';

// Create dynamic schema based on whether we're closing the order
const createQuotationSchema = (isClosingOrder: boolean) => z.object({
  quotationNumber: z.string().min(1, 'Número de cotización requerido'),
  approvalType: z.enum(['fleet', 'customer'], {
    required_error: 'Tipo de aprobación requerido',
  }),
  validityDays: z.number().min(1, 'Días de validez debe ser mayor a 0').max(365, 'Máximo 365 días'),
  customerNotes: z.string().optional(),
  paymentTerms: z.string().min(1, 'Términos de pago requeridos'),
  services: z.array(z.object({
    serviceId: z.string(),
    serviceName: z.string().min(1, 'Nombre del servicio requerido'),
    description: z.string().min(1, 'Descripción requerida'),
    estimatedCost: z.number().min(1, 'Costo debe ser mayor a 0'),
    laborCost: z.number().min(0, 'Costo de mano de obra debe ser mayor o igual a 0'),
    estimatedDuration: z.number().min(0.5, 'Duración debe ser al menos 0.5 horas'),
    evidence: isClosingOrder
      ? z.array(z.instanceof(File)).min(1, 'Debe subir al menos una evidencia para cerrar la orden')
      : z.array(z.instanceof(File)).optional(),
    parts: z.array(z.object({
      name: z.string().min(1, 'Nombre de la pieza requerido'),
      quantity: z.number().min(1, 'Cantidad debe ser mayor a 0'),
      unitCost: z.number().min(0, 'Costo debe ser mayor o igual a 0'),
      availability: z.enum(['available', 'unavailable']),
      partNumber: z.string().optional(),
      supplier: z.string().optional(),
      eta: z.string().optional(),
    })).optional(),
  })).min(1, 'Debe incluir al menos un servicio'),
});

type QuotationFormData = z.infer<ReturnType<typeof createQuotationSchema>>;

interface QuotationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  order: CorrectiveMaintenanceOrder;
  isClosingOrder?: boolean; // Nueva prop para indicar si estamos cerrando la orden
}

export default function QuotationModal({ isOpen, onClose, onSuccess, order, isClosingOrder = false }: QuotationModalProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [serviceEvidence, setServiceEvidence] = useState<Record<string, File[]>>({});
  const [evidenceErrors, setEvidenceErrors] = useState<Record<string, string>>({});
  const [inventoryFlowInfo, setInventoryFlowInfo] = useState<Record<string, any>>({});



  // Generate quotation number
  const generateQuotationNumber = () => {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const time = String(date.getTime()).slice(-6); // Last 6 digits of timestamp
    return `COT-${year}${month}${day}-${time}`;
  };

  // Prepare default services from order
  const defaultServices = order.services?.map(service => ({
    serviceId: service._id,
    serviceName: service.serviceName,
    description: service.description,
    estimatedCost: service.estimatedCost,
    laborCost: service.laborCost,
    estimatedDuration: service.estimatedDuration,
    evidence: [] as File[],
    parts: service.parts?.map(part => ({
      name: part.name,
      quantity: part.quantity,
      unitCost: part.unitCost,
      availability: part.availability === 'available' ? 'available' as 'available' | 'unavailable' : 'unavailable' as 'available' | 'unavailable',
      partNumber: part.partNumber || '',
      supplier: part.supplier || '',
      eta: part.eta || '',
    })) || [],
  })) || [];

  const form = useForm<QuotationFormData>({
    resolver: zodResolver(createQuotationSchema(isClosingOrder)),
    defaultValues: {
      quotationNumber: generateQuotationNumber(),
      approvalType: order.approvalType || 'customer',
      validityDays: 15,
      customerNotes: '',
      paymentTerms: 'Pago contra entrega',
      services: defaultServices,
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'services',
  });

  // Reset form when modal opens or order changes
  useEffect(() => {
    if (isOpen && order.services) {
      const newDefaultServices = order.services.map(service => ({
        serviceId: service._id,
        serviceName: service.serviceName,
        description: service.description,
        estimatedCost: service.estimatedCost,
        laborCost: service.laborCost,
        estimatedDuration: service.estimatedDuration,
        evidence: [] as File[],
        parts: service.parts?.map(part => ({
          name: part.name,
          quantity: part.quantity,
          unitCost: part.unitCost,
          availability: part.availability === 'available' ? 'available' as 'available' | 'unavailable' : 'unavailable' as 'available' | 'unavailable',
          partNumber: part.partNumber || '',
          supplier: part.supplier || '',
          eta: part.eta || '',
        })) || [],
      }));

      form.reset({
        quotationNumber: generateQuotationNumber(),
        approvalType: order.approvalType || 'customer',
        validityDays: 15,
        customerNotes: '',
        paymentTerms: 'Pago contra entrega',
        services: newDefaultServices,
      });

      // Clear evidence state
      setServiceEvidence({});
      setEvidenceErrors({});
    }
  }, [isOpen, order._id, order.services, form]);

  // Initialize evidence errors for services without evidence (only when closing order)
  React.useEffect(() => {
    if (isClosingOrder) {
      const newErrors: Record<string, string> = {};
      fields.forEach(field => {
        const evidence = serviceEvidence[field.serviceId] || [];
        if (evidence.length === 0) {
          newErrors[field.serviceId] = 'Debe subir al menos un archivo de evidencia para cerrar la orden';
        }
      });
      setEvidenceErrors(newErrors);
    } else {
      // Clear evidence errors when not closing order
      setEvidenceErrors({});
    }
  }, [fields, serviceEvidence, isClosingOrder]);

  // Calculate totals from form services (editable)
  const watchedServices = form.watch('services');

  const totalCost = watchedServices.reduce((sum, service) => sum + (service.estimatedCost || 0), 0);
  const totalLaborCost = watchedServices.reduce((sum, service) => sum + (service.laborCost || 0), 0);
  const totalPartsCost = totalCost - totalLaborCost;
  const totalDuration = watchedServices.reduce((sum, service) => sum + (service.estimatedDuration || 0), 0);

  const addService = () => {
    append({
      serviceId: `new-service-${Date.now()}`,
      serviceName: '',
      description: '',
      estimatedCost: 0,
      laborCost: 0,
      estimatedDuration: 1,
      evidence: [] as File[],
      parts: [],
    });
  };

  const removeService = (index: number) => {
    if (fields.length > 1) {
      const serviceId = fields[index].serviceId;
      // Remove evidence for this service
      setServiceEvidence(prev => {
        const newEvidence = { ...prev };
        delete newEvidence[serviceId];
        return newEvidence;
      });
      remove(index);
    }
  };

  // Functions for managing parts within services
  const addPartToService = (serviceIndex: number) => {
    const currentParts = form.getValues(`services.${serviceIndex}.parts`) || [];
    form.setValue(`services.${serviceIndex}.parts`, [
      ...currentParts,
      {
        name: '',
        quantity: 1,
        unitCost: 0,
        availability: 'unavailable' as 'available' | 'unavailable',
        partNumber: '',
        supplier: '',
        eta: '',
      }
    ]);
  };

  const removePartFromService = (serviceIndex: number, partIndex: number) => {
    const currentParts = form.getValues(`services.${serviceIndex}.parts`) || [];
    if (currentParts.length > 1) {
      const newParts = currentParts.filter((_, index) => index !== partIndex);
      form.setValue(`services.${serviceIndex}.parts`, newParts);
    }
  };

  const updatePartAvailability = (serviceIndex: number, partIndex: number, availability: 'available' | 'unavailable') => {
    form.setValue(`services.${serviceIndex}.parts.${partIndex}.availability`, availability);

    // Update inventory flow information
    const serviceId = fields[serviceIndex].serviceId;
    const serviceParts = form.getValues(`services.${serviceIndex}.parts`) || [];

    // Update the specific part's availability and ensure totalCost is calculated
    const updatedParts = serviceParts.map((part, index) =>
      index === partIndex ? {
        ...part,
        availability,
        totalCost: part.quantity * part.unitCost
      } : {
        ...part,
        totalCost: part.quantity * part.unitCost
      }
    );

    // Calculate inventory flow decision
    const flowDecision = shouldEnterInventoryFlow(updatedParts);
    const nextSteps = getInventoryNextSteps(updatedParts);
    const notifications = generateInventoryNotifications(updatedParts, fields[serviceIndex].serviceName);

    setInventoryFlowInfo(prev => ({
      ...prev,
      [serviceId]: {
        decision: flowDecision,
        nextSteps,
        notifications,
        updatedAt: new Date().toISOString()
      }
    }));
  };

  const handleFileChange = (serviceId: string, files: FileList | null) => {
    if (files) {
      const fileArray = Array.from(files);
      const newFiles = [...(serviceEvidence[serviceId] || []), ...fileArray];

      setServiceEvidence(prev => ({
        ...prev,
        [serviceId]: newFiles
      }));

      // Clear evidence error for this service
      setEvidenceErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[serviceId];
        return newErrors;
      });

      // Update form data
      const serviceIndex = fields.findIndex(field => field.serviceId === serviceId);
      if (serviceIndex !== -1) {
        form.setValue(`services.${serviceIndex}.evidence`, newFiles);
      }
    }
  };

  const removeFile = (serviceId: string, fileIndex: number) => {
    const newFiles = [...(serviceEvidence[serviceId] || [])];
    newFiles.splice(fileIndex, 1);

    setServiceEvidence(prev => ({
      ...prev,
      [serviceId]: newFiles
    }));

    // Set error if no files left
    if (newFiles.length === 0) {
      setEvidenceErrors(prev => ({
        ...prev,
        [serviceId]: 'Debe subir al menos un archivo de evidencia'
      }));
    }

    // Update form data
    const serviceIndex = fields.findIndex(field => field.serviceId === serviceId);
    if (serviceIndex !== -1) {
      form.setValue(`services.${serviceIndex}.evidence`, newFiles);
    }
  };

  const onSubmit = async (data: QuotationFormData) => {

    // Validate that all services have evidence (only when closing order)
    if (isClosingOrder) {
      const newEvidenceErrors: Record<string, string> = {};
      data.services.forEach(service => {
        const evidence = serviceEvidence[service.serviceId] || [];
        if (evidence.length === 0) {
          newEvidenceErrors[service.serviceId] = 'Debe subir al menos un archivo de evidencia para cerrar la orden';
        }
      });

      if (Object.keys(newEvidenceErrors).length > 0) {
        console.error('❌ Services without evidence (closing order):', newEvidenceErrors);
        setEvidenceErrors(newEvidenceErrors);
        toast({
          title: 'Error',
          description: 'Todos los servicios deben tener al menos un archivo de evidencia para cerrar la orden',
          variant: 'destructive',
        });
        return;
      }
    }





    setIsSubmitting(true);
    try {
      // Create FormData for file upload
      const formData = new FormData();

      // Add basic quotation data
      formData.append('quotationNumber', data.quotationNumber);
      formData.append('approvalType', data.approvalType);
      formData.append('validityDays', data.validityDays.toString());
      formData.append('paymentTerms', data.paymentTerms);

      if (data.customerNotes) {
        formData.append('customerNotes', data.customerNotes);
      }

      // Add services data (without evidence)
      const servicesWithoutEvidence = data.services.map(service => ({
        serviceId: service.serviceId,
        serviceName: service.serviceName,
        description: service.description,
        estimatedCost: service.estimatedCost,
        laborCost: service.laborCost,
        estimatedDuration: service.estimatedDuration,
        parts: service.parts || [],
      }));
      formData.append('services', JSON.stringify(servicesWithoutEvidence));

      // Add evidence files
      data.services.forEach((service) => {
        const evidence = serviceEvidence[service.serviceId] || [];
        evidence.forEach((file) => {
          formData.append(`evidence_${service.serviceId}`, file);
        });
      });

      // Use fetch directly instead of Server Action
      const response = await fetch(`/api/corrective-maintenance/orders/${order._id}/quotation`, {
        method: 'POST',
        body: formData,
        // Don't set Content-Type header, let browser set it for FormData
      });

      const responseData = await response.json();

      if (response.ok && responseData.success !== false) {
        toast({
          title: 'Éxito',
          description: 'Cotización creada exitosamente',
        });
        form.reset();
        onSuccess();
      } else {
        console.error('❌ QuotationModal - API returned error:', responseData.message);
        toast({
          title: 'Error',
          description: responseData.message || 'Error al crear la cotización',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('❌ QuotationModal - Error creating quotation:', error);
      toast({
        title: 'Error',
        description: 'Error inesperado al crear la cotización',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    form.reset();
    setServiceEvidence({});
    setEvidenceErrors({});
    onClose();
  };

  if (!order.services || order.services.length === 0) {

    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>No hay servicios para cotizar</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-gray-600">
              Primero debe completar el diagnóstico para poder crear una cotización.
            </p>
            <div className="mt-4 p-3 bg-gray-100 rounded text-sm space-y-2">
              <p><strong>Estado de la orden:</strong> {order.status}</p>
              <p><strong>Diagnóstico:</strong> {order.diagnosis ? 'Completado' : 'Pendiente'}</p>
              <p><strong>Servicios encontrados:</strong> {order.services?.length || 0}</p>
              {order.diagnosis && (
                <div className="mt-2 p-2 bg-blue-50 rounded">
                  <p className="text-blue-800 font-medium">Diagnóstico completado:</p>
                  <p className="text-blue-700 text-xs">{order.diagnosis.diagnosisNotes}</p>
                  <p className="text-blue-600 text-xs mt-1">
                    Completado el: {new Date(order.diagnosis.completedAt).toLocaleString()}
                  </p>
                </div>
              )}
              {order.status === 'diagnosed' && (!order.services || order.services.length === 0) && (
                <div className="mt-2 p-2 bg-yellow-50 rounded">
                  <p className="text-yellow-800 font-medium">⚠️ Problema detectado:</p>
                  <p className="text-yellow-700 text-xs">
                    El diagnóstico está completado pero no se encontraron servicios.
                    Esto puede indicar un problema con la sincronización de datos.
                  </p>
                  <p className="text-yellow-600 text-xs mt-1">
                    Intente refrescar la página o contacte al administrador.
                  </p>
                </div>
              )}
            </div>
          </div>
          <DialogFooter className="gap-2">
            <Button variant="outline" onClick={() => window.location.reload()}>
              Refrescar Página
            </Button>
            <Button onClick={handleClose}>Cerrar</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isClosingOrder ? 'Cerrar Orden - Evidencia Final' : 'Crear Cotización'}
          </DialogTitle>
          <DialogDescription>
            {isClosingOrder
              ? 'Suba la evidencia final de los servicios completados para cerrar la orden.'
              : 'Complete la información de la cotización para los servicios diagnosticados.'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Quotation Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="quotationNumber">Número de Cotización *</Label>
              <Input
                {...form.register('quotationNumber')}
                placeholder="COT-20241215-123456"
                className="font-mono"
              />
              {form.formState.errors.quotationNumber && (
                <p className="text-sm text-red-500">{form.formState.errors.quotationNumber.message}</p>
              )}
            </div>

            {/* Approval Type - Hidden, defaults to customer */}
            <input
              type="hidden"
              {...form.register('approvalType')}
              value="customer"
            />

            <div className="space-y-2">
              <Label htmlFor="validityDays">Días de Validez *</Label>
              <Input
                type="number"
                {...form.register('validityDays', { valueAsNumber: true })}
                min="1"
                max="365"
                placeholder="15"
              />
              {form.formState.errors.validityDays && (
                <p className="text-sm text-red-500">{form.formState.errors.validityDays.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4">
            {/* Payment Terms - Hidden, always "Pago contra entrega" */}
            <input
              type="hidden"
              {...form.register('paymentTerms')}
              value="Pago contra entrega"
            />

            <div className="space-y-2">
              <Label htmlFor="customerNotes">Notas para el Cliente</Label>
              <Input
                {...form.register('customerNotes')}
                placeholder="Información adicional para el cliente (opcional)"
              />
              {form.formState.errors.customerNotes && (
                <p className="text-sm text-red-500">{form.formState.errors.customerNotes.message}</p>
              )}
            </div>
          </div>

          {/* Services List (Editable) */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium">Servicios Incluidos en la Cotización</h3>
                <p className="text-sm text-gray-600">
                  Puedes ajustar los costos y duración de cada servicio antes de crear la cotización.
                </p>
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addService}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Agregar Servicio
              </Button>
            </div>

            {fields.map((field, index) => (
              <Card key={field.id}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-base font-medium">Servicio {index + 1}</h4>
                    {fields.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeService(index)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                  <div className="space-y-2">
                    <div>
                      <Label htmlFor={`services.${index}.serviceName`}>Nombre del Servicio *</Label>
                      <Input
                        {...form.register(`services.${index}.serviceName`)}
                        placeholder="Ej: Cambio de pastillas de freno"
                      />
                      {form.formState.errors.services?.[index]?.serviceName && (
                        <p className="text-sm text-red-500">
                          {form.formState.errors.services[index]?.serviceName?.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor={`services.${index}.description`}>Descripción *</Label>
                      <Input
                        {...form.register(`services.${index}.description`)}
                        placeholder="Descripción detallada del servicio"
                      />
                      {form.formState.errors.services?.[index]?.description && (
                        <p className="text-sm text-red-500">
                          {form.formState.errors.services[index]?.description?.message}
                        </p>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`services.${index}.estimatedCost`}>Costo Total (MXN) *</Label>
                      <Input
                        type="number"
                        step="0.01"
                        {...form.register(`services.${index}.estimatedCost`, { valueAsNumber: true })}
                        placeholder="0.00"
                      />
                      {form.formState.errors.services?.[index]?.estimatedCost && (
                        <p className="text-sm text-red-500">
                          {form.formState.errors.services[index]?.estimatedCost?.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`services.${index}.laborCost`}>Mano de Obra (MXN) *</Label>
                      <Input
                        type="number"
                        step="0.01"
                        {...form.register(`services.${index}.laborCost`, { valueAsNumber: true })}
                        placeholder="0.00"
                      />
                      {form.formState.errors.services?.[index]?.laborCost && (
                        <p className="text-sm text-red-500">
                          {form.formState.errors.services[index]?.laborCost?.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`services.${index}.estimatedDuration`}>Duración (horas) *</Label>
                      <Input
                        type="number"
                        step="0.5"
                        {...form.register(`services.${index}.estimatedDuration`, { valueAsNumber: true })}
                        placeholder="1.0"
                      />
                      {form.formState.errors.services?.[index]?.estimatedDuration && (
                        <p className="text-sm text-red-500">
                          {form.formState.errors.services[index]?.estimatedDuration?.message}
                        </p>
                      )}
                    </div>
                  </div>



                  {/* Parts Management Section */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Package className="h-4 w-4" />
                        <Label className="text-base font-medium">Refacciones Necesarias</Label>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => addPartToService(index)}
                        className="flex items-center gap-2"
                      >
                        <Plus className="h-4 w-4" />
                        Agregar Refacción
                      </Button>
                    </div>

                    {/* Parts List */}
                    {watchedServices[index]?.parts?.map((part, partIndex) => (
                      <Card key={partIndex} className="border-l-4 border-l-blue-200">
                        <CardContent className="pt-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor={`services.${index}.parts.${partIndex}.name`}>Nombre de la Refacción *</Label>
                              <Input
                                {...form.register(`services.${index}.parts.${partIndex}.name`)}
                                placeholder="Ej: Pastillas de freno delanteras"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`services.${index}.parts.${partIndex}.partNumber`}>Número de Parte</Label>
                              <Input
                                {...form.register(`services.${index}.parts.${partIndex}.partNumber`)}
                                placeholder="Ej: BP-001-2024"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`services.${index}.parts.${partIndex}.quantity`}>Cantidad *</Label>
                              <Input
                                type="number"
                                min="1"
                                {...form.register(`services.${index}.parts.${partIndex}.quantity`, { valueAsNumber: true })}
                                placeholder="1"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`services.${index}.parts.${partIndex}.unitCost`}>Costo Unitario (MXN) *</Label>
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                {...form.register(`services.${index}.parts.${partIndex}.unitCost`, { valueAsNumber: true })}
                                placeholder="0.00"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`services.${index}.parts.${partIndex}.supplier`}>Proveedor</Label>
                              <Input
                                {...form.register(`services.${index}.parts.${partIndex}.supplier`)}
                                placeholder="Ej: AutoPartes México"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`services.${index}.parts.${partIndex}.availability`}>¿Se tiene la pieza? *</Label>
                              <Select
                                value={watchedServices[index]?.parts?.[partIndex]?.availability || 'unavailable'}
                                onValueChange={(value: 'available' | 'unavailable') =>
                                  updatePartAvailability(index, partIndex, value)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecciona disponibilidad" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="available">
                                    <div className="flex items-center gap-2">
                                      <Badge className="bg-green-100 text-green-800">✓</Badge>
                                      Sí
                                    </div>
                                  </SelectItem>
                                  <SelectItem value="unavailable">
                                    <div className="flex items-center gap-2">
                                      <Badge className="bg-red-100 text-red-800">✗</Badge>
                                      No
                                    </div>
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>

                          {/* Show inventory flow warning if part is unavailable */}
                          {watchedServices[index]?.parts?.[partIndex]?.availability === 'unavailable' && (
                            <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                              <div className="flex items-center gap-2">
                                <AlertTriangle className="h-4 w-4 text-orange-600" />
                                <p className="text-sm text-orange-800 font-medium">
                                  Esta refacción entrará al flujo de inventarios
                                </p>
                              </div>
                              <p className="text-xs text-orange-700 mt-1">
                                Se iniciará el proceso de pedido y seguimiento de esta refacción automáticamente.
                              </p>
                              <div className="mt-2 space-y-2">
                                <Label htmlFor={`services.${index}.parts.${partIndex}.eta`}>Fecha estimada de llegada</Label>
                                <Input
                                  type="date"
                                  {...form.register(`services.${index}.parts.${partIndex}.eta`)}
                                />
                              </div>
                            </div>
                          )}

                          {/* Part summary and remove button */}
                          <div className="mt-4 flex items-center justify-between">
                            <div className="text-sm text-gray-600">
                              <p>
                                <strong>Costo total:</strong> {formatCurrency((part.quantity || 0) * (part.unitCost || 0))}
                              </p>
                            </div>
                            {(watchedServices[index]?.parts?.length || 0) > 1 && (
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removePartFromService(index, partIndex)}
                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    )) || (
                      <div className="text-center py-4 text-gray-500">
                        <Package className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                        <p className="text-sm">No hay refacciones agregadas</p>
                        <p className="text-xs">Haz clic en "Agregar Refacción" para comenzar</p>
                      </div>
                    )}
                  </div>

                  {/* Inventory Flow Summary */}
                  {inventoryFlowInfo[field.serviceId] && (
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Package className="h-4 w-4" />
                        <Label className="text-base font-medium">Resumen del Flujo de Inventarios</Label>
                      </div>

                      <Card className={`border-l-4 ${
                        inventoryFlowInfo[field.serviceId].decision.shouldEnterInventoryFlow
                          ? 'border-l-orange-400 bg-orange-50'
                          : 'border-l-green-400 bg-green-50'
                      }`}>
                        <CardContent className="pt-4">
                          <div className="space-y-3">
                            <div className="flex items-center gap-2">
                              {inventoryFlowInfo[field.serviceId].decision.shouldEnterInventoryFlow ? (
                                <AlertTriangle className="h-5 w-5 text-orange-600" />
                              ) : (
                                <CheckCircle className="h-5 w-5 text-green-600" />
                              )}
                              <p className={`font-medium ${
                                inventoryFlowInfo[field.serviceId].decision.shouldEnterInventoryFlow
                                  ? 'text-orange-800'
                                  : 'text-green-800'
                              }`}>
                                {inventoryFlowInfo[field.serviceId].decision.shouldEnterInventoryFlow
                                  ? 'Requiere gestión de inventarios'
                                  : 'Puede proceder sin inventarios'
                                }
                              </p>
                            </div>

                            <p className="text-sm text-gray-700">
                              {inventoryFlowInfo[field.serviceId].decision.reason}
                            </p>

                            {inventoryFlowInfo[field.serviceId].decision.estimatedDelay && (
                              <div className="flex items-center gap-2 text-sm">
                                <Clock className="h-4 w-4 text-gray-500" />
                                <span className="text-gray-600">
                                  Retraso estimado: {inventoryFlowInfo[field.serviceId].decision.estimatedDelay} horas
                                </span>
                              </div>
                            )}

                            {inventoryFlowInfo[field.serviceId].nextSteps && (
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs">
                                <div>
                                  <p className="font-medium text-green-700">Disponibles:</p>
                                  <p>{inventoryFlowInfo[field.serviceId].nextSteps.availableParts.length}</p>
                                </div>
                                <div>
                                  <p className="font-medium text-orange-700">Por ordenar:</p>
                                  <p>{inventoryFlowInfo[field.serviceId].nextSteps.partsToOrder.length}</p>
                                </div>
                                <div>
                                  <p className="font-medium text-yellow-700">Por verificar:</p>
                                  <p>{inventoryFlowInfo[field.serviceId].nextSteps.partsToVerify.length}</p>
                                </div>
                              </div>
                            )}

                            {inventoryFlowInfo[field.serviceId].nextSteps?.estimatedStartTime && (
                              <div className="text-xs text-gray-600 bg-white p-2 rounded">
                                <p>
                                  <strong>Inicio estimado del servicio:</strong> {' '}
                                  {new Date(inventoryFlowInfo[field.serviceId].nextSteps.estimatedStartTime).toLocaleString()}
                                </p>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  )}

                  {/* Evidence Upload Section - Only show when closing order */}
                  {isClosingOrder && (
                    <div className="space-y-3">
                      <Label>Evidencia del Servicio *</Label>
                    <div className={`border-2 border-dashed rounded-lg p-4 ${
                      evidenceErrors[field.serviceId]
                        ? 'border-red-300 bg-red-50'
                        : 'border-gray-300'
                    }`}>
                      <div className="text-center">
                        <Upload className="mx-auto h-8 w-8 text-gray-400" />
                        <div className="mt-2">
                          <label htmlFor={`evidence-upload-${field.serviceId}`} className="cursor-pointer">
                            <span className="text-sm font-medium text-gray-900">
                              Subir fotos o videos *
                            </span>
                            <span className="block text-xs text-gray-500 mt-1">
                              PNG, JPG, MP4 hasta 10MB cada uno (Obligatorio)
                            </span>
                          </label>
                          <input
                            id={`evidence-upload-${field.serviceId}`}
                            type="file"
                            multiple
                            accept="image/*,video/*"
                            onChange={(e) => handleFileChange(field.serviceId, e.target.files)}
                            className="hidden"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Evidence validation error */}
                    {evidenceErrors[field.serviceId] && (
                      <p className="text-sm text-red-500">
                        {evidenceErrors[field.serviceId]}
                      </p>
                    )}

                    {/* Display uploaded files */}
                    {serviceEvidence[field.serviceId] && serviceEvidence[field.serviceId].length > 0 ? (
                      <div className="space-y-2">
                        <Label className="text-sm text-green-700">✓ Archivos subidos:</Label>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                          {serviceEvidence[field.serviceId].map((file, fileIndex) => (
                            <div key={fileIndex} className="relative border rounded-lg p-2 bg-gray-50">
                              <div className="flex items-center space-x-2">
                                <Image className="h-4 w-4 text-gray-500" />
                                <span className="text-xs truncate flex-1">{file.name}</span>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeFile(field.serviceId, fileIndex)}
                                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </div>
                              <div className="text-xs text-gray-500 mt-1">
                                {(file.size / 1024 / 1024).toFixed(2)} MB
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : isClosingOrder ? (
                      <div className="text-center py-2">
                        <p className="text-sm text-red-600">
                          ⚠️ Debe subir al menos un archivo de evidencia para cerrar la orden
                        </p>
                      </div>
                    ) : null}
                    </div>
                  )}

                  {/* Hidden field for serviceId */}
                  <input
                    type="hidden"
                    {...form.register(`services.${index}.serviceId`)}
                  />
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                Resumen de la Cotización
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Mano de Obra:</p>
                  <p className="font-medium">{formatCurrency(totalLaborCost)}</p>
                </div>
                <div>
                  <p className="text-gray-500">Refacciones:</p>
                  <p className="font-medium">{formatCurrency(totalPartsCost)}</p>
                </div>
                <div>
                  <p className="text-gray-500">Duración Total:</p>
                  <p className="font-medium">{formatDuration(totalDuration)}</p>
                </div>
                <div>
                  <p className="text-gray-500">Total:</p>
                  <p className="font-bold text-lg">{formatCurrency(totalCost)}</p>
                </div>
              </div>

              <Separator />

              <div className="text-xs text-gray-500">
                <p>• Los precios incluyen mano de obra y refacciones</p>
                <p>• La cotización es válida hasta la fecha especificada</p>
                <p>• Los tiempos son estimados y pueden variar según disponibilidad</p>
              </div>
            </CardContent>
          </Card>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancelar
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isClosingOrder ? 'Cerrar Orden' : 'Crear Cotización'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
