'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  BarChart3,
  Bell,
  Calendar,
  Clock,
  DollarSign,
  Plus,
  TrendingUp,
  Wrench,
  AlertTriangle,
  CheckCircle,
  Timer,
  Activity,
  TestTube
} from 'lucide-react';
import { CorrectiveMaintenanceOrder } from '../types';
import MetricsCard from './MetricsCard';
import NotificationsPanel, { generateMockNotifications } from './NotificationsPanel';
import CreateOrderModal from './CreateOrderModal';
import ServiceTimeTracker from './ServiceTimeTracker';
import RealTimeServiceDashboard from './RealTimeServiceDashboard';
import TestingPanel from './TestingPanel';

interface DashboardProps {
  orders: CorrectiveMaintenanceOrder[];
  period: 'today' | 'week' | 'month' | 'year';
  onRefresh?: () => void;
}

export default function CorrectiveMaintenanceDashboard({ orders, period, onRefresh }: DashboardProps) {
  const router = useRouter();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [notifications, setNotifications] = useState(generateMockNotifications());

  // Calculate metrics from orders
  const metricsData = {
    totalOrders: orders.length,
    completedOrders: orders.filter(order => order.status === 'completed').length,
    inProgressOrders: orders.filter(order => order.status === 'in-progress').length,
    pendingOrders: orders.filter(order => ['pending', 'diagnosed', 'quoted'].includes(order.status)).length,
    averageCompletionTime: 18.5, // This would come from backend calculation
    slaCompliance: 92.3,
    totalRevenue: orders.reduce((sum, order) => sum + (order.totalEstimatedCost || 0), 0),
    averageOrderValue: orders.length > 0
      ? orders.reduce((sum, order) => sum + (order.totalEstimatedCost || 0), 0) / orders.length
      : 0,
    approvalRate: 87.5,
    customerSatisfaction: 8.2,
  };

  // Quick stats for cards
  const quickStats = [
    {
      title: 'Órdenes Activas',
      value: orders.filter(o => ['pending', 'diagnosed', 'quoted', 'approved', 'in-progress'].includes(o.status)).length,
      icon: <Wrench className="h-5 w-5" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      change: '+12%',
      changeType: 'positive' as const,
    },
    {
      title: 'SLA en Riesgo',
      value: 3, // This would be calculated based on actual SLA data
      icon: <AlertTriangle className="h-5 w-5" />,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      change: '-5%',
      changeType: 'negative' as const,
    },
    {
      title: 'Completadas Hoy',
      value: orders.filter(o => o.status === 'completed' && isToday(o.updatedAt)).length,
      icon: <CheckCircle className="h-5 w-5" />,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      change: '+8%',
      changeType: 'positive' as const,
    },
    {
      title: 'Ingresos del Mes',
      value: `$${(metricsData.totalRevenue / 1000).toFixed(0)}K`,
      icon: <DollarSign className="h-5 w-5" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      change: '+15%',
      changeType: 'positive' as const,
    },
  ];

  const handleOrderCreated = () => {
    setIsCreateModalOpen(false);
    if (onRefresh) {
      onRefresh();
    } else {
      router.refresh();
    }
  };

  const handleRefreshData = () => {
    if (onRefresh) {
      onRefresh();
    } else {
      router.refresh();
    }
  };

  const handleMarkAsRead = (notificationId: string) => {
    setNotifications(prev =>
      prev.map(n => n.id === notificationId ? { ...n, isRead: true } : n)
    );
  };

  const handleDismissNotification = (notificationId: string) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
  };

  const handleViewOrder = (orderId: string) => {
    router.push(`/dashboard/corrective-maintenance/${orderId}`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Dashboard de Mantenimiento Correctivo</h1>
          <p className="text-gray-600">
            Monitorea el rendimiento y gestiona las órdenes de mantenimiento
          </p>
        </div>
        <Button onClick={() => setIsCreateModalOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Nueva Orden
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {quickStats.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                  <div className="flex items-center mt-1">
                    <span className={`text-sm ${
                      stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.changeType === 'positive' ? '↗' : '↘'} {stat.change}
                    </span>
                    <span className="text-sm text-gray-500 ml-1">vs mes anterior</span>
                  </div>
                </div>
                <div className={`p-3 rounded-full ${stat.bgColor}`}>
                  <div className={stat.color}>
                    {stat.icon}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Metrics and Charts */}
        <div className="lg:col-span-2">
          <Tabs defaultValue="real-time" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="real-time" className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                Tiempo Real
              </TabsTrigger>
              <TabsTrigger value="metrics" className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Métricas
              </TabsTrigger>
              <TabsTrigger value="time-tracking" className="flex items-center gap-2">
                <Timer className="h-4 w-4" />
                Tiempo
              </TabsTrigger>
              <TabsTrigger value="testing" className="flex items-center gap-2">
                <TestTube className="h-4 w-4" />
                Testing
              </TabsTrigger>
              <TabsTrigger value="schedule" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Programación
              </TabsTrigger>
            </TabsList>

            <TabsContent value="real-time" className="mt-6">
              <RealTimeServiceDashboard
                orders={orders}
                onRefresh={handleRefreshData}
              />
            </TabsContent>

            <TabsContent value="metrics" className="mt-6">
              <MetricsCard data={metricsData} period={period} />
            </TabsContent>

            <TabsContent value="time-tracking" className="mt-6">
              {orders.length > 0 ? (
                <div className="space-y-4">
                  {orders
                    .filter(order => order.services && order.services.length > 0)
                    .slice(0, 3) // Show only first 3 orders with services
                    .map(order => (
                      <div key={order._id}>
                        <div className="mb-4">
                          <h3 className="text-lg font-medium">
                            Orden #{order._id.slice(-8).toUpperCase()}
                          </h3>
                          <p className="text-sm text-gray-600">
                            {order.vehicle?.brand} {order.vehicle?.model} - {order.vehicle?.carPlates?.plates}
                          </p>
                        </div>
                        <ServiceTimeTracker
                          services={order.services || []}
                          orderId={order._id}
                        />
                      </div>
                    ))}
                  {orders.filter(order => order.services && order.services.length > 0).length === 0 && (
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center py-8 text-gray-500">
                          <Timer className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                          <p>No hay servicios en progreso</p>
                          <p className="text-sm">
                            Los servicios aparecerán aquí cuando se inicien
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              ) : (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center py-8 text-gray-500">
                      <Timer className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p>No hay órdenes para rastrear</p>
                      <p className="text-sm">
                        Crea una nueva orden para comenzar el seguimiento de tiempo
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="testing" className="mt-6">
              <TestingPanel
                orders={orders}
                onRefresh={handleRefreshData}
              />
            </TabsContent>

            <TabsContent value="schedule" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Programación de Servicios</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 text-gray-500">
                    <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>Vista de calendario próximamente</p>
                    <p className="text-sm">
                      Aquí podrás ver la programación de servicios y citas
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Notifications Panel */}
        <div className="lg:col-span-1">
          <NotificationsPanel
            notifications={notifications}
            onMarkAsRead={handleMarkAsRead}
            onDismiss={handleDismissNotification}
            onViewOrder={handleViewOrder}
          />
        </div>
      </div>

      {/* Recent Orders Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Órdenes Recientes</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/dashboard/corrective-maintenance')}
            >
              Ver todas
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {orders.slice(0, 5).length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Wrench className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No hay órdenes recientes</p>
              <p className="text-sm">Las nuevas órdenes aparecerán aquí</p>
            </div>
          ) : (
            <div className="space-y-3">
              {orders.slice(0, 5).map((order) => (
                <div
                  key={order._id}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                  onClick={() => handleViewOrder(order._id)}
                >
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                    <div>
                      <p className="font-medium">#{order._id.slice(-8).toUpperCase()}</p>
                      <p className="text-sm text-gray-600">
                        {order.vehicle?.brand} {order.vehicle?.model} - {order.vehicle?.carPlates?.plates}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant="secondary" className="mb-1">
                      {order.status}
                    </Badge>
                    <p className="text-sm text-gray-500">
                      {new Date(order.createdAt).toLocaleDateString('es-ES')}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Order Modal */}
      <CreateOrderModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleOrderCreated}
      />
    </div>
  );
}

// Helper function to check if a date is today
function isToday(dateString: string): boolean {
  const today = new Date();
  const date = new Date(dateString);
  return date.toDateString() === today.toDateString();
}
