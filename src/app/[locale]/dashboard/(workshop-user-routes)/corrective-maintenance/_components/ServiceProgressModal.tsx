'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, Upload, Clock, CheckCircle, AlertTriangle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { CorrectiveService, SERVICE_STATUS_LABELS, getStatusColor, formatCurrency, formatDuration } from '../types';
import { updateServiceProgress } from '../_actions/updateServiceProgress';

const progressSchema = z.object({
  status: z.enum(['not-started', 'in-progress', 'completed', 'waiting-for-parts', 'cancelled']),
  notes: z.string().optional(),
  evidence: z.array(z.instanceof(File)).optional(),
});

type ProgressFormData = z.infer<typeof progressSchema>;

interface ServiceProgressModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  serviceId: string;
  service?: CorrectiveService;
}

export default function ServiceProgressModal({
  isOpen,
  onClose,
  onSuccess,
  serviceId,
  service
}: ServiceProgressModalProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

  const form = useForm<ProgressFormData>({
    resolver: zodResolver(progressSchema),
    defaultValues: {
      status: service?.status || 'not-started',
      notes: '',
      evidence: [],
    },
  });

  const watchedStatus = form.watch('status');

  const onSubmit = async (data: ProgressFormData) => {
    setIsSubmitting(true);
    try {
      const formData = {
        ...data,
        evidence: selectedFiles,
      };

      const response = await updateServiceProgress(serviceId, formData);

      if (response.success) {
        toast({
          title: 'Éxito',
          description: 'Progreso del servicio actualizado exitosamente',
        });
        form.reset();
        setSelectedFiles([]);
        onSuccess();
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Error al actualizar el progreso del servicio',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating service progress:', error);
      toast({
        title: 'Error',
        description: 'Error inesperado al actualizar el progreso del servicio',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setSelectedFiles(prev => [...prev, ...files]);
  };

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleClose = () => {
    form.reset();
    setSelectedFiles([]);
    onClose();
  };

  const getProgressPercentage = (status: string) => {
    switch (status) {
      case 'not-started': return 0;
      case 'in-progress': return 50;
      case 'waiting-for-parts': return 25;
      case 'completed': return 100;
      case 'cancelled': return 0;
      default: return 0;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'not-started': return <Clock className="h-4 w-4" />;
      case 'in-progress': return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'waiting-for-parts': return <AlertTriangle className="h-4 w-4" />;
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'cancelled': return <AlertTriangle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  if (!service) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Servicio no encontrado</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-gray-600">
              No se pudo cargar la información del servicio.
            </p>
          </div>
          <DialogFooter>
            <Button onClick={handleClose}>Cerrar</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Actualizar Progreso del Servicio</DialogTitle>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Service Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{service.serviceName}</span>
                <Badge className={getStatusColor(service.status, 'service')} variant="secondary">
                  {SERVICE_STATUS_LABELS[service.status as keyof typeof SERVICE_STATUS_LABELS]}
                </Badge>
              </CardTitle>
              <p className="text-sm text-gray-600">{service.description}</p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Costo Estimado:</p>
                  <p className="font-medium">{formatCurrency(service.estimatedCost)}</p>
                </div>
                <div>
                  <p className="text-gray-500">Mano de Obra:</p>
                  <p className="font-medium">{formatCurrency(service.laborCost)}</p>
                </div>
                <div>
                  <p className="text-gray-500">Duración Estimada:</p>
                  <p className="font-medium">{formatDuration(service.estimatedDuration)}</p>
                </div>
                <div>
                  <p className="text-gray-500">Refacciones:</p>
                  <p className="font-medium">{service.parts.length}</p>
                </div>
              </div>

              {/* Current Progress */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Progreso Actual</Label>
                  <span className="text-sm text-gray-500">
                    {getProgressPercentage(service.status)}%
                  </span>
                </div>
                <Progress value={getProgressPercentage(service.status)} className="h-2" />
              </div>
            </CardContent>
          </Card>

          {/* Status Update */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Actualizar Estado</h3>

            <div className="space-y-2">
              <Label htmlFor="status">Nuevo Estado *</Label>
              <Select
                value={watchedStatus}
                onValueChange={(value: any) => form.setValue('status', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona un estado" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="not-started">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      No Iniciado
                    </div>
                  </SelectItem>
                  <SelectItem value="in-progress">
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4" />
                      En Progreso
                    </div>
                  </SelectItem>
                  <SelectItem value="waiting-for-parts">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4" />
                      Esperando Refacciones
                    </div>
                  </SelectItem>
                  <SelectItem value="completed">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4" />
                      Completado
                    </div>
                  </SelectItem>
                  <SelectItem value="cancelled">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4" />
                      Cancelado
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              {form.formState.errors.status && (
                <p className="text-sm text-red-500">{form.formState.errors.status.message}</p>
              )}
            </div>

            {/* Progress Preview */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-3">
                  <div className={getStatusColor(watchedStatus, 'service').split(' ')[0] + ' p-2 rounded-full'}>
                    {getStatusIcon(watchedStatus)}
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">
                      {SERVICE_STATUS_LABELS[watchedStatus as keyof typeof SERVICE_STATUS_LABELS]}
                    </p>
                    <div className="flex items-center gap-2 mt-1">
                      <Progress value={getProgressPercentage(watchedStatus)} className="h-2 flex-1" />
                      <span className="text-sm text-gray-500">
                        {getProgressPercentage(watchedStatus)}%
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notas del Progreso</Label>
            <Textarea
              {...form.register('notes')}
              placeholder="Describe el progreso realizado, problemas encontrados, etc..."
              rows={4}
            />
            {form.formState.errors.notes && (
              <p className="text-sm text-red-500">{form.formState.errors.notes.message}</p>
            )}
          </div>

          {/* Evidence Upload */}
          <div className="space-y-4">
            <Label>Evidencia (Fotos/Videos)</Label>

            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
              <div className="text-center">
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-4">
                  <label htmlFor="evidence-upload" className="cursor-pointer">
                    <span className="mt-2 block text-sm font-medium text-gray-900">
                      Subir archivos de evidencia
                    </span>
                    <span className="mt-1 block text-sm text-gray-500">
                      PNG, JPG, MP4 hasta 10MB cada uno
                    </span>
                  </label>
                  <input
                    id="evidence-upload"
                    type="file"
                    multiple
                    accept="image/*,video/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                </div>
              </div>
            </div>

            {/* Selected Files */}
            {selectedFiles.length > 0 && (
              <div className="space-y-2">
                <Label>Archivos Seleccionados</Label>
                <div className="space-y-2">
                  {selectedFiles.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm truncate">{file.name}</span>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeFile(index)}
                      >
                        Eliminar
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancelar
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Actualizar Progreso
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
