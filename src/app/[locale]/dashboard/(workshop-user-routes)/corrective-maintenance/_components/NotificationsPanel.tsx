'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Bell,
  Clock,
  AlertTriangle,
  CheckCircle,
  Package,
  DollarSign,
  X,
  Eye
} from 'lucide-react';

interface Notification {
  id: string;
  type: 'sla_warning' | 'parts_arrived' | 'approval_pending' | 'order_completed' | 'payment_due';
  title: string;
  message: string;
  orderId?: string;
  vehicleInfo?: string;
  timestamp: string;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high';
}

interface NotificationsPanelProps {
  notifications: Notification[];
  onMarkAsRead: (notificationId: string) => void;
  onDismiss: (notificationId: string) => void;
  onViewOrder: (orderId: string) => void;
}

export default function NotificationsPanel({
  notifications,
  onMarkAsRead,
  onDismiss,
  onViewOrder
}: NotificationsPanelProps) {
  const [filter, setFilter] = useState<'all' | 'unread' | 'high'>('all');

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'sla_warning': return <Clock className="h-4 w-4" />;
      case 'parts_arrived': return <Package className="h-4 w-4" />;
      case 'approval_pending': return <AlertTriangle className="h-4 w-4" />;
      case 'order_completed': return <CheckCircle className="h-4 w-4" />;
      case 'payment_due': return <DollarSign className="h-4 w-4" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  const getNotificationColor = (type: string, priority: string) => {
    if (priority === 'high') {
      return 'bg-red-100 text-red-800 border-red-200';
    }

    switch (type) {
      case 'sla_warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'parts_arrived': return 'bg-green-100 text-green-800 border-green-200';
      case 'approval_pending': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'order_completed': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'payment_due': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'high': return 'Alta';
      case 'medium': return 'Media';
      case 'low': return 'Baja';
      default: return 'Normal';
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    switch (filter) {
      case 'unread': return !notification.isRead;
      case 'high': return notification.priority === 'high';
      default: return true;
    }
  });

  const unreadCount = notifications.filter(n => !n.isRead).length;
  const highPriorityCount = notifications.filter(n => n.priority === 'high').length;

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notificaciones
            {unreadCount > 0 && (
              <Badge variant="destructive" className="ml-2">
                {unreadCount}
              </Badge>
            )}
          </CardTitle>
        </div>

        {/* Filter Buttons */}
        <div className="flex gap-2 mt-3">
          <Button
            variant={filter === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('all')}
          >
            Todas ({notifications.length})
          </Button>
          <Button
            variant={filter === 'unread' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('unread')}
          >
            No leídas ({unreadCount})
          </Button>
          <Button
            variant={filter === 'high' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('high')}
          >
            Urgentes ({highPriorityCount})
          </Button>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <ScrollArea className="h-[400px]">
          {filteredNotifications.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              <Bell className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No hay notificaciones</p>
              <p className="text-sm">
                {filter === 'unread' && 'Todas las notificaciones han sido leídas'}
                {filter === 'high' && 'No hay notificaciones urgentes'}
                {filter === 'all' && 'No tienes notificaciones en este momento'}
              </p>
            </div>
          ) : (
            <div className="space-y-1">
              {filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 border-l-4 hover:bg-gray-50 transition-colors ${
                    getNotificationColor(notification.type, notification.priority)
                  } ${!notification.isRead ? 'bg-blue-50' : ''}`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3 flex-1">
                      <div className="mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className={`text-sm font-medium ${
                            !notification.isRead ? 'font-semibold' : ''
                          }`}>
                            {notification.title}
                          </h4>
                          <Badge
                            variant="outline"
                            className={notification.priority === 'high' ? 'border-red-300 text-red-700' : ''}
                          >
                            {getPriorityLabel(notification.priority)}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">
                          {notification.message}
                        </p>
                        {notification.vehicleInfo && (
                          <p className="text-xs text-gray-500 mb-2">
                            Vehículo: {notification.vehicleInfo}
                          </p>
                        )}
                        <div className="flex items-center gap-2 text-xs text-gray-500">
                          <span>
                            {format(new Date(notification.timestamp), 'dd/MM/yyyy HH:mm', { locale: es })}
                          </span>
                          {!notification.isRead && (
                            <Badge variant="secondary">
                              Nuevo
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-1 ml-2">
                      {notification.orderId && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            onViewOrder(notification.orderId!);
                            if (!notification.isRead) {
                              onMarkAsRead(notification.id);
                            }
                          }}
                          className="h-8 w-8 p-0"
                        >
                          <Eye className="h-3 w-3" />
                        </Button>
                      )}
                      {!notification.isRead && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onMarkAsRead(notification.id)}
                          className="h-8 w-8 p-0"
                        >
                          <CheckCircle className="h-3 w-3" />
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onDismiss(notification.id)}
                        className="h-8 w-8 p-0 text-gray-400 hover:text-red-600"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

// Mock data generator for testing
export const generateMockNotifications = (): Notification[] => [
  {
    id: '1',
    type: 'sla_warning',
    title: 'SLA en riesgo',
    message: 'La orden #CM001 está cerca de exceder el tiempo estimado de reparación.',
    orderId: 'cm001',
    vehicleInfo: 'Toyota Corolla - ABC-123',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    isRead: false,
    priority: 'high',
  },
  {
    id: '2',
    type: 'parts_arrived',
    title: 'Refacciones disponibles',
    message: 'Las pastillas de freno para la orden #CM002 han llegado al taller.',
    orderId: 'cm002',
    vehicleInfo: 'Honda Civic - XYZ-789',
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
    isRead: false,
    priority: 'medium',
  },
  {
    id: '3',
    type: 'approval_pending',
    title: 'Aprobación pendiente',
    message: 'La cotización de $2,500 MXN está esperando aprobación de flotillas.',
    orderId: 'cm003',
    vehicleInfo: 'Nissan Sentra - DEF-456',
    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
    isRead: true,
    priority: 'medium',
  },
  {
    id: '4',
    type: 'order_completed',
    title: 'Orden completada',
    message: 'El mantenimiento correctivo de la orden #CM004 ha sido completado exitosamente.',
    orderId: 'cm004',
    vehicleInfo: 'Ford Focus - GHI-123',
    timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8 hours ago
    isRead: true,
    priority: 'low',
  },
  {
    id: '5',
    type: 'payment_due',
    title: 'Pago pendiente',
    message: 'El cliente debe $1,800 MXN por servicios completados.',
    orderId: 'cm005',
    vehicleInfo: 'Chevrolet Aveo - JKL-789',
    timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // 12 hours ago
    isRead: false,
    priority: 'high',
  },
];
