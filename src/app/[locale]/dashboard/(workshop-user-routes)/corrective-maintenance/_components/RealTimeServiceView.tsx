'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Clock, CheckCircle, Timer, Car, User, Calendar, 
  Image as ImageIcon, MessageSquare, Phone, Mail,
  AlertTriangle, PlayCircle, PauseCircle, MapPin,
  Wrench, Package, Camera, FileText
} from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';
import { es } from 'date-fns/locale';

interface ServicePhase {
  id: string;
  name: string;
  description: string;
  estimatedDuration: number;
  status: 'pending' | 'in-progress' | 'completed' | 'skipped';
  actualDuration?: number;
  startedAt?: string;
  completedAt?: string;
  evidence: string[];
  notes?: string;
}

interface ServiceUpdate {
  id: string;
  timestamp: string;
  type: 'phase_start' | 'phase_complete' | 'note' | 'evidence' | 'milestone';
  title: string;
  description: string;
  phase?: string;
  evidence?: string[];
  technicianName?: string;
}

interface VehicleInfo {
  brand: string;
  model: string;
  year: number;
  plate: string;
  vin?: string;
  color?: string;
}

interface TechnicianInfo {
  name: string;
  phone?: string;
  email?: string;
  photo?: string;
  specialization?: string;
}

interface WorkshopInfo {
  name: string;
  address: string;
  phone: string;
  email?: string;
}

interface RealTimeServiceViewProps {
  serviceId: string;
  orderId: string;
  serviceName: string;
  description: string;
  status: string;
  estimatedCost: number;
  startedAt?: string;
  estimatedCompletionTime?: string;
  phases: ServicePhase[];
  updates: ServiceUpdate[];
  vehicle: VehicleInfo;
  technician: TechnicianInfo;
  workshop: WorkshopInfo;
  customerView?: boolean;
}

export default function RealTimeServiceView({
  serviceId,
  orderId,
  serviceName,
  description,
  status,
  estimatedCost,
  startedAt,
  estimatedCompletionTime,
  phases,
  updates,
  vehicle,
  technician,
  workshop,
  customerView = false
}: RealTimeServiceViewProps) {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Auto-refresh data
  useEffect(() => {
    const interval = setInterval(() => {
      // In a real implementation, this would trigger a data refresh
      console.log('Refreshing service data...');
    }, refreshInterval * 1000);

    return () => clearInterval(interval);
  }, [refreshInterval]);

  const getOverallProgress = () => {
    const completedPhases = phases.filter(phase => phase.status === 'completed').length;
    const inProgressPhases = phases.filter(phase => phase.status === 'in-progress').length * 0.5;
    return Math.round(((completedPhases + inProgressPhases) / phases.length) * 100);
  };

  const getCurrentPhase = () => {
    return phases.find(phase => phase.status === 'in-progress') || 
           phases.find(phase => phase.status === 'pending') ||
           phases[phases.length - 1];
  };

  const getEstimatedTimeRemaining = () => {
    if (!startedAt) return null;
    
    const completedTime = phases
      .filter(phase => phase.status === 'completed')
      .reduce((total, phase) => total + (phase.actualDuration || phase.estimatedDuration), 0);
    
    const totalEstimatedTime = phases.reduce((total, phase) => total + phase.estimatedDuration, 0);
    const remainingTime = totalEstimatedTime - completedTime;
    
    return Math.max(0, remainingTime);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'not-started': return 'bg-gray-100 text-gray-800';
      case 'in-progress': return 'bg-blue-100 text-blue-800 animate-pulse';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'waiting-for-parts': return 'bg-yellow-100 text-yellow-800';
      case 'paused': return 'bg-orange-100 text-orange-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'not-started': return <Clock className="h-4 w-4" />;
      case 'in-progress': return <PlayCircle className="h-4 w-4" />;
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'waiting-for-parts': return <Package className="h-4 w-4" />;
      case 'paused': return <PauseCircle className="h-4 w-4" />;
      case 'cancelled': return <AlertTriangle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'not-started': return 'No Iniciado';
      case 'in-progress': return 'En Progreso';
      case 'completed': return 'Completado';
      case 'waiting-for-parts': return 'Esperando Refacciones';
      case 'paused': return 'Pausado';
      case 'cancelled': return 'Cancelado';
      default: return 'Estado Desconocido';
    }
  };

  const getPhaseStatusIcon = (phaseStatus: ServicePhase['status']) => {
    switch (phaseStatus) {
      case 'pending': return <Clock className="h-4 w-4 text-gray-400" />;
      case 'in-progress': return <Timer className="h-4 w-4 text-blue-500 animate-pulse" />;
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'skipped': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default: return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getUpdateIcon = (type: ServiceUpdate['type']) => {
    switch (type) {
      case 'phase_start': return <PlayCircle className="h-4 w-4 text-blue-500" />;
      case 'phase_complete': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'note': return <MessageSquare className="h-4 w-4 text-blue-500" />;
      case 'evidence': return <Camera className="h-4 w-4 text-purple-500" />;
      case 'milestone': return <CheckCircle className="h-4 w-4 text-green-500" />;
      default: return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN'
    }).format(amount);
  };

  const currentPhase = getCurrentPhase();
  const timeRemaining = getEstimatedTimeRemaining();
  const overallProgress = getOverallProgress();

  return (
    <div className="max-w-6xl mx-auto p-4 space-y-6">
      {/* Header with real-time status */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl">{serviceName}</CardTitle>
              <p className="text-gray-600 mt-1">{description}</p>
            </div>
            <div className="flex items-center gap-4">
              <Badge className={getStatusColor(status)} variant="secondary">
                {getStatusIcon(status)}
                <span className="ml-2">{getStatusLabel(status)}</span>
              </Badge>
              <div className="text-right text-sm text-gray-500">
                <p>Actualizado:</p>
                <p className="font-medium">{format(currentTime, 'HH:mm:ss')}</p>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Service Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Progress Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Timer className="h-5 w-5" />
                Progreso del Servicio
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Progreso General</span>
                <span className="text-sm text-gray-500">{overallProgress}%</span>
              </div>
              <Progress value={overallProgress} className="h-3" />
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">{overallProgress}%</p>
                  <p className="text-xs text-gray-500">Completado</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">
                    {timeRemaining ? `${timeRemaining}m` : 'N/A'}
                  </p>
                  <p className="text-xs text-gray-500">Tiempo Restante</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-purple-600">{phases.length}</p>
                  <p className="text-xs text-gray-500">Fases Total</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-orange-600">
                    {formatCurrency(estimatedCost)}
                  </p>
                  <p className="text-xs text-gray-500">Costo</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Current Phase */}
          {currentPhase && (
            <Card className="border-l-4 border-l-blue-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {getPhaseStatusIcon(currentPhase.status)}
                  Fase Actual: {currentPhase.name}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">{currentPhase.description}</p>
                <div className="flex items-center gap-4">
                  <Badge className={
                    currentPhase.status === 'in-progress' 
                      ? 'bg-blue-100 text-blue-800' 
                      : 'bg-gray-100 text-gray-800'
                  }>
                    {currentPhase.status === 'in-progress' ? 'En Progreso' : 'Pendiente'}
                  </Badge>
                  <span className="text-sm text-gray-500">
                    Duración estimada: {currentPhase.estimatedDuration} minutos
                  </span>
                </div>
                
                {currentPhase.startedAt && (
                  <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm">
                      <strong>Iniciado:</strong> {format(new Date(currentPhase.startedAt), 'HH:mm', { locale: es })}
                      <span className="text-gray-500 ml-2">
                        ({formatDistanceToNow(new Date(currentPhase.startedAt), { locale: es, addSuffix: true })})
                      </span>
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Service Phases Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Fases del Servicio</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {phases.map((phase, index) => (
                  <div key={phase.id} className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        phase.status === 'completed' ? 'bg-green-100' :
                        phase.status === 'in-progress' ? 'bg-blue-100' : 'bg-gray-100'
                      }`}>
                        {getPhaseStatusIcon(phase.status)}
                      </div>
                      {index < phases.length - 1 && (
                        <div className={`w-0.5 h-8 ml-4 mt-1 ${
                          phase.status === 'completed' ? 'bg-green-300' : 'bg-gray-200'
                        }`} />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium">{phase.name}</h4>
                        <div className="flex items-center gap-2">
                          {phase.evidence.length > 0 && (
                            <Badge variant="outline" className="text-xs">
                              <ImageIcon className="h-3 w-3 mr-1" />
                              {phase.evidence.length}
                            </Badge>
                          )}
                          <span className="text-xs text-gray-500">
                            {phase.estimatedDuration}m
                          </span>
                        </div>
                      </div>
                      <p className="text-xs text-gray-600 mt-1">{phase.description}</p>
                      
                      {phase.completedAt && (
                        <p className="text-xs text-green-600 mt-2">
                          Completado: {format(new Date(phase.completedAt), 'HH:mm')}
                        </p>
                      )}
                      
                      {phase.notes && (
                        <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
                          {phase.notes}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Updates */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Actualizaciones Recientes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {updates.slice(0, 10).map((update) => (
                  <div key={update.id} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="flex-shrink-0">
                      {getUpdateIcon(update.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium">{update.title}</h4>
                        <span className="text-xs text-gray-500">
                          {format(new Date(update.timestamp), 'HH:mm')}
                        </span>
                      </div>
                      <p className="text-xs text-gray-600 mt-1">{update.description}</p>
                      {update.technicianName && (
                        <p className="text-xs text-blue-600 mt-1">
                          por {update.technicianName}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar with vehicle and contact info */}
        <div className="space-y-6">
          {/* Vehicle Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Car className="h-5 w-5" />
                Información del Vehículo
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <p className="text-sm text-gray-500">Vehículo</p>
                <p className="font-medium">{vehicle.brand} {vehicle.model} {vehicle.year}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Placas</p>
                <p className="font-medium">{vehicle.plate}</p>
              </div>
              {vehicle.color && (
                <div>
                  <p className="text-sm text-gray-500">Color</p>
                  <p className="font-medium">{vehicle.color}</p>
                </div>
              )}
              {vehicle.vin && (
                <div>
                  <p className="text-sm text-gray-500">VIN</p>
                  <p className="font-mono text-xs">{vehicle.vin}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Technician Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Técnico Asignado
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-3">
                {technician.photo ? (
                  <img 
                    src={technician.photo} 
                    alt={technician.name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center">
                    <User className="h-6 w-6 text-gray-500" />
                  </div>
                )}
                <div>
                  <p className="font-medium">{technician.name}</p>
                  {technician.specialization && (
                    <p className="text-sm text-gray-500">{technician.specialization}</p>
                  )}
                </div>
              </div>
              
              {!customerView && (
                <div className="space-y-2">
                  {technician.phone && (
                    <Button variant="outline" size="sm" className="w-full justify-start">
                      <Phone className="h-4 w-4 mr-2" />
                      {technician.phone}
                    </Button>
                  )}
                  {technician.email && (
                    <Button variant="outline" size="sm" className="w-full justify-start">
                      <Mail className="h-4 w-4 mr-2" />
                      {technician.email}
                    </Button>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Workshop Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wrench className="h-5 w-5" />
                Taller
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <p className="font-medium">{workshop.name}</p>
                <div className="flex items-start gap-2 mt-2">
                  <MapPin className="h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-gray-600">{workshop.address}</p>
                </div>
              </div>
              
              <div className="space-y-2">
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Phone className="h-4 w-4 mr-2" />
                  {workshop.phone}
                </Button>
                {workshop.email && (
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    <Mail className="h-4 w-4 mr-2" />
                    {workshop.email}
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Service Times */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Tiempos del Servicio
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {startedAt && (
                <div>
                  <p className="text-sm text-gray-500">Iniciado</p>
                  <p className="font-medium">
                    {format(new Date(startedAt), 'dd/MM/yyyy HH:mm')}
                  </p>
                </div>
              )}
              {estimatedCompletionTime && (
                <div>
                  <p className="text-sm text-gray-500">Finalización Estimada</p>
                  <p className="font-medium">
                    {format(new Date(estimatedCompletionTime), 'dd/MM/yyyy HH:mm')}
                  </p>
                </div>
              )}
              {timeRemaining && (
                <div>
                  <p className="text-sm text-gray-500">Tiempo Restante</p>
                  <p className="font-medium text-blue-600">{timeRemaining} minutos</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Auto-refresh control */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Actualización Automática</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Cada {refreshInterval}s</span>
                <div className="flex gap-1">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setRefreshInterval(15)}
                    className={refreshInterval === 15 ? 'bg-blue-50' : ''}
                  >
                    15s
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setRefreshInterval(30)}
                    className={refreshInterval === 30 ? 'bg-blue-50' : ''}
                  >
                    30s
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setRefreshInterval(60)}
                    className={refreshInterval === 60 ? 'bg-blue-50' : ''}
                  >
                    1m
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}