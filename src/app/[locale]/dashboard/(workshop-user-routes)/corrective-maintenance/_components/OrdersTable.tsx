'use client';

import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Eye, ChevronLeft, ChevronRight, Package, Wrench, Clock, AlertTriangle } from 'lucide-react';
import {
  CorrectiveMaintenanceOrder,
  getStatusLabel,
  getStatusColor,
  formatCurrency,
  ORDER_TYPE_LABELS,
  FAILURE_TYPE_LABELS
} from '../types';

interface OrdersTableProps {
  data: CorrectiveMaintenanceOrder[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  onPageChange: (page: number) => void;
}

export default function OrdersTable({ data, pagination, onPageChange }: OrdersTableProps) {
  const router = useRouter();



  const handleViewOrder = (orderId: string) => {
    router.push(`/dashboard/corrective-maintenance/${orderId}`);
  };

  if (!data || data.length === 0) {
    return (
      <Card>
        <CardContent className="py-10">
          <div className="text-center">
            <p className="text-gray-500">No se encontraron órdenes de mantenimiento correctivo</p>
            <p className="text-xs text-gray-400 mt-2">
              Crea una nueva orden para comenzar
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Orden
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Vehículo
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tipo
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Estado
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Progreso
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Costo Estimado
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Fecha
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Acciones
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.map((order) => (
                  <tr key={order._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          #{order._id.slice(-8).toUpperCase()}
                        </div>
                        <div className="text-sm text-gray-500">
                          {FAILURE_TYPE_LABELS[order.failureType]}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {order.vehicle?.brand} {order.vehicle?.model}
                        </div>
                        <div className="text-sm text-gray-500">
                          {order.vehicle?.carNumber} • {order.vehicle?.carPlates?.plates}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {ORDER_TYPE_LABELS[order.type]}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge
                        className={getStatusColor(order.status, 'order')}
                        variant="secondary"
                      >
                        {getStatusLabel(order.status, 'order')}
                      </Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {order.services && order.services.length > 0 ? (
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <Wrench className="h-3 w-3 text-gray-500" />
                            <span className="text-xs text-gray-600">
                              {order.services.filter(s => s.status === 'completed').length}/{order.services.length} servicios
                            </span>
                          </div>
                          {order.services.some(s => s.parts && s.parts.length > 0) && (
                            <div className="flex items-center gap-2">
                              <Package className="h-3 w-3 text-gray-500" />
                              <span className="text-xs text-gray-600">
                                {order.services.reduce((acc, s) => acc + (s.parts?.filter(p => p.availability === 'available').length || 0), 0)}/
                                {order.services.reduce((acc, s) => acc + (s.parts?.length || 0), 0)} partes
                              </span>
                              {order.services.some(s => s.parts?.some(p => p.availability === 'unavailable')) && (
                                <AlertTriangle className="h-3 w-3 text-orange-500" />
                              )}
                            </div>
                          )}
                          {order.services.some(s => s.status === 'in-progress') && (
                            <div className="flex items-center gap-2">
                              <Clock className="h-3 w-3 text-blue-500" />
                              <span className="text-xs text-blue-600">En progreso</span>
                            </div>
                          )}
                        </div>
                      ) : (
                        <span className="text-xs text-gray-400">Sin servicios</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {order.totalEstimatedCost ? formatCurrency(order.totalEstimatedCost) : 'Pendiente'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {format(new Date(order.createdAt), 'dd/MM/yyyy', { locale: es })}
                      </div>
                      <div className="text-sm text-gray-500">
                        {format(new Date(order.createdAt), 'HH:mm', { locale: es })}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewOrder(order._id)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        Ver
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Mostrando {((pagination.page - 1) * pagination.limit) + 1} a{' '}
            {Math.min(pagination.page * pagination.limit, pagination.total)} de{' '}
            {pagination.total} resultados
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Anterior
            </Button>

            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                const pageNumber = i + 1;
                return (
                  <Button
                    key={pageNumber}
                    variant={pagination.page === pageNumber ? "default" : "outline"}
                    size="sm"
                    onClick={() => onPageChange(pageNumber)}
                  >
                    {pageNumber}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(pagination.page + 1)}
              disabled={pagination.page >= pagination.totalPages}
            >
              Siguiente
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
