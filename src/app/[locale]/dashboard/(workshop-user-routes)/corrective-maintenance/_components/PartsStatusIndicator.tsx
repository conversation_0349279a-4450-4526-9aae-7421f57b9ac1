'use client';

import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import {
  Package,
  Clock,
  CheckCircle,
  AlertTriangle,
  Truck
} from 'lucide-react';
import { CorrectiveService } from '../types';

interface PartsStatusIndicatorProps {
  services: CorrectiveService[];
  compact?: boolean;
}

export default function PartsStatusIndicator({ services, compact = false }: PartsStatusIndicatorProps) {
  // Calculate parts statistics
  const allParts = services.flatMap(service => service.parts || []);
  const totalParts = allParts.length;

  if (totalParts === 0) {
    return compact ? (
      <Badge variant="outline" className="text-gray-600">
        Sin refacciones
      </Badge>
    ) : null;
  }

  const availableParts = allParts.filter(part => part.availability === 'available');
  const pendingParts = allParts.filter(part => part.availability === 'pending');
  const unavailableParts = allParts.filter(part => part.availability === 'unavailable');

  const availabilityPercentage = (availableParts.length / totalParts) * 100;

  const getStatusIcon = (availability: string) => {
    switch (availability) {
      case 'available': return <CheckCircle className="h-3 w-3" />;
      case 'pending': return <Clock className="h-3 w-3" />;
      case 'unavailable': return <AlertTriangle className="h-3 w-3" />;
      default: return <Package className="h-3 w-3" />;
    }
  };

  const getStatusColor = (availability: string) => {
    switch (availability) {
      case 'available': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'unavailable': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (availability: string) => {
    switch (availability) {
      case 'available': return 'Disponible';
      case 'pending': return 'Pendiente';
      case 'unavailable': return 'No disponible';
      default: return 'Desconocido';
    }
  };

  if (compact) {
    return (
      <div className="flex items-center gap-2">
        <Package className="h-4 w-4 text-gray-500" />
        <div className="flex items-center gap-1">
          {availableParts.length > 0 && (
            <Badge className="bg-green-100 text-green-800">
              {availableParts.length} disponible{availableParts.length !== 1 ? 's' : ''}
            </Badge>
          )}
          {pendingParts.length > 0 && (
            <Badge className="bg-yellow-100 text-yellow-800">
              {pendingParts.length} pendiente{pendingParts.length !== 1 ? 's' : ''}
            </Badge>
          )}
          {unavailableParts.length > 0 && (
            <Badge className="bg-red-100 text-red-800">
              {unavailableParts.length} no disponible{unavailableParts.length !== 1 ? 's' : ''}
            </Badge>
          )}
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-base">
          <Package className="h-5 w-5" />
          Estado de Refacciones
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Overall Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>Disponibilidad general</span>
            <span className="font-medium">{Math.round(availabilityPercentage)}%</span>
          </div>
          <Progress value={availabilityPercentage} className="h-2" />
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-3 gap-3 text-sm">
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="font-medium text-green-600">{availableParts.length}</span>
            </div>
            <p className="text-gray-600">Disponibles</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Clock className="h-4 w-4 text-yellow-600" />
              <span className="font-medium text-yellow-600">{pendingParts.length}</span>
            </div>
            <p className="text-gray-600">Pendientes</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <span className="font-medium text-red-600">{unavailableParts.length}</span>
            </div>
            <p className="text-gray-600">No disponibles</p>
          </div>
        </div>

        {/* Parts by Service */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Por servicio:</h4>
          {services.filter(service => service.parts && service.parts.length > 0).map(service => (
            <div key={service._id} className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">{service.serviceName}</span>
                <Badge variant="outline">
                  {service.parts.length} refacción{service.parts.length !== 1 ? 'es' : ''}
                </Badge>
              </div>
              <div className="space-y-1">
                {service.parts.map((part, index) => (
                  <div key={index} className="flex items-center justify-between text-xs">
                    <div className="flex items-center gap-2">
                      <span>{part.name}</span>
                      <span className="text-gray-500">(x{part.quantity})</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {part.eta && part.availability === 'pending' && (
                        <div className="flex items-center gap-1 text-gray-500">
                          <Truck className="h-3 w-3" />
                          <span>{new Date(part.eta).toLocaleDateString('es-ES')}</span>
                        </div>
                      )}
                      <Badge
                        className={getStatusColor(part.availability)}
                        variant="secondary"
                      >
                        {getStatusIcon(part.availability)}
                        <span className="ml-1">{getStatusLabel(part.availability)}</span>
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Recommendations */}
        {pendingParts.length > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-yellow-800">Refacciones pendientes</p>
                <p className="text-yellow-700">
                  {pendingParts.length} refacción{pendingParts.length !== 1 ? 'es' : ''}
                  {pendingParts.length !== 1 ? ' están' : ' está'} pendiente{pendingParts.length !== 1 ? 's' : ''}.
                  Los servicios relacionados quedarán en espera hasta que lleguen.
                </p>
              </div>
            </div>
          </div>
        )}

        {unavailableParts.length > 0 && (
          <div className="bg-red-50 border border-red-200 rounded p-3">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-red-800">Refacciones no disponibles</p>
                <p className="text-red-700">
                  {unavailableParts.length} refacción{unavailableParts.length !== 1 ? 'es' : ''}
                  no {unavailableParts.length !== 1 ? 'están' : 'está'} disponible{unavailableParts.length !== 1 ? 's' : ''}.
                  Contacta al proveedor para verificar disponibilidad.
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
