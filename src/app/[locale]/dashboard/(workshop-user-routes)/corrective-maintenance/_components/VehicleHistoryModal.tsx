'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import {
  Loader2,
  Car,
  Wrench,
  FileText,
  Calendar,
  DollarSign,
  Clock,
  CheckCircle,
  AlertTriangle,
  Image,
  Eye,
  Download,
  Package,
  User,
  MapPin,
  Phone,
  Mail,
  Star,
  MessageSquare,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { formatCurrency, formatDuration, getStatusColor, getStatusLabel } from '../types';

interface MaintenanceRecord {
  _id: string;
  type: 'preventive' | 'corrective';
  status: string;
  date: string;
  createdAt: string;
  updatedAt: string;
  failureType?: string;
  arrivalMethod?: string;
  customerDescription?: string;
  diagnosisNotes?: string;
  completionNotes?: string;
  workshop: {
    _id: string;
    name: string;
    address?: string;
    phone?: string;
    email?: string;
  };
  associate?: {
    _id: string;
    name: string;
    email: string;
    phone?: string;
  };
  services: Array<{
    _id: string;
    serviceName: string;
    description?: string;
    status: string;
    estimatedCost: number;
    actualCost?: number;
    estimatedDuration?: number;
    actualDuration?: number;
    completedAt?: string;
    startedAt?: string;
    notes?: string;
    technicalNotes?: string;
    qualityCheckPassed?: boolean;
    progressPhotos?: string[];
    parts?: Array<{
      _id: string;
      name: string;
      partNumber?: string;
      quantity: number;
      unitCost: number;
      supplier?: string;
      availability: 'available' | 'pending' | 'unavailable';
      eta?: string;
      notes?: string;
    }>;
  }>;
  quotation?: {
    _id: string;
    status: string;
    totalAmount: number;
    validityDays: number;
    approvalType: string;
    approverEmail?: string;
    customerNotes?: string;
    internalNotes?: string;
    paymentTerms?: string;
    warrantyTerms?: string;
    createdAt: string;
    submittedAt?: string;
    approvedAt?: string;
    rejectedAt?: string;
    rejectionReason?: string;
  };
  beforePhotos?: string[];
  afterPhotos?: string[];
  diagnosisPhotos?: string[];
  totalCost: number;
  totalActualCost?: number;
  customerSatisfactionRating?: number;
  workQualityRating?: number;
  recommendationsForFuture?: string;
  finalInspectionPassed?: boolean;
  notes?: string;
}

interface VehicleInfo {
  _id: string;
  brand: string;
  model: string;
  year: number;
  carNumber: string;
  carPlates: {
    plates: string;
  };
  mileage?: number;
}

interface VehicleHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  vehicleId: string;
}

export default function VehicleHistoryModal({ isOpen, onClose, vehicleId }: VehicleHistoryModalProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [vehicle, setVehicle] = useState<VehicleInfo | null>(null);
  const [maintenanceHistory, setMaintenanceHistory] = useState<MaintenanceRecord[]>([]);

  useEffect(() => {
    if (isOpen && vehicleId) {
      loadVehicleHistory();
    }
  }, [isOpen, vehicleId]);

  const loadVehicleHistory = async () => {
    setIsLoading(true);
    try {
      console.log('🔍 Loading vehicle history for vehicleId:', vehicleId);
      console.log('🔍 vehicleId type:', typeof vehicleId);
      console.log('🔍 vehicleId length:', vehicleId?.length);

      // Get vehicle information
      console.log('📞 Fetching vehicle info...');
      const vehicleResponse = await fetch(`/api/vendor-platform/vehicles/${vehicleId}`);
      console.log('📞 Vehicle response status:', vehicleResponse.status);

      if (!vehicleResponse.ok) {
        const errorText = await vehicleResponse.text();
        console.error('❌ Vehicle fetch failed:', errorText);
        throw new Error(`Failed to fetch vehicle information: ${vehicleResponse.status}`);
      }

      const vehicleData = await vehicleResponse.json();
      console.log('✅ Vehicle data received:', vehicleData);
      setVehicle(vehicleData.data);

      // Get maintenance history for this vehicle
      console.log('📞 Fetching maintenance history...');
      const historyResponse = await fetch(
        `/api/vendor-platform/corrective-maintenance/orders?stockId=${vehicleId}&limit=50&page=1`
      );
      console.log('📞 History response status:', historyResponse.status);
      console.log('📞 History request URL:', `/api/vendor-platform/corrective-maintenance/orders?stockId=${vehicleId}&limit=50&page=1`);

      if (!historyResponse.ok) {
        const errorText = await historyResponse.text();
        console.error('❌ History fetch failed:', errorText);
        throw new Error(`Failed to fetch maintenance history: ${historyResponse.status}`);
      }

      const historyData = await historyResponse.json();
      console.log('✅ Vehicle history response:', historyData);
      console.log('📊 History data length:', historyData.data?.length || 0);
      console.log('📊 Raw history data:', historyData.data);

      // Check if we have data to transform
      if (!historyData.data || historyData.data.length === 0) {
        console.log('📊 No history data found for vehicle:', vehicleId);
        console.log('📊 Response structure:', historyData);
        console.log('📊 historyData.data:', historyData.data);
        console.log('📊 historyData.success:', historyData.success);
        setMaintenanceHistory([]);
        return;
      }

      // Transform the orders to match our interface
      console.log('🔄 Transforming history data...');
      const transformedHistory: MaintenanceRecord[] = historyData.data.map((order: any, index: number) => {
        console.log(`🔄 Transforming order ${index + 1}:`, order);
        return {
        _id: order._id,
        type: 'corrective', // All records from this API are corrective maintenance
        status: order.status,
        date: order.createdAt,
        createdAt: order.createdAt,
        updatedAt: order.updatedAt,
        failureType: order.failureType,
        arrivalMethod: order.arrivalMethod,
        customerDescription: order.customerDescription,
        diagnosisNotes: order.diagnosisNotes,
        completionNotes: order.completionNotes,
        workshop: {
          _id: order.workshop?._id || '',
          name: order.workshop?.name || 'Taller no especificado',
          address: order.workshop?.address,
          phone: order.workshop?.phone,
          email: order.workshop?.email,
        },
        associate: order.associate ? {
          _id: order.associate._id,
          name: order.associate.name,
          email: order.associate.email,
          phone: order.associate.phone,
        } : undefined,
        services: order.services?.map((service: any) => ({
          _id: service._id,
          serviceName: service.serviceName,
          description: service.description,
          status: service.status,
          estimatedCost: service.estimatedCost || 0,
          actualCost: service.actualCost,
          estimatedDuration: service.estimatedDuration,
          actualDuration: service.actualDuration,
          completedAt: service.completedAt,
          startedAt: service.startedAt,
          notes: service.notes,
          technicalNotes: service.technicalNotes,
          qualityCheckPassed: service.qualityCheckPassed,
          progressPhotos: service.progressPhotos || [],
          parts: service.parts || [],
        })) || [],
        quotation: order.quotation,
        beforePhotos: order.beforePhotos || [],
        afterPhotos: order.afterPhotos || [],
        diagnosisPhotos: order.diagnosisPhotos || [],
        totalCost: order.quotation?.totalAmount || 0,
        totalActualCost: order.totalActualCost,
        customerSatisfactionRating: order.customerSatisfactionRating,
        workQualityRating: order.workQualityRating,
        recommendationsForFuture: order.recommendationsForFuture,
        finalInspectionPassed: order.finalInspectionPassed,
        notes: order.notes,
      };
      });

      console.log('✅ Transformed history:', transformedHistory);
      setMaintenanceHistory(transformedHistory);
    } catch (error) {
      console.error('❌ Error loading vehicle history:', error);
      toast({
        title: 'Error',
        description: 'Error al cargar el historial del vehículo',
        variant: 'destructive',
      });

      // Set empty history on error
      setMaintenanceHistory([]);
    } finally {
      setIsLoading(false);
    }
  };

  const getMaintenanceTypeIcon = (type: string) => {
    return type === 'preventive' ? <Calendar className="w-4 h-4" /> : <Wrench className="w-4 h-4" />;
  };

  const getMaintenanceTypeColor = (type: string) => {
    return type === 'preventive' ? 'bg-blue-100 text-blue-800' : 'bg-orange-100 text-orange-800';
  };

  const getMaintenanceTypeLabel = (type: string) => {
    return type === 'preventive' ? 'Preventivo' : 'Correctivo';
  };

  const correctiveHistory = maintenanceHistory.filter(record => record.type === 'corrective');
  const preventiveHistory = maintenanceHistory.filter(record => record.type === 'preventive');
  const totalSpent = maintenanceHistory.reduce((sum, record) => sum + record.totalCost, 0);
  const totalServices = maintenanceHistory.reduce((sum, record) => sum + record.services.length, 0);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Car className="w-5 h-5" />
            Historial de Mantenimiento
          </DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-8 h-8 animate-spin" />
            <span className="ml-2">Cargando historial...</span>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Vehicle Information */}
            {vehicle && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Car className="w-5 h-5" />
                    Información del Vehículo
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 text-sm md:grid-cols-4">
                    <div>
                      <p className="text-gray-500">Vehículo:</p>
                      <p className="font-medium">{vehicle.brand} {vehicle.model} {vehicle.year}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Número:</p>
                      <p className="font-medium">{vehicle.carNumber}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Placas:</p>
                      <p className="font-medium">{vehicle.carPlates.plates}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Kilometraje:</p>
                      <p className="font-medium">{vehicle.mileage?.toLocaleString()} km</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Summary Statistics */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Mantenimientos</p>
                      <p className="text-2xl font-bold">{maintenanceHistory.length}</p>
                    </div>
                    <div className="p-2 bg-blue-100 rounded-full">
                      <FileText className="w-4 h-4 text-blue-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Servicios Realizados</p>
                      <p className="text-2xl font-bold">{totalServices}</p>
                    </div>
                    <div className="p-2 bg-green-100 rounded-full">
                      <Wrench className="w-4 h-4 text-green-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Gasto Total</p>
                      <p className="text-2xl font-bold">{formatCurrency(totalSpent)}</p>
                    </div>
                    <div className="p-2 bg-yellow-100 rounded-full">
                      <DollarSign className="w-4 h-4 text-yellow-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Último Mantenimiento</p>
                      <p className="text-sm font-bold">
                        {maintenanceHistory.length > 0
                          ? format(new Date(maintenanceHistory[0].date), 'dd/MM/yyyy', { locale: es })
                          : 'N/A'
                        }
                      </p>
                    </div>
                    <div className="p-2 bg-purple-100 rounded-full">
                      <Clock className="w-4 h-4 text-purple-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Maintenance History Tabs */}
            <Tabs defaultValue="all" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="all">Todos ({maintenanceHistory.length})</TabsTrigger>
                <TabsTrigger value="corrective">Correctivo ({correctiveHistory.length})</TabsTrigger>
                <TabsTrigger value="preventive">Preventivo ({preventiveHistory.length})</TabsTrigger>
              </TabsList>

              <TabsContent value="all" className="space-y-4">
                {maintenanceHistory.length > 0 ? (
                  maintenanceHistory.map((record) => (
                    <MaintenanceRecordCard key={record._id} record={record} />
                  ))
                ) : (
                  <div className="py-8 text-center">
                    <FileText className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                    <h3 className="mb-2 text-lg font-medium text-gray-900">Sin historial de mantenimiento</h3>
                    <p className="text-gray-500">Este vehículo no tiene registros de mantenimiento aún.</p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="corrective" className="space-y-4">
                {correctiveHistory.length > 0 ? (
                  correctiveHistory.map((record) => (
                    <MaintenanceRecordCard key={record._id} record={record} />
                  ))
                ) : (
                  <div className="py-8 text-center">
                    <Wrench className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                    <h3 className="mb-2 text-lg font-medium text-gray-900">Sin mantenimiento correctivo</h3>
                    <p className="text-gray-500">Este vehículo no tiene registros de mantenimiento correctivo.</p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="preventive" className="space-y-4">
                {preventiveHistory.length > 0 ? (
                  preventiveHistory.map((record) => (
                    <MaintenanceRecordCard key={record._id} record={record} />
                  ))
                ) : (
                  <div className="py-8 text-center">
                    <Calendar className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                    <h3 className="mb-2 text-lg font-medium text-gray-900">Sin mantenimiento preventivo</h3>
                    <p className="text-gray-500">Este vehículo no tiene registros de mantenimiento preventivo.</p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}

function MaintenanceRecordCard({ record }: { record: MaintenanceRecord }) {
  const [isExpanded, setIsExpanded] = useState(false);

  const getMaintenanceTypeIcon = (type: string) => {
    return type === 'preventive' ? <Calendar className="w-4 h-4" /> : <Wrench className="w-4 h-4" />;
  };

  const getMaintenanceTypeColor = (type: string) => {
    return type === 'preventive' ? 'bg-blue-100 text-blue-800' : 'bg-orange-100 text-orange-800';
  };

  const getMaintenanceTypeLabel = (type: string) => {
    return type === 'preventive' ? 'Preventivo' : 'Correctivo';
  };

  const getFailureTypeLabel = (failureType: string) => {
    const labels: Record<string, string> = {
      'known': 'Falla Conocida',
      'unknown': 'Falla Desconocida',
      'preventive': 'Preventivo'
    };
    return labels[failureType] || failureType;
  };

  const getArrivalMethodLabel = (method: string) => {
    const labels: Record<string, string> = {
      'driving': 'Conduciendo',
      'tow-truck': 'Grúa',
      'delivered': 'Entregado'
    };
    return labels[method] || method;
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Badge className={getMaintenanceTypeColor(record.type)} variant="secondary">
              {getMaintenanceTypeIcon(record.type)}
              <span className="ml-1">{getMaintenanceTypeLabel(record.type)}</span>
            </Badge>
            <Badge className={getStatusColor(record.status, 'order')} variant="secondary">
              {getStatusLabel(record.status, 'order')}
            </Badge>
            {record.customerSatisfactionRating && (
              <div className="flex items-center gap-1">
                <Star className="w-4 h-4 text-yellow-500 fill-current" />
                <span className="text-sm font-medium">{record.customerSatisfactionRating}/5</span>
              </div>
            )}
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-500">
              {format(new Date(record.date), 'dd/MM/yyyy HH:mm', { locale: es })}
            </p>
            <p className="text-sm font-medium">{record.workshop.name}</p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full mt-2"
        >
          {isExpanded ? (
            <>
              <ChevronUp className="w-4 h-4 mr-2" />
              Ocultar detalles
            </>
          ) : (
            <>
              <ChevronDown className="w-4 h-4 mr-2" />
              Ver detalles completos
            </>
          )}
        </Button>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Basic Information */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <h4 className="mb-2 font-medium">Información Básica</h4>
            <div className="space-y-2 text-sm">
              {record.failureType && (
                <div className="flex justify-between">
                  <span>Tipo de falla:</span>
                  <span className="font-medium">{getFailureTypeLabel(record.failureType)}</span>
                </div>
              )}
              {record.arrivalMethod && (
                <div className="flex justify-between">
                  <span>Método de llegada:</span>
                  <span className="font-medium">{getArrivalMethodLabel(record.arrivalMethod)}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span>Total de servicios:</span>
                <span className="font-medium">{record.services.length}</span>
              </div>
              <div className="flex justify-between">
                <span>Costo estimado:</span>
                <span className="font-bold">{formatCurrency(record.totalCost)}</span>
              </div>
              {record.totalActualCost && (
                <div className="flex justify-between">
                  <span>Costo real:</span>
                  <span className="font-bold text-green-600">{formatCurrency(record.totalActualCost)}</span>
                </div>
              )}
            </div>
          </div>

          <div>
            <h4 className="mb-2 font-medium">Contacto</h4>
            <div className="space-y-2 text-sm">
              {record.associate && (
                <>
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 text-gray-500" />
                    <span>{record.associate.name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4 text-gray-500" />
                    <span>{record.associate.email}</span>
                  </div>
                  {record.associate.phone && (
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4 text-gray-500" />
                      <span>{record.associate.phone}</span>
                    </div>
                  )}
                </>
              )}
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-gray-500" />
                <span>{record.workshop.name}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Services Summary */}
        <div>
          <h4 className="mb-2 font-medium">Servicios</h4>
          <div className="space-y-2">
            {record.services.map((service, index) => (
              <div key={service._id || index} className="flex items-center justify-between p-2 text-sm rounded bg-gray-50">
                <div className="flex-1">
                  <span className="font-medium">{service.serviceName}</span>
                  {service.description && (
                    <p className="mt-1 text-xs text-gray-600">{service.description}</p>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">{formatCurrency(service.estimatedCost)}</span>
                  {service.actualCost && service.actualCost !== service.estimatedCost && (
                    <span className="font-medium text-green-600">
                      → {formatCurrency(service.actualCost)}
                    </span>
                  )}
                  <Badge
                    className={getStatusColor(service.status, 'service')}
                    variant="secondary"
                  >
                    {service.status === 'completed' ? <CheckCircle className="w-3 h-3" /> : <Clock className="w-3 h-3" />}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Expanded Details */}
        {isExpanded && (
          <div className="pt-4 space-y-6 border-t">
            {/* Customer Description */}
            {record.customerDescription && (
              <div>
                <h4 className="flex items-center gap-2 mb-2 font-medium">
                  <MessageSquare className="w-4 h-4" />
                  Descripción del Cliente
                </h4>
                <p className="p-3 text-sm text-gray-600 rounded bg-blue-50">
                  {record.customerDescription}
                </p>
              </div>
            )}

            {/* Diagnosis */}
            {record.diagnosisNotes && (
              <div>
                <h4 className="flex items-center gap-2 mb-2 font-medium">
                  <FileText className="w-4 h-4" />
                  Diagnóstico
                </h4>
                <p className="p-3 text-sm text-gray-600 rounded bg-yellow-50">
                  {record.diagnosisNotes}
                </p>
              </div>
            )}

            {/* Quotation Details */}
            {record.quotation && (
              <QuotationDetails quotation={record.quotation} />
            )}

            {/* Service Details */}
            <div>
              <h4 className="mb-3 font-medium">Detalles de Servicios</h4>
              <div className="space-y-4">
                {record.services.map((service, index) => (
                  <ServiceDetails key={service._id || index} service={service} />
                ))}
              </div>
            </div>

            {/* Photos */}
            <PhotoGallery
              beforePhotos={record.beforePhotos}
              afterPhotos={record.afterPhotos}
              diagnosisPhotos={record.diagnosisPhotos}
              servicePhotos={record.services.flatMap(s => s.progressPhotos || [])}
            />

            {/* Completion Details */}
            {record.status === 'completed' && (
              <CompletionDetails record={record} />
            )}

            {/* Notes */}
            {record.notes && (
              <div>
                <h4 className="mb-2 font-medium">Notas Generales</h4>
                <p className="p-3 text-sm text-gray-600 rounded bg-gray-50">
                  {record.notes}
                </p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}



// Component for quotation details
function QuotationDetails({ quotation }: { quotation: any }) {
  return (
    <div>
      <h4 className="flex items-center gap-2 mb-2 font-medium">
        <DollarSign className="w-4 h-4" />
        Cotización
      </h4>
      <div className="p-4 space-y-3 rounded bg-green-50">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex justify-between">
            <span>Estado:</span>
            <Badge className={getStatusColor(quotation.status, 'quotation' as any)} variant="secondary">
              {getStatusLabel(quotation.status, 'quotation' as any)}
            </Badge>
          </div>
          <div className="flex justify-between">
            <span>Monto total:</span>
            <span className="font-bold">{formatCurrency(quotation.totalAmount)}</span>
          </div>
          <div className="flex justify-between">
            <span>Validez:</span>
            <span>{quotation.validityDays} días</span>
          </div>
          <div className="flex justify-between">
            <span>Tipo de aprobación:</span>
            <span className="capitalize">{quotation.approvalType}</span>
          </div>
        </div>

        {quotation.customerNotes && (
          <div>
            <p className="text-sm font-medium">Notas para el cliente:</p>
            <p className="text-sm text-gray-600">{quotation.customerNotes}</p>
          </div>
        )}

        {quotation.paymentTerms && (
          <div>
            <p className="text-sm font-medium">Términos de pago:</p>
            <p className="text-sm text-gray-600">{quotation.paymentTerms}</p>
          </div>
        )}

        {quotation.warrantyTerms && (
          <div>
            <p className="text-sm font-medium">Términos de garantía:</p>
            <p className="text-sm text-gray-600">{quotation.warrantyTerms}</p>
          </div>
        )}

        <div className="pt-2 text-xs text-gray-500 border-t">
          <p>Creada: {format(new Date(quotation.createdAt), 'dd/MM/yyyy HH:mm', { locale: es })}</p>
          {quotation.approvedAt && (
            <p>Aprobada: {format(new Date(quotation.approvedAt), 'dd/MM/yyyy HH:mm', { locale: es })}</p>
          )}
        </div>
      </div>
    </div>
  );
}

// Component for service details
function ServiceDetails({ service }: { service: any }) {
  const [showParts, setShowParts] = useState(false);

  return (
    <div className="p-3 space-y-3 border rounded">
      <div className="flex items-center justify-between">
        <div>
          <h5 className="font-medium">{service.serviceName}</h5>
          {service.description && (
            <p className="text-sm text-gray-600">{service.description}</p>
          )}
        </div>
        <Badge className={getStatusColor(service.status, 'service')} variant="secondary">
          {getStatusLabel(service.status, 'service')}
        </Badge>
      </div>

      <div className="grid grid-cols-2 gap-4 text-sm">
        <div className="space-y-1">
          <div className="flex justify-between">
            <span>Costo estimado:</span>
            <span className="font-medium">{formatCurrency(service.estimatedCost)}</span>
          </div>
          {service.actualCost && (
            <div className="flex justify-between">
              <span>Costo real:</span>
              <span className="font-medium text-green-600">{formatCurrency(service.actualCost)}</span>
            </div>
          )}
          {service.estimatedDuration && (
            <div className="flex justify-between">
              <span>Duración estimada:</span>
              <span>{formatDuration(service.estimatedDuration)}</span>
            </div>
          )}
        </div>

        <div className="space-y-1">
          {service.startedAt && (
            <div className="flex justify-between">
              <span>Iniciado:</span>
              <span>{format(new Date(service.startedAt), 'dd/MM HH:mm', { locale: es })}</span>
            </div>
          )}
          {service.completedAt && (
            <div className="flex justify-between">
              <span>Completado:</span>
              <span>{format(new Date(service.completedAt), 'dd/MM HH:mm', { locale: es })}</span>
            </div>
          )}
          {service.qualityCheckPassed !== undefined && (
            <div className="flex justify-between">
              <span>Control de calidad:</span>
              <span className={service.qualityCheckPassed ? 'text-green-600' : 'text-red-600'}>
                {service.qualityCheckPassed ? '✓ Aprobado' : '✗ Rechazado'}
              </span>
            </div>
          )}
        </div>
      </div>

      {service.parts && service.parts.length > 0 && (
        <div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowParts(!showParts)}
            className="h-auto p-0"
          >
            <Package className="w-4 h-4 mr-2" />
            Refacciones ({service.parts.length})
            {showParts ? <ChevronUp className="w-4 h-4 ml-1" /> : <ChevronDown className="w-4 h-4 ml-1" />}
          </Button>

          {showParts && (
            <div className="mt-2 space-y-2">
              {service.parts.map((part: any, index: number) => (
                <div key={part._id || index} className="p-2 text-sm rounded bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div>
                      <p className="font-medium">{part.name}</p>
                      {part.partNumber && (
                        <p className="text-gray-600">P/N: {part.partNumber}</p>
                      )}
                      {part.supplier && (
                        <p className="text-gray-600">Proveedor: {part.supplier}</p>
                      )}
                    </div>
                    <div className="text-right">
                      <p>Cant: {part.quantity}</p>
                      <p className="font-medium">{formatCurrency(part.unitCost * part.quantity)}</p>
                      <Badge
                        className={
                          part.availability === 'available' ? 'bg-green-100 text-green-800' :
                          part.availability === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }
                        variant="secondary"
                      >
                        {part.availability === 'available' ? 'Disponible' :
                         part.availability === 'pending' ? 'Pendiente' : 'No disponible'}
                      </Badge>
                    </div>
                  </div>
                  {part.eta && (
                    <p className="mt-1 text-xs text-gray-500">
                      ETA: {format(new Date(part.eta), 'dd/MM/yyyy', { locale: es })}
                    </p>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {service.notes && (
        <div>
          <p className="text-sm font-medium">Notas:</p>
          <p className="text-sm text-gray-600">{service.notes}</p>
        </div>
      )}

      {service.technicalNotes && (
        <div>
          <p className="text-sm font-medium">Notas técnicas:</p>
          <p className="text-sm text-gray-600">{service.technicalNotes}</p>
        </div>
      )}
    </div>
  );
}

// Component for photo gallery
function PhotoGallery({
  beforePhotos = [],
  afterPhotos = [],
  diagnosisPhotos = [],
  servicePhotos = []
}: {
  beforePhotos?: string[];
  afterPhotos?: string[];
  diagnosisPhotos?: string[];
  servicePhotos?: string[];
}) {
  const [selectedPhoto, setSelectedPhoto] = useState<string | null>(null);

  const allPhotos = [
    ...beforePhotos.map(url => ({ url, type: 'Antes' })),
    ...diagnosisPhotos.map(url => ({ url, type: 'Diagnóstico' })),
    ...servicePhotos.map(url => ({ url, type: 'Progreso' })),
    ...afterPhotos.map(url => ({ url, type: 'Después' })),
  ];

  if (allPhotos.length === 0) return null;

  return (
    <div>
      <h4 className="flex items-center gap-2 mb-3 font-medium">
        <Image className="w-4 h-4" />
        Evidencia Fotográfica ({allPhotos.length})
      </h4>

      <div className="grid grid-cols-2 gap-3 md:grid-cols-4">
        {allPhotos.map((photo, index) => (
          <div key={index} className="relative group">
            <img
              src={photo.url}
              alt={`${photo.type} ${index + 1}`}
              className="object-cover w-full h-24 transition-opacity rounded cursor-pointer hover:opacity-80"
              onClick={() => setSelectedPhoto(photo.url)}
            />
            <div className="absolute px-2 py-1 text-xs text-white bg-black rounded bottom-1 left-1 bg-opacity-70">
              {photo.type}
            </div>
            <div className="absolute transition-opacity opacity-0 top-1 right-1 group-hover:opacity-100">
              <Button
                size="sm"
                variant="secondary"
                className="w-6 h-6 p-0"
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedPhoto(photo.url);
                }}
              >
                <Eye className="w-3 h-3" />
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* Photo Modal */}
      {selectedPhoto && (
        <Dialog open={!!selectedPhoto} onOpenChange={() => setSelectedPhoto(null)}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>Evidencia Fotográfica</DialogTitle>
            </DialogHeader>
            <div className="flex justify-center">
              <img
                src={selectedPhoto}
                alt="Evidencia"
                className="max-w-full max-h-[70vh] object-contain"
              />
            </div>
            <div className="flex justify-center gap-2">
              <Button
                variant="outline"
                onClick={() => window.open(selectedPhoto, '_blank')}
              >
                <Download className="w-4 h-4 mr-2" />
                Descargar
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}

// Component for completion details
function CompletionDetails({ record }: { record: MaintenanceRecord }) {
  return (
    <div>
      <h4 className="flex items-center gap-2 mb-3 font-medium">
        <CheckCircle className="w-4 h-4" />
        Detalles de Finalización
      </h4>

      <div className="p-4 space-y-3 rounded bg-green-50">
        <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-2">
          {record.customerSatisfactionRating && (
            <div className="flex justify-between">
              <span>Satisfacción del cliente:</span>
              <div className="flex items-center gap-1">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-4 w-4 ${
                      i < record.customerSatisfactionRating!
                        ? 'text-yellow-500 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
                <span className="ml-1 font-medium">
                  {record.customerSatisfactionRating}/5
                </span>
              </div>
            </div>
          )}

          {record.workQualityRating && (
            <div className="flex justify-between">
              <span>Calidad del trabajo:</span>
              <div className="flex items-center gap-1">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-4 w-4 ${
                      i < record.workQualityRating!
                        ? 'text-yellow-500 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
                <span className="ml-1 font-medium">
                  {record.workQualityRating}/5
                </span>
              </div>
            </div>
          )}

          {record.finalInspectionPassed !== undefined && (
            <div className="flex justify-between">
              <span>Inspección final:</span>
              <span className={record.finalInspectionPassed ? 'text-green-600' : 'text-red-600'}>
                {record.finalInspectionPassed ? '✓ Aprobada' : '✗ Rechazada'}
              </span>
            </div>
          )}

          {record.totalActualCost && (
            <div className="flex justify-between">
              <span>Costo final:</span>
              <span className="font-bold">{formatCurrency(record.totalActualCost)}</span>
            </div>
          )}
        </div>

        {record.completionNotes && (
          <div>
            <p className="text-sm font-medium">Notas de finalización:</p>
            <p className="text-sm text-gray-600">{record.completionNotes}</p>
          </div>
        )}

        {record.recommendationsForFuture && (
          <div>
            <p className="text-sm font-medium">Recomendaciones futuras:</p>
            <p className="text-sm text-gray-600">{record.recommendationsForFuture}</p>
          </div>
        )}
      </div>
    </div>
  );
}
