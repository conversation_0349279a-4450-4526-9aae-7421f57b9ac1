'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import { 
  Settings, Play, Pause, CheckCircle, Clock, AlertTriangle, 
  Package, Camera, MessageSquare, TrendingUp, Users, 
  FileText, BarChart3, Target, Timer, Wrench
} from 'lucide-react';

// Import our new components
import EnhancedServiceExecutionModal from './EnhancedServiceExecutionModal';
import RealTimeServiceView from './RealTimeServiceView';
import StructuredEvidenceModal from './StructuredEvidenceModal';
import FleetQuotationSlackNotifier from './FleetQuotationSlackNotifier';
import InventoryCheckModal from './InventoryCheckModal';

interface ServiceMetrics {
  totalServices: number;
  completedToday: number;
  inProgress: number;
  averageCompletionTime: number;
  slaCompliance: number;
  customerSatisfaction: number;
  pendingQuotations: number;
  overdueQuotations: number;
}

interface TechnicianPerformance {
  id: string;
  name: string;
  servicesCompleted: number;
  averageTime: number;
  qualityScore: number;
  efficiency: number;
}

interface EnhancedServiceDashboardProps {
  initialServices: any[];
  initialQuotations: any[];
  currentUser: any;
  slackConfig: any;
}

export default function EnhancedServiceDashboard({
  initialServices,
  initialQuotations,
  currentUser,
  slackConfig: initialSlackConfig
}: EnhancedServiceDashboardProps) {
  const { toast } = useToast();
  const [services, setServices] = useState(initialServices);
  const [quotations, setQuotations] = useState(initialQuotations);
  const [slackConfig, setSlackConfig] = useState(initialSlackConfig);
  const [activeTab, setActiveTab] = useState('overview');
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);
  
  // Modal states
  const [selectedService, setSelectedService] = useState<any>(null);
  const [isExecutionModalOpen, setIsExecutionModalOpen] = useState(false);
  const [isEvidenceModalOpen, setIsEvidenceModalOpen] = useState(false);
  const [isInventoryModalOpen, setIsInventoryModalOpen] = useState(false);
  const [isRealTimeViewOpen, setIsRealTimeViewOpen] = useState(false);

  // Metrics calculation
  const [metrics, setMetrics] = useState<ServiceMetrics>({
    totalServices: 0,
    completedToday: 0,
    inProgress: 0,
    averageCompletionTime: 0,
    slaCompliance: 95,
    customerSatisfaction: 4.8,
    pendingQuotations: 0,
    overdueQuotations: 0,
  });

  const [technicianPerformance, setTechnicianPerformance] = useState<TechnicianPerformance[]>([]);

  // Calculate metrics from services data
  useEffect(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const completedToday = services.filter(service => {
      const completedAt = service.completedAt ? new Date(service.completedAt) : null;
      return completedAt && completedAt >= today;
    }).length;

    const inProgress = services.filter(service => service.status === 'in-progress').length;

    const pendingQuotations = quotations.filter(q => q.status === 'pending').length;
    const overdueQuotations = quotations.filter(q => {
      return q.status === 'pending' && q.dueDate && new Date(q.dueDate) < new Date();
    }).length;

    setMetrics({
      totalServices: services.length,
      completedToday,
      inProgress,
      averageCompletionTime: 120, // This would be calculated from actual data
      slaCompliance: 95,
      customerSatisfaction: 4.8,
      pendingQuotations,
      overdueQuotations,
    });
  }, [services, quotations]);

  // Auto-refresh data
  useEffect(() => {
    if (!isAutoRefresh) return;

    const interval = setInterval(() => {
      refreshData();
    }, refreshInterval * 1000);

    return () => clearInterval(interval);
  }, [refreshInterval, isAutoRefresh]);

  const refreshData = async () => {
    try {
      // In a real implementation, these would be API calls
      console.log('Refreshing dashboard data...');
      // setServices(await fetchServices());
      // setQuotations(await fetchQuotations());
    } catch (error) {
      console.error('Error refreshing data:', error);
    }
  };

  const handleServiceAction = async (serviceId: string, action: string) => {
    const service = services.find(s => s._id === serviceId);
    if (!service) return;

    setSelectedService(service);

    switch (action) {
      case 'start':
      case 'complete':
      case 'pause':
        setIsExecutionModalOpen(true);
        break;
      case 'evidence':
        setIsEvidenceModalOpen(true);
        break;
      case 'inventory':
        setIsInventoryModalOpen(true);
        break;
      case 'realtime':
        setIsRealTimeViewOpen(true);
        break;
      default:
        break;
    }
  };

  const handleQuotationUpdate = (quotationId: string, updates: any) => {
    setQuotations(prev => prev.map(q => 
      q.id === quotationId ? { ...q, ...updates } : q
    ));
  };

  const getServiceStatusColor = (status: string) => {
    switch (status) {
      case 'not-started': return 'bg-gray-100 text-gray-800';
      case 'in-progress': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'waiting-for-parts': return 'bg-yellow-100 text-yellow-800';
      case 'paused': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getServiceStatusIcon = (status: string) => {
    switch (status) {
      case 'not-started': return <Clock className="h-4 w-4" />;
      case 'in-progress': return <Play className="h-4 w-4" />;
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'waiting-for-parts': return <Package className="h-4 w-4" />;
      case 'paused': return <Pause className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN'
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header with controls */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Dashboard de Servicios Avanzado</h1>
          <p className="text-gray-600 mt-1">
            Gestión integral de servicios con seguimiento en tiempo real
          </p>
        </div>
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 text-sm">
            <div className={`w-2 h-2 rounded-full ${isAutoRefresh ? 'bg-green-500' : 'bg-gray-400'}`} />
            <span>Auto-actualización: {refreshInterval}s</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsAutoRefresh(!isAutoRefresh)}
          >
            {isAutoRefresh ? 'Pausar' : 'Activar'}
          </Button>
          <Button variant="outline" size="sm" onClick={refreshData}>
            Actualizar
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Servicios</p>
                <p className="text-2xl font-bold">{metrics.totalServices}</p>
              </div>
              <Wrench className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Completados Hoy</p>
                <p className="text-2xl font-bold text-green-600">{metrics.completedToday}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">En Progreso</p>
                <p className="text-2xl font-bold text-blue-600">{metrics.inProgress}</p>
              </div>
              <Play className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Tiempo Promedio</p>
                <p className="text-2xl font-bold">{metrics.averageCompletionTime}m</p>
              </div>
              <Timer className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">SLA</p>
                <p className="text-2xl font-bold text-green-600">{metrics.slaCompliance}%</p>
              </div>
              <Target className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Satisfacción</p>
                <p className="text-2xl font-bold text-yellow-600">{metrics.customerSatisfaction}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Cotizaciones</p>
                <p className="text-2xl font-bold text-orange-600">{metrics.pendingQuotations}</p>
              </div>
              <FileText className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Vencidas</p>
                <p className="text-2xl font-bold text-red-600">{metrics.overdueQuotations}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Dashboard Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Resumen</TabsTrigger>
          <TabsTrigger value="services">Servicios</TabsTrigger>
          <TabsTrigger value="quotations">Cotizaciones</TabsTrigger>
          <TabsTrigger value="performance">Rendimiento</TabsTrigger>
          <TabsTrigger value="settings">Configuración</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Services in Progress */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Play className="h-5 w-5" />
                Servicios en Progreso ({metrics.inProgress})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {services.filter(s => s.status === 'in-progress').length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Clock className="h-12 w-12 mx-auto mb-4" />
                  <p>No hay servicios en progreso</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {services
                    .filter(s => s.status === 'in-progress')
                    .slice(0, 5)
                    .map(service => (
                      <div key={service._id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex-1">
                          <div className="flex items-center gap-3">
                            <Badge className={getServiceStatusColor(service.status)}>
                              {getServiceStatusIcon(service.status)}
                              <span className="ml-1">En Progreso</span>
                            </Badge>
                            <h4 className="font-medium">{service.serviceName}</h4>
                          </div>
                          <div className="grid grid-cols-3 gap-4 mt-2 text-sm text-gray-600">
                            <span>Vehículo: {service.vehicle?.plate || 'N/A'}</span>
                            <span>Técnico: {service.technician?.name || 'N/A'}</span>
                            <span>Progreso: {service.progress || 0}%</span>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleServiceAction(service._id, 'realtime')}
                          >
                            Ver en Tiempo Real
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => handleServiceAction(service._id, 'complete')}
                          >
                            Gestionar
                          </Button>
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Próximos Servicios</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {services
                    .filter(s => s.status === 'not-started')
                    .slice(0, 3)
                    .map(service => (
                      <div key={service._id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm font-medium">{service.serviceName}</span>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleServiceAction(service._id, 'start')}
                        >
                          Iniciar
                        </Button>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Inventario Requerido</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {services
                    .filter(s => s.status === 'waiting-for-parts')
                    .slice(0, 3)
                    .map(service => (
                      <div key={service._id} className="flex items-center justify-between p-2 bg-yellow-50 rounded">
                        <span className="text-sm font-medium">{service.serviceName}</span>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleServiceAction(service._id, 'inventory')}
                        >
                          Verificar
                        </Button>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Evidencia Pendiente</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {services
                    .filter(s => s.evidenceRequired && !s.evidenceCompleted)
                    .slice(0, 3)
                    .map(service => (
                      <div key={service._id} className="flex items-center justify-between p-2 bg-blue-50 rounded">
                        <span className="text-sm font-medium">{service.serviceName}</span>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleServiceAction(service._id, 'evidence')}
                        >
                          Subir
                        </Button>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="services" className="space-y-4">
          <div className="grid gap-4">
            {services.map(service => (
              <Card key={service._id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3">
                        <h3 className="text-lg font-medium">{service.serviceName}</h3>
                        <Badge className={getServiceStatusColor(service.status)}>
                          {getServiceStatusIcon(service.status)}
                          <span className="ml-1 capitalize">{service.status}</span>
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">Vehículo:</p>
                          <p className="font-medium">{service.vehicle?.plate || 'N/A'}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Técnico:</p>
                          <p className="font-medium">{service.technician?.name || 'N/A'}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Costo:</p>
                          <p className="font-medium">{formatCurrency(service.estimatedCost || 0)}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Progreso:</p>
                          <p className="font-medium">{service.progress || 0}%</p>
                        </div>
                      </div>

                      {service.progress > 0 && (
                        <div className="mt-3">
                          <Progress value={service.progress || 0} className="h-2" />
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col gap-2 ml-4">
                      {service.status === 'not-started' && (
                        <Button
                          size="sm"
                          onClick={() => handleServiceAction(service._id, 'start')}
                        >
                          Iniciar
                        </Button>
                      )}
                      
                      {service.status === 'in-progress' && (
                        <>
                          <Button
                            size="sm"
                            onClick={() => handleServiceAction(service._id, 'complete')}
                          >
                            Gestionar
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleServiceAction(service._id, 'realtime')}
                          >
                            Tiempo Real
                          </Button>
                        </>
                      )}

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleServiceAction(service._id, 'evidence')}
                      >
                        <Camera className="h-4 w-4 mr-1" />
                        Evidencia
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="quotations">
          <FleetQuotationSlackNotifier
            quotations={quotations}
            slackConfig={slackConfig}
            onConfigUpdate={setSlackConfig}
            onQuotationUpdate={handleQuotationUpdate}
          />
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Rendimiento de Técnicos</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {technicianPerformance.map(tech => (
                    <div key={tech.id} className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <p className="font-medium">{tech.name}</p>
                        <p className="text-sm text-gray-600">
                          {tech.servicesCompleted} servicios completados
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">{tech.efficiency}% eficiencia</p>
                        <p className="text-sm text-gray-600">{tech.averageTime}m promedio</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Métricas de Calidad</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Cumplimiento SLA</span>
                    <Badge variant="default">{metrics.slaCompliance}%</Badge>
                  </div>
                  <Progress value={metrics.slaCompliance} className="h-2" />
                  
                  <div className="flex items-center justify-between">
                    <span>Satisfacción del Cliente</span>
                    <Badge variant="default">{metrics.customerSatisfaction}/5.0</Badge>
                  </div>
                  <Progress value={(metrics.customerSatisfaction / 5) * 100} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Configuración del Dashboard
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Intervalo de Auto-actualización
                </label>
                <div className="flex gap-2">
                  {[15, 30, 60, 120].map(seconds => (
                    <Button
                      key={seconds}
                      variant={refreshInterval === seconds ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setRefreshInterval(seconds)}
                    >
                      {seconds}s
                    </Button>
                  ))}
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Button
                  variant={isAutoRefresh ? 'default' : 'outline'}
                  onClick={() => setIsAutoRefresh(!isAutoRefresh)}
                >
                  {isAutoRefresh ? 'Desactivar' : 'Activar'} Auto-actualización
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Modals */}
      {selectedService && (
        <>
          <EnhancedServiceExecutionModal
            isOpen={isExecutionModalOpen}
            onClose={() => setIsExecutionModalOpen(false)}
            onSuccess={() => {
              setIsExecutionModalOpen(false);
              refreshData();
            }}
            service={selectedService}
            orderId={selectedService.orderId}
          />

          <StructuredEvidenceModal
            isOpen={isEvidenceModalOpen}
            onClose={() => setIsEvidenceModalOpen(false)}
            onSubmit={async (evidence) => {
              // Handle evidence submission
              console.log('Evidence submitted:', evidence);
              return true;
            }}
            serviceId={selectedService._id}
            orderId={selectedService.orderId}
            serviceName={selectedService.serviceName}
            phases={selectedService.phases || []}
            currentPhase={selectedService.currentPhase || 'preparation'}
          />

          <InventoryCheckModal
            isOpen={isInventoryModalOpen}
            onClose={() => setIsInventoryModalOpen(false)}
            parts={selectedService.parts || []}
            vehicleInfo={selectedService.vehicle}
            onProceed={(canProceed, result) => {
              console.log('Inventory check result:', result);
              setIsInventoryModalOpen(false);
            }}
          />

          {isRealTimeViewOpen && (
            <div className="fixed inset-0 bg-black bg-opacity-50 z-50 overflow-y-auto">
              <div className="min-h-screen py-4">
                <div className="bg-white rounded-lg mx-4">
                  <div className="flex items-center justify-between p-4 border-b">
                    <h2 className="text-xl font-semibold">Vista en Tiempo Real</h2>
                    <Button
                      variant="ghost"
                      onClick={() => setIsRealTimeViewOpen(false)}
                    >
                      ✕
                    </Button>
                  </div>
                  <RealTimeServiceView
                    serviceId={selectedService._id}
                    orderId={selectedService.orderId}
                    serviceName={selectedService.serviceName}
                    description={selectedService.description}
                    status={selectedService.status}
                    estimatedCost={selectedService.estimatedCost}
                    startedAt={selectedService.startedAt}
                    estimatedCompletionTime={selectedService.estimatedCompletionTime}
                    phases={selectedService.phases || []}
                    updates={selectedService.updates || []}
                    vehicle={selectedService.vehicle || {}}
                    technician={selectedService.technician || {}}
                    workshop={{
                      name: 'Taller Principal',
                      address: 'Av. Principal 123',
                      phone: '+52 ************'
                    }}
                  />
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}