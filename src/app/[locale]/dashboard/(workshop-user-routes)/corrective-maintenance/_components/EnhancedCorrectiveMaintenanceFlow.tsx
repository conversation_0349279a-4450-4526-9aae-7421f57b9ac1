'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Package, 
  Clock, 
  Play, 
  Pause, 
  CheckCircle, 
  AlertTriangle, 
  FileText,
  Activity,
  Settings,
  Eye
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import InventoryCheckModal from './InventoryCheckModal';
import RealTimeTrackingDashboard from './RealTimeTrackingDashboard';
import EnhancedServiceEvidenceModal from './EnhancedServiceEvidenceModal';
import { useInventory } from '@/hooks/useInventory';
import { useRealTimeTracking } from '@/hooks/useRealTimeTracking';

interface Service {
  _id: string;
  serviceName: string;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  parts: Array<{
    name: string;
    partNumber?: string;
    quantity: number;
    unitCost: number;
    totalCost: number;
    supplier: string;
    isAvailable?: boolean;
    needsOrdering?: boolean;
  }>;
  estimatedDuration: number;
  estimatedCost: number;
  currentPhase?: string;
  isPaused?: boolean;
  pauseReason?: string;
}

interface Order {
  _id: string;
  orderNumber: string;
  status: string;
  services: Service[];
  stockVehicle?: {
    brand: string;
    model: string;
    year: string;
    carNumber: string;
    plates: string;
  };
  associate?: {
    name: string;
    email: string;
  };
}

interface EnhancedCorrectiveMaintenanceFlowProps {
  order: Order;
  onOrderUpdate?: () => void;
}

export default function EnhancedCorrectiveMaintenanceFlow({
  order,
  onOrderUpdate,
}: EnhancedCorrectiveMaintenanceFlowProps) {
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [showInventoryCheck, setShowInventoryCheck] = useState(false);
  const [showEvidenceModal, setShowEvidenceModal] = useState(false);
  const [evidenceModalType, setEvidenceModalType] = useState<'phase' | 'completion'>('phase');
  const [activeTab, setActiveTab] = useState('overview');

  const { toast } = useToast();
  const { checkPartsAvailability } = useInventory();
  const { startServiceTracking, pauseService, resumeService } = useRealTimeTracking();

  const vehicleInfo = order.stockVehicle ? {
    brand: order.stockVehicle.brand,
    model: order.stockVehicle.model,
    year: parseInt(order.stockVehicle.year),
  } : undefined;

  const handleStartService = async (service: Service) => {
    setSelectedService(service);
    
    // Check if service has parts that need inventory verification
    if (service.parts && service.parts.length > 0) {
      setShowInventoryCheck(true);
    } else {
      // Start service directly if no parts
      await startServiceDirectly(service);
    }
  };

  const handleInventoryCheckResult = async (canProceed: boolean, inventoryResult: any) => {
    if (!selectedService) return;

    if (canProceed) {
      await startServiceDirectly(selectedService);
    } else {
      toast({
        title: 'Inventario Insuficiente',
        description: 'No se puede iniciar el servicio debido a falta de inventario. Gestiona el inventario primero.',
        variant: 'destructive',
      });
    }
  };

  const startServiceDirectly = async (service: Service) => {
    const success = await startServiceTracking(service._id);
    if (success) {
      toast({
        title: 'Servicio Iniciado',
        description: `El servicio "${service.serviceName}" ha sido iniciado correctamente`,
      });
      onOrderUpdate?.();
    }
  };

  const handlePauseService = async (service: Service) => {
    const reason = prompt('Motivo de la pausa:');
    if (reason) {
      const success = await pauseService(service._id, reason);
      if (success) {
        onOrderUpdate?.();
      }
    }
  };

  const handleResumeService = async (service: Service) => {
    const success = await resumeService(service._id);
    if (success) {
      onOrderUpdate?.();
    }
  };

  const handleUpdatePhase = (service: Service) => {
    setSelectedService(service);
    setEvidenceModalType('phase');
    setShowEvidenceModal(true);
  };

  const handleCompleteService = (service: Service) => {
    setSelectedService(service);
    setEvidenceModalType('completion');
    setShowEvidenceModal(true);
  };

  const getServiceStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getServiceStatusIcon = (service: Service) => {
    if (service.isPaused) {
      return <Pause className="h-4 w-4 text-yellow-500" />;
    }
    
    switch (service.status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'in-progress':
        return <Play className="h-4 w-4 text-blue-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-gray-500" />;
      case 'cancelled':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getServiceActions = (service: Service) => {
    const actions = [];

    if (service.status === 'pending') {
      actions.push(
        <Button
          key="start"
          size="sm"
          onClick={() => handleStartService(service)}
          className="bg-green-600 hover:bg-green-700"
        >
          <Play className="h-4 w-4 mr-1" />
          Iniciar
        </Button>
      );
    }

    if (service.status === 'in-progress') {
      if (service.isPaused) {
        actions.push(
          <Button
            key="resume"
            size="sm"
            onClick={() => handleResumeService(service)}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Play className="h-4 w-4 mr-1" />
            Reanudar
          </Button>
        );
      } else {
        actions.push(
          <Button
            key="pause"
            size="sm"
            variant="outline"
            onClick={() => handlePauseService(service)}
          >
            <Pause className="h-4 w-4 mr-1" />
            Pausar
          </Button>
        );
      }

      actions.push(
        <Button
          key="update-phase"
          size="sm"
          variant="outline"
          onClick={() => handleUpdatePhase(service)}
        >
          <FileText className="h-4 w-4 mr-1" />
          Actualizar Fase
        </Button>
      );

      actions.push(
        <Button
          key="complete"
          size="sm"
          onClick={() => handleCompleteService(service)}
          className="bg-green-600 hover:bg-green-700"
        >
          <CheckCircle className="h-4 w-4 mr-1" />
          Completar
        </Button>
      );
    }

    return actions;
  };

  return (
    <div className="space-y-6">
      {/* Order Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Orden {order.orderNumber}</CardTitle>
              <p className="text-sm text-gray-600">
                {order.stockVehicle && (
                  `${order.stockVehicle.brand} ${order.stockVehicle.model} ${order.stockVehicle.year} - ${order.stockVehicle.carNumber}`
                )}
              </p>
            </div>
            <Badge className={getServiceStatusColor(order.status)}>
              {order.status}
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Resumen</TabsTrigger>
          <TabsTrigger value="tracking">Seguimiento</TabsTrigger>
          <TabsTrigger value="inventory">Inventario</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Services List */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Servicios</h3>
            {order.services.map((service) => (
              <Card key={service._id}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getServiceStatusIcon(service)}
                      <div>
                        <CardTitle className="text-base">{service.serviceName}</CardTitle>
                        <p className="text-sm text-gray-600">
                          {service.currentPhase && `Fase: ${service.currentPhase}`}
                          {service.isPaused && service.pauseReason && ` (Pausado: ${service.pauseReason})`}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getServiceStatusColor(service.status)}>
                        {service.isPaused ? 'Pausado' : service.status}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-600">
                      <p>Duración estimada: {service.estimatedDuration}h</p>
                      <p>Costo estimado: ${service.estimatedCost.toLocaleString()}</p>
                      {service.parts && service.parts.length > 0 && (
                        <p>Partes requeridas: {service.parts.length}</p>
                      )}
                    </div>
                    
                    <div className="flex gap-2">
                      {getServiceActions(service)}
                    </div>
                  </div>

                  {/* Parts Status */}
                  {service.parts && service.parts.length > 0 && (
                    <div className="mt-3 pt-3 border-t">
                      <div className="flex items-center gap-2 text-sm">
                        <Package className="h-4 w-4" />
                        <span>Estado de partes:</span>
                        {service.parts.some(p => p.needsOrdering) && (
                          <Badge variant="destructive" className="text-xs">
                            Requiere pedido
                          </Badge>
                        )}
                        {service.parts.every(p => p.isAvailable) && (
                          <Badge variant="default" className="text-xs bg-green-100 text-green-800">
                            Disponibles
                          </Badge>
                        )}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="tracking">
          <RealTimeTrackingDashboard orderId={order._id} />
        </TabsContent>

        <TabsContent value="inventory">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Gestión de Inventario
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                Aquí puedes gestionar el inventario y verificar la disponibilidad de partes.
              </p>
              {/* TODO: Add inventory management interface */}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Modals */}
      {showInventoryCheck && selectedService && (
        <InventoryCheckModal
          isOpen={showInventoryCheck}
          onClose={() => setShowInventoryCheck(false)}
          parts={selectedService.parts}
          vehicleInfo={vehicleInfo}
          onProceed={handleInventoryCheckResult}
        />
      )}

      {showEvidenceModal && selectedService && (
        <EnhancedServiceEvidenceModal
          isOpen={showEvidenceModal}
          onClose={() => setShowEvidenceModal(false)}
          serviceId={selectedService._id}
          serviceName={selectedService.serviceName}
          currentPhase={selectedService.currentPhase || 'unknown'}
          isCompletion={evidenceModalType === 'completion'}
          onSuccess={() => {
            onOrderUpdate?.();
            setShowEvidenceModal(false);
          }}
        />
      )}
    </div>
  );
}
