'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Loader2, Car, User, AlertTriangle, Search, CheckCircle,
  Calendar, MapPin, Phone, Mail, Wrench
} from 'lucide-react';
import { createOrder } from '../_actions/createOrder';
import { getWorkshops } from '../_actions/getWorkshops';
import { getVehicleByVin } from '@/vendor-platform/modules/vehicles/getVehicleByVin';
import { getVehicleByPlate } from '@/vendor-platform/modules/vehicles/getVehicleByPlate';

// Ultra-simplified schema - VIN or Plates and service details
const createOrderSchema = z.object({
  searchType: z.enum(['vin', 'plates']),
  searchValue: z.string().min(1, 'Ingresa el VIN o las placas del vehículo'),
  workshopId: z.string().min(1, 'Selecciona un taller'),
  failureType: z.enum(['known', 'unknown']),
  arrivalMethod: z.enum(['driving', 'tow-truck']),
  customerDescription: z.string().min(10, 'La descripción debe tener al menos 10 caracteres'),
  urgency: z.enum(['low', 'normal', 'high', 'urgent']),
}).refine((data) => {
  // Validate VIN length if searching by VIN
  if (data.searchType === 'vin') {
    return data.searchValue.length === 17;
  }
  // Validate plates format (at least 3 characters)
  if (data.searchType === 'plates') {
    return data.searchValue.length >= 3;
  }
  return true;
}, {
  message: 'VIN debe tener 17 caracteres o placas al menos 3 caracteres',
  path: ['searchValue'],
});

type CreateOrderFormData = z.infer<typeof createOrderSchema>;

interface CreateOrderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface Workshop {
  _id: string;
  name: string;
  location?: string;
}

interface VehicleData {
  _id: string;
  brand: string;
  model: string;
  year: number;
  carNumber: string;
  carPlates: {
    plates: string;
  };
  vin?: string;
  color?: string;
  status: string;
  drivers?: Array<{
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    curp?: string;
    rfc?: string;
  }>;
  organization?: {
    _id: string;
    name: string;
  };
}

export default function CreateOrderModal({ isOpen, onClose, onSuccess }: CreateOrderModalProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [workshops, setWorkshops] = useState<Workshop[]>([]);
  const [loadingData, setLoadingData] = useState(false);
  const [vehicleData, setVehicleData] = useState<VehicleData | null>(null);
  const [searchingVehicle, setSearchingVehicle] = useState(false);
  const [vehicleSearched, setVehicleSearched] = useState(false);

  const form = useForm<CreateOrderFormData>({
    resolver: zodResolver(createOrderSchema),
    defaultValues: {
      searchType: 'vin',
      searchValue: '',
      failureType: 'unknown',
      arrivalMethod: 'driving',
      urgency: 'normal',
      customerDescription: '',
    },
  });

  // Load workshops when modal opens
  useEffect(() => {
    if (isOpen) {
      loadWorkshops();
      resetForm();
    }
  }, [isOpen]);

  const resetForm = () => {
    form.reset();
    setVehicleData(null);
    setVehicleSearched(false);
  };

  const loadWorkshops = async () => {
    setLoadingData(true);
    try {
      const workshopsResponse = await getWorkshops();
      if (workshopsResponse?.success) {
        setWorkshops(workshopsResponse.data);
      }
    } catch (error) {
      console.error('Error loading workshops:', error);
      toast({
        title: 'Error',
        description: 'Error al cargar los talleres',
        variant: 'destructive',
      });
    } finally {
      setLoadingData(false);
    }
  };

  const searchVehicle = async () => {
    const { searchType, searchValue } = form.getValues();

    if (!searchValue) {
      toast({
        title: 'Campo Requerido',
        description: 'Ingresa el VIN del vehículo',
        variant: 'destructive',
      });
      return;
    }

    // Validate VIN format
    if (searchValue.length !== 17) {
      toast({
        title: 'VIN Inválido',
        description: 'El VIN debe tener exactamente 17 caracteres',
        variant: 'destructive',
      });
      return;
    }

    setSearchingVehicle(true);
    setVehicleSearched(true);
    try {
      // Always search by VIN now
      const response = await getVehicleByVin(searchValue);

      console.log('🔍 Modal received response:', response);

      if (response?.success && response.data) {
        setVehicleData(response.data);
        toast({
          title: 'Vehículo Encontrado',
          description: `${response.data.brand} ${response.data.model} - ${response.data.carPlates.plates}`,
        });
      } else {
        setVehicleData(null);
        toast({
          title: 'Vehículo No Encontrado',
          description: response?.message || 'No se encontró un vehículo con este VIN',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error searching vehicle:', error);
      setVehicleData(null);
      toast({
        title: 'Error',
        description: 'Error al buscar el vehículo',
        variant: 'destructive',
      });
    } finally {
      setSearchingVehicle(false);
    }
  };

  const onSubmit = async (data: CreateOrderFormData) => {
    if (!vehicleData) {
      toast({
        title: 'Error',
        description: 'Primero debes buscar y encontrar un vehículo válido',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Use the real vehicle data from the backend
      const associateId = vehicleData.drivers?.[vehicleData.drivers.length - 1]?._id;
      
      if (!associateId) {
        setIsSubmitting(false);
        toast({
          title: 'Vehículo Sin Conductor',
          description: 'Este vehículo no tiene conductores asignados. Asigna un conductor antes de crear la orden de mantenimiento.',
          variant: 'destructive',
        });
        return;
      }
      
      console.log('🔍 Order data debug:', {
        stockId: vehicleData._id,
        associateId: associateId,
        workshopId: data.workshopId,
        type: 'customer-initiated',
        failureType: data.failureType,
        arrivalMethod: data.arrivalMethod,
        vehicleHasDrivers: vehicleData.drivers?.length || 0,
        allDrivers: vehicleData.drivers?.map(d => ({ id: d._id, name: `${d.firstName} ${d.lastName}` }))
      });

      const orderData = {
        stockId: vehicleData._id,
        associateId: associateId,
        workshopId: data.workshopId,
        type: 'customer-initiated' as const,
        failureType: data.failureType,
        arrivalMethod: data.arrivalMethod,
        customerDescription: data.customerDescription,
        canVehicleDrive: data.arrivalMethod === 'driving',
        needsTowTruck: data.arrivalMethod === 'tow-truck',
        approvalType: 'customer' as const,
        // Add metadata for tracking
        metadata: {
          urgency: data.urgency,
          createdVia: `${data.searchType}-search-modal`,
          searchedValue: data.searchValue,
          searchType: data.searchType,
        },
      };

      const response = await createOrder(orderData);

      if (response.success) {
        toast({
          title: 'Éxito',
          description: 'Orden de mantenimiento correctivo creada exitosamente',
        });
        form.reset();
        onSuccess();
        onClose();
        // Refresh the page to show the new order
        window.location.reload();
      } else {
        // Handle specific error for vehicle already having active corrective maintenance
        const specificError = (response.data as any)?.error || '';
        const errorMessage = response.message || '';

        const isVehicleAlreadyActive = specificError === 'Vehicle already has an active corrective maintenance order' ||
                                      specificError.includes('Vehicle already has an active corrective maintenance order') ||
                                      errorMessage.includes('Vehicle already has an active corrective maintenance order');

        if (isVehicleAlreadyActive) {
          onClose();
          setTimeout(() => {
            toast({
              title: 'Vehículo con Orden Activa',
              description: 'Este vehículo ya tiene una orden de mantenimiento correctivo activa. Complete o cancele la orden existente antes de crear una nueva.',
              variant: 'destructive',
            });
          }, 100);
        } else {
          onClose();
          setTimeout(() => {
            toast({
              title: 'Error',
              description: response.message || 'Error al crear la orden',
              variant: 'destructive',
            });
          }, 100);
        }
      }
    } catch (error: any) {
      console.error('Error creating order:', error);
      onClose();
      setTimeout(() => {
        toast({
          title: 'Error',
          description: 'Error inesperado al crear la orden',
          variant: 'destructive',
        });
      }, 100);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const formatAssociateName = (associate: any) => {
    return `${associate.firstName || ''} ${associate.lastName || ''}`.trim() || 'Sin nombre';
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Nueva Orden de Mantenimiento Correctivo</DialogTitle>
          <p className="text-sm text-gray-600">
            Busca el vehículo por VIN para crear una orden rápidamente
          </p>
        </DialogHeader>

        {loadingData ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Cargando datos...</span>
          </div>
        ) : (
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* VIN Search Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Search className="h-5 w-5" />
                  Buscar Vehículo
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">

                <div className="flex gap-3">
                  <div className="flex-1 space-y-2">
                    <Label htmlFor="searchValue">VIN del Vehículo *</Label>
                    <Input
                      {...form.register('searchValue')}
                      placeholder="Ingresa el VIN de 17 caracteres"
                      style={{ textTransform: 'uppercase' }}
                      maxLength={17}
                    />
                    {form.formState.errors.searchValue && (
                      <p className="text-sm text-red-500">{form.formState.errors.searchValue.message}</p>
                    )}
                  </div>
                  <div className="flex items-end">
                    <Button
                      type="button"
                      onClick={searchVehicle}
                      disabled={searchingVehicle || !form.watch('searchValue')}
                    >
                      {searchingVehicle ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : (
                        <Search className="h-4 w-4 mr-2" />
                      )}
                      Buscar
                    </Button>
                  </div>
                </div>

                {/* Vehicle Found Display */}
                {vehicleData && (
                  <Card className="bg-green-50 border-green-200">
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-2 text-green-700">
                        <CheckCircle className="h-5 w-5" />
                        Vehículo Encontrado
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="flex items-center gap-2">
                          <Car className="h-4 w-4 text-blue-600" />
                          <div>
                            <p className="font-medium">{vehicleData.brand} {vehicleData.model}</p>
                            <p className="text-sm text-gray-600">Año {vehicleData.year}</p>
                          </div>
                        </div>

                        <div>
                          <p className="text-sm text-gray-600">Placas</p>
                          <p className="font-medium">{vehicleData.carPlates.plates}</p>
                        </div>

                        <div>
                          <p className="text-sm text-gray-600">Estado</p>
                          <Badge variant="outline" className="capitalize">
                            {vehicleData.status}
                          </Badge>
                        </div>
                      </div>

                      {vehicleData.color && (
                        <div>
                          <p className="text-sm text-gray-600">Color</p>
                          <p className="font-medium capitalize">{vehicleData.color}</p>
                        </div>
                      )}

                      {vehicleData.organization && (
                        <div>
                          <p className="text-sm text-gray-600">Organización</p>
                          <p className="font-medium">{vehicleData.organization.name}</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}

                {/* Vehicle Not Found Display */}
                {vehicleSearched && !vehicleData && !searchingVehicle && (
                  <Card className="bg-red-50 border-red-200">
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2 text-red-700">
                        <AlertTriangle className="h-5 w-5" />
                        <p className="font-medium">Vehículo no encontrado</p>
                      </div>
                      <p className="text-sm text-red-600 mt-1">
                        No se encontró un vehículo con el VIN ingresado.
                        Verifica que el VIN sea correcto.
                      </p>
                    </CardContent>
                  </Card>
                )}
              </CardContent>
            </Card>

            {/* Service Details - Only show if vehicle is found */}
            {vehicleData && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Wrench className="h-5 w-5" />
                    Detalles del Servicio
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Workshop Selection */}
                    <div className="space-y-2">
                      <Label htmlFor="workshopId">Taller *</Label>
                      <Select
                        value={form.watch('workshopId') || undefined}
                        onValueChange={(value) => form.setValue('workshopId', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Selecciona un taller" />
                        </SelectTrigger>
                        <SelectContent>
                          {workshops.map((workshop) => (
                            <SelectItem key={workshop._id} value={workshop._id}>
                              <div className="flex items-center gap-2">
                                <MapPin className="h-4 w-4" />
                                {workshop.name} {workshop.location ? `- ${workshop.location}` : ''}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {form.formState.errors.workshopId && (
                        <p className="text-sm text-red-500">{form.formState.errors.workshopId.message}</p>
                      )}
                    </div>

                    {/* Urgency */}
                    <div className="space-y-2">
                      <Label htmlFor="urgency">Urgencia *</Label>
                      <Select
                        value={form.watch('urgency')}
                        onValueChange={(value: 'low' | 'normal' | 'high' | 'urgent') =>
                          form.setValue('urgency', value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">🟢 Baja</SelectItem>
                          <SelectItem value="normal">🔵 Normal</SelectItem>
                          <SelectItem value="high">🟡 Alta</SelectItem>
                          <SelectItem value="urgent">🔴 Urgente</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Failure Type */}
                    <div className="space-y-2">
                      <Label htmlFor="failureType">Tipo de Falla *</Label>
                      <Select
                        value={form.watch('failureType')}
                        onValueChange={(value: 'known' | 'unknown') => form.setValue('failureType', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="known">Falla Conocida</SelectItem>
                          <SelectItem value="unknown">Requiere Diagnóstico</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Arrival Method */}
                    <div className="space-y-2">
                      <Label htmlFor="arrivalMethod">Método de Llegada *</Label>
                      <Select
                        value={form.watch('arrivalMethod')}
                        onValueChange={(value: 'driving' | 'tow-truck') =>
                          form.setValue('arrivalMethod', value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="driving">🚗 Llega Rodando</SelectItem>
                          <SelectItem value="tow-truck">🚛 Requiere Grúa</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Customer Description */}
                  <div className="space-y-2">
                    <Label htmlFor="customerDescription">Descripción del Problema *</Label>
                    <Textarea
                      {...form.register('customerDescription')}
                      placeholder="Describe detalladamente el problema reportado por el cliente..."
                      rows={4}
                    />
                    {form.formState.errors.customerDescription && (
                      <p className="text-sm text-red-500">{form.formState.errors.customerDescription.message}</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancelar
              </Button>

              <Button
                type="submit"
                disabled={isSubmitting || !vehicleData}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Crear Orden
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}