'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Upload, Camera, Video, FileText, CheckCircle, AlertTriangle, 
  X, Image as ImageIcon, Loader2, Eye, Download, MessageSquare 
} from 'lucide-react';

const evidenceSchema = z.object({
  phase: z.string(),
  evidenceType: z.enum(['photo', 'video', 'document']),
  files: z.array(z.instanceof(File)).min(1, 'Se requiere al menos un archivo'),
  description: z.string().min(10, 'La descripción debe tener al menos 10 caracteres'),
  isRequired: z.boolean().default(false),
  qualityCheck: z.object({
    isVisible: z.boolean(),
    isRelevant: z.boolean(),
    hasClearDetails: z.boolean(),
  }).optional(),
});

type EvidenceFormData = z.infer<typeof evidenceSchema>;

interface EvidenceRequirement {
  id: string;
  phase: string;
  type: 'photo' | 'video' | 'document';
  title: string;
  description: string;
  required: boolean;
  minFiles?: number;
  maxFiles?: number;
  acceptedFormats?: string[];
  examples?: string[];
  status: 'pending' | 'uploaded' | 'approved' | 'rejected';
  uploadedFiles?: UploadedFile[];
  rejectionReason?: string;
}

interface UploadedFile {
  id: string;
  name: string;
  url: string;
  type: string;
  size: number;
  uploadedAt: string;
  description?: string;
  approved?: boolean;
  rejectionReason?: string;
}

interface ServicePhase {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed';
}

interface StructuredEvidenceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (evidence: EvidenceFormData) => Promise<boolean>;
  serviceId: string;
  orderId: string;
  serviceName: string;
  phases: ServicePhase[];
  currentPhase: string;
}

export default function StructuredEvidenceModal({
  isOpen,
  onClose,
  onSubmit,
  serviceId,
  orderId,
  serviceName,
  phases,
  currentPhase
}: StructuredEvidenceModalProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [previewFiles, setPreviewFiles] = useState<{file: File, url: string}[]>([]);
  const [activeTab, setActiveTab] = useState('upload');
  const [selectedRequirement, setSelectedRequirement] = useState<string>('');

  // Evidence requirements based on service phases
  const [evidenceRequirements, setEvidenceRequirements] = useState<EvidenceRequirement[]>([
    {
      id: 'initial_inspection',
      phase: 'preparation',
      type: 'photo',
      title: 'Inspección Inicial del Vehículo',
      description: 'Fotos generales del estado del vehículo antes de iniciar el trabajo',
      required: true,
      minFiles: 3,
      maxFiles: 8,
      acceptedFormats: ['image/jpeg', 'image/png'],
      examples: ['Vista frontal', 'Vista trasera', 'Interior', 'Área de trabajo específica'],
      status: 'pending'
    },
    {
      id: 'problem_documentation',
      phase: 'diagnosis',
      type: 'photo',
      title: 'Documentación del Problema',
      description: 'Evidencia fotográfica del problema específico a resolver',
      required: true,
      minFiles: 2,
      maxFiles: 6,
      acceptedFormats: ['image/jpeg', 'image/png'],
      examples: ['Componente dañado', 'Detalles del problema', 'Mediciones si aplica'],
      status: 'pending'
    },
    {
      id: 'diagnostic_results',
      phase: 'diagnosis',
      type: 'document',
      title: 'Resultados de Diagnóstico',
      description: 'Reporte detallado del diagnóstico realizado',
      required: true,
      minFiles: 1,
      maxFiles: 3,
      acceptedFormats: ['application/pdf', 'image/jpeg', 'image/png'],
      examples: ['Reporte de escáner', 'Mediciones', 'Códigos de error'],
      status: 'pending'
    },
    {
      id: 'parts_verification',
      phase: 'parts_verification',
      type: 'photo',
      title: 'Verificación de Refacciones',
      description: 'Fotos de las refacciones antes de la instalación',
      required: false,
      minFiles: 1,
      maxFiles: 4,
      acceptedFormats: ['image/jpeg', 'image/png'],
      examples: ['Refacciones nuevas', 'Números de parte', 'Comparación con piezas originales'],
      status: 'pending'
    },
    {
      id: 'work_progress',
      phase: 'execution',
      type: 'photo',
      title: 'Progreso del Trabajo',
      description: 'Fotos del proceso de trabajo y pasos importantes',
      required: true,
      minFiles: 3,
      maxFiles: 10,
      acceptedFormats: ['image/jpeg', 'image/png'],
      examples: ['Desmontaje', 'Proceso de instalación', 'Conexiones', 'Torques aplicados'],
      status: 'pending'
    },
    {
      id: 'work_video',
      phase: 'execution',
      type: 'video',
      title: 'Video del Proceso (Opcional)',
      description: 'Video corto mostrando el proceso crítico del trabajo',
      required: false,
      minFiles: 1,
      maxFiles: 2,
      acceptedFormats: ['video/mp4', 'video/mov'],
      examples: ['Funcionamiento antes/después', 'Proceso de instalación crítico'],
      status: 'pending'
    },
    {
      id: 'quality_testing',
      phase: 'testing',
      type: 'photo',
      title: 'Pruebas de Calidad',
      description: 'Evidencia de las pruebas realizadas y resultados',
      required: true,
      minFiles: 2,
      maxFiles: 6,
      acceptedFormats: ['image/jpeg', 'image/png'],
      examples: ['Pruebas funcionales', 'Mediciones finales', 'Funcionamiento correcto'],
      status: 'pending'
    },
    {
      id: 'final_inspection',
      phase: 'cleanup',
      type: 'photo',
      title: 'Inspección Final',
      description: 'Fotos del vehículo terminado y área de trabajo limpia',
      required: true,
      minFiles: 3,
      maxFiles: 6,
      acceptedFormats: ['image/jpeg', 'image/png'],
      examples: ['Trabajo completado', 'Área limpia', 'Vehículo listo para entrega'],
      status: 'pending'
    },
    {
      id: 'completion_summary',
      phase: 'cleanup',
      type: 'document',
      title: 'Resumen de Trabajo Completado',
      description: 'Documento resumen con detalles del trabajo realizado',
      required: true,
      minFiles: 1,
      maxFiles: 2,
      acceptedFormats: ['application/pdf', 'image/jpeg', 'image/png'],
      examples: ['Reporte de trabajo', 'Lista de verificación', 'Recomendaciones'],
      status: 'pending'
    }
  ]);

  const form = useForm<EvidenceFormData>({
    resolver: zodResolver(evidenceSchema),
    defaultValues: {
      phase: currentPhase,
      evidenceType: 'photo',
      files: [],
      description: '',
      isRequired: false,
    },
  });

  // Filter requirements by current phase
  const getCurrentPhaseRequirements = () => {
    return evidenceRequirements.filter(req => req.phase === currentPhase);
  };

  // Get all requirements
  const getAllRequirements = () => {
    return evidenceRequirements;
  };

  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    // Validate file types and sizes
    const validFiles = files.filter(file => {
      const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB limit
      const isValidType = selectedRequirement ? 
        evidenceRequirements.find(req => req.id === selectedRequirement)?.acceptedFormats?.includes(file.type) :
        true;
      
      if (!isValidSize) {
        toast({
          title: 'Archivo muy grande',
          description: `${file.name} excede el límite de 10MB`,
          variant: 'destructive',
        });
      }
      
      if (!isValidType) {
        toast({
          title: 'Tipo de archivo no válido',
          description: `${file.name} no es un tipo de archivo aceptado`,
          variant: 'destructive',
        });
      }
      
      return isValidSize && isValidType;
    });

    setSelectedFiles(prev => [...prev, ...validFiles]);

    // Create preview URLs for images
    const newPreviews = validFiles
      .filter(file => file.type.startsWith('image/'))
      .map(file => ({
        file,
        url: URL.createObjectURL(file)
      }));
    
    setPreviewFiles(prev => [...prev, ...newPreviews]);
  };

  // Remove file
  const removeFile = (index: number) => {
    const fileToRemove = selectedFiles[index];
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
    
    // Remove preview if exists
    const previewIndex = previewFiles.findIndex(p => p.file === fileToRemove);
    if (previewIndex >= 0) {
      URL.revokeObjectURL(previewFiles[previewIndex].url);
      setPreviewFiles(prev => prev.filter((_, i) => i !== previewIndex));
    }
  };

  // Submit evidence
  const handleSubmit = async (data: EvidenceFormData) => {
    if (selectedFiles.length === 0) {
      toast({
        title: 'Error',
        description: 'Selecciona al menos un archivo',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const evidenceData = {
        ...data,
        files: selectedFiles,
      };

      const success = await onSubmit(evidenceData);
      
      if (success) {
        toast({
          title: 'Éxito',
          description: 'Evidencia subida correctamente',
        });
        
        // Update requirement status
        if (selectedRequirement) {
          setEvidenceRequirements(prev => prev.map(req => 
            req.id === selectedRequirement 
              ? { ...req, status: 'uploaded' as const }
              : req
          ));
        }
        
        // Reset form
        form.reset();
        setSelectedFiles([]);
        setPreviewFiles([]);
        onClose();
      } else {
        toast({
          title: 'Error',
          description: 'Error al subir la evidencia',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error submitting evidence:', error);
      toast({
        title: 'Error',
        description: 'Error inesperado al subir la evidencia',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Clean up preview URLs on unmount
  useEffect(() => {
    return () => {
      previewFiles.forEach(preview => URL.revokeObjectURL(preview.url));
    };
  }, []);

  const getRequirementStatusColor = (status: EvidenceRequirement['status']) => {
    switch (status) {
      case 'pending': return 'bg-gray-100 text-gray-800';
      case 'uploaded': return 'bg-blue-100 text-blue-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRequirementStatusIcon = (status: EvidenceRequirement['status']) => {
    switch (status) {
      case 'pending': return <Upload className="h-4 w-4" />;
      case 'uploaded': return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'approved': return <CheckCircle className="h-4 w-4" />;
      case 'rejected': return <AlertTriangle className="h-4 w-4" />;
      default: return <Upload className="h-4 w-4" />;
    }
  };

  const getTypeIcon = (type: EvidenceRequirement['type']) => {
    switch (type) {
      case 'photo': return <Camera className="h-4 w-4" />;
      case 'video': return <Video className="h-4 w-4" />;
      case 'document': return <FileText className="h-4 w-4" />;
      default: return <Upload className="h-4 w-4" />;
    }
  };

  const currentPhaseRequirements = getCurrentPhaseRequirements();
  const allRequirements = getAllRequirements();
  const completedRequirements = allRequirements.filter(req => req.status === 'approved').length;
  const totalRequirements = allRequirements.filter(req => req.required).length;
  const progressPercentage = totalRequirements > 0 ? (completedRequirements / totalRequirements) * 100 : 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Evidencia Estructurada - {serviceName}</DialogTitle>
          <div className="flex items-center gap-4 mt-2">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Progreso de Evidencia:</span>
              <Badge variant="outline">{completedRequirements}/{totalRequirements}</Badge>
            </div>
            <Progress value={progressPercentage} className="flex-1 h-2" />
            <span className="text-sm text-gray-600">{Math.round(progressPercentage)}%</span>
          </div>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="requirements">Requerimientos</TabsTrigger>
            <TabsTrigger value="upload">Subir Evidencia</TabsTrigger>
            <TabsTrigger value="gallery">Galería</TabsTrigger>
          </TabsList>

          <TabsContent value="requirements" className="space-y-4">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Esta es la evidencia requerida para completar el servicio. Los elementos marcados como 
                "Requerido" son obligatorios para finalizar el trabajo.
              </AlertDescription>
            </Alert>

            {phases.map(phase => {
              const phaseRequirements = allRequirements.filter(req => req.phase === phase.id);
              if (phaseRequirements.length === 0) return null;

              return (
                <Card key={phase.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>Fase: {phase.name}</span>
                      <Badge variant="outline">
                        {phaseRequirements.filter(req => req.status === 'approved').length}/
                        {phaseRequirements.filter(req => req.required).length} completados
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {phaseRequirements.map(requirement => (
                        <div 
                          key={requirement.id}
                          className={`p-4 border rounded-lg ${
                            requirement.phase === currentPhase ? 'border-blue-200 bg-blue-50' : 'border-gray-200'
                          }`}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex items-start gap-3 flex-1">
                              <div className="flex items-center gap-2">
                                {getTypeIcon(requirement.type)}
                                {getRequirementStatusIcon(requirement.status)}
                              </div>
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-1">
                                  <h4 className="font-medium">{requirement.title}</h4>
                                  {requirement.required && (
                                    <Badge variant="destructive" className="text-xs">Requerido</Badge>
                                  )}
                                </div>
                                <p className="text-sm text-gray-600 mb-2">{requirement.description}</p>
                                
                                {requirement.examples && (
                                  <div className="text-xs text-gray-500">
                                    <p className="font-medium mb-1">Ejemplos:</p>
                                    <ul className="list-disc list-inside space-y-0.5">
                                      {requirement.examples.map((example, idx) => (
                                        <li key={idx}>{example}</li>
                                      ))}
                                    </ul>
                                  </div>
                                )}
                              </div>
                            </div>
                            <div className="flex flex-col items-end gap-2">
                              <Badge className={getRequirementStatusColor(requirement.status)}>
                                {requirement.status}
                              </Badge>
                              {requirement.phase === currentPhase && requirement.status === 'pending' && (
                                <Button
                                  size="sm"
                                  onClick={() => {
                                    setSelectedRequirement(requirement.id);
                                    form.setValue('evidenceType', requirement.type);
                                    setActiveTab('upload');
                                  }}
                                >
                                  Subir
                                </Button>
                              )}
                            </div>
                          </div>
                          
                          {requirement.rejectionReason && (
                            <Alert className="mt-3">
                              <AlertTriangle className="h-4 w-4" />
                              <AlertDescription>
                                <strong>Rechazado:</strong> {requirement.rejectionReason}
                              </AlertDescription>
                            </Alert>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </TabsContent>

          <TabsContent value="upload" className="space-y-6">
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              {/* Requirement Selection */}
              {currentPhaseRequirements.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Seleccionar Requerimiento de Evidencia</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-3">
                      {currentPhaseRequirements.map(req => (
                        <div 
                          key={req.id}
                          className={`p-3 border rounded cursor-pointer transition-colors ${
                            selectedRequirement === req.id 
                              ? 'border-blue-500 bg-blue-50' 
                              : 'border-gray-200 hover:bg-gray-50'
                          }`}
                          onClick={() => {
                            setSelectedRequirement(req.id);
                            form.setValue('evidenceType', req.type);
                          }}
                        >
                          <div className="flex items-center gap-3">
                            <Checkbox 
                              checked={selectedRequirement === req.id}
                              onChange={() => {}}
                            />
                            {getTypeIcon(req.type)}
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <span className="font-medium">{req.title}</span>
                                {req.required && (
                                  <Badge variant="destructive" className="text-xs">Requerido</Badge>
                                )}
                              </div>
                              <p className="text-sm text-gray-600">{req.description}</p>
                            </div>
                            <Badge className={getRequirementStatusColor(req.status)}>
                              {req.status}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* File Upload */}
              <Card>
                <CardHeader>
                  <CardTitle>Subir Archivos</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                    <div className="text-center">
                      <Upload className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="mt-4">
                        <label htmlFor="evidence-upload" className="cursor-pointer">
                          <span className="mt-2 block text-sm font-medium text-gray-900">
                            Seleccionar archivos
                          </span>
                          <span className="mt-1 block text-sm text-gray-500">
                            Imágenes, videos o documentos (máx. 10MB cada uno)
                          </span>
                        </label>
                        <input
                          id="evidence-upload"
                          type="file"
                          multiple
                          accept="image/*,video/*,.pdf"
                          onChange={handleFileChange}
                          className="hidden"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Selected Files Preview */}
                  {selectedFiles.length > 0 && (
                    <div className="space-y-3">
                      <Label>Archivos Seleccionados ({selectedFiles.length})</Label>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                        {selectedFiles.map((file, index) => {
                          const preview = previewFiles.find(p => p.file === file);
                          return (
                            <div key={index} className="relative border rounded-lg p-2 bg-gray-50">
                              {preview ? (
                                <div className="aspect-square mb-2">
                                  <img 
                                    src={preview.url} 
                                    alt={file.name}
                                    className="w-full h-full object-cover rounded"
                                  />
                                </div>
                              ) : (
                                <div className="aspect-square mb-2 flex items-center justify-center bg-gray-200 rounded">
                                  {file.type.startsWith('video/') ? (
                                    <Video className="h-8 w-8 text-gray-500" />
                                  ) : (
                                    <FileText className="h-8 w-8 text-gray-500" />
                                  )}
                                </div>
                              )}
                              
                              <div className="space-y-1">
                                <p className="text-xs font-medium truncate">{file.name}</p>
                                <p className="text-xs text-gray-500">
                                  {(file.size / 1024 / 1024).toFixed(2)} MB
                                </p>
                              </div>
                              
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeFile(index)}
                                className="absolute -top-2 -right-2 h-6 w-6 p-0 bg-red-500 text-white rounded-full hover:bg-red-600"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description">Descripción de la Evidencia *</Label>
                <Textarea
                  {...form.register('description')}
                  placeholder="Describe detalladamente qué muestra esta evidencia y su relevancia para el servicio..."
                  rows={4}
                />
                {form.formState.errors.description && (
                  <p className="text-sm text-red-600">{form.formState.errors.description.message}</p>
                )}
              </div>

              {/* Quality Check */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Verificación de Calidad</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox {...form.register('qualityCheck.isVisible')} />
                    <Label className="text-sm">Las imágenes/videos son claros y visibles</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox {...form.register('qualityCheck.isRelevant')} />
                    <Label className="text-sm">El contenido es relevante para el servicio</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox {...form.register('qualityCheck.hasClearDetails')} />
                    <Label className="text-sm">Se pueden ver claramente los detalles importantes</Label>
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-end gap-3">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancelar
                </Button>
                <Button 
                  type="submit" 
                  disabled={isSubmitting || selectedFiles.length === 0}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Subir Evidencia
                </Button>
              </div>
            </form>
          </TabsContent>

          <TabsContent value="gallery" className="space-y-4">
            <Alert>
              <Eye className="h-4 w-4" />
              <AlertDescription>
                Aquí puedes ver toda la evidencia subida organizizada por fases del servicio.
              </AlertDescription>
            </Alert>

            {phases.map(phase => {
              const phaseRequirements = allRequirements.filter(req => 
                req.phase === phase.id && req.status !== 'pending'
              );
              
              if (phaseRequirements.length === 0) return null;

              return (
                <Card key={phase.id}>
                  <CardHeader>
                    <CardTitle>Evidencia - {phase.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {phaseRequirements.map(req => (
                        <div key={req.id} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="font-medium">{req.title}</h4>
                            <Badge className={getRequirementStatusColor(req.status)}>
                              {req.status}
                            </Badge>
                          </div>
                          
                          {req.uploadedFiles && req.uploadedFiles.length > 0 ? (
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                              {req.uploadedFiles.map(file => (
                                <div key={file.id} className="aspect-square bg-gray-100 rounded border">
                                  {file.type.startsWith('image/') ? (
                                    <img 
                                      src={file.url} 
                                      alt={file.name}
                                      className="w-full h-full object-cover rounded"
                                    />
                                  ) : (
                                    <div className="w-full h-full flex items-center justify-center">
                                      {file.type.startsWith('video/') ? (
                                        <Video className="h-8 w-8 text-gray-500" />
                                      ) : (
                                        <FileText className="h-8 w-8 text-gray-500" />
                                      )}
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          ) : (
                            <p className="text-sm text-gray-500 text-center py-8">
                              No hay evidencia subida para este requerimiento
                            </p>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}