'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, Play, Pause, CheckCircle, Clock, Upload, X, Image } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { CorrectiveService, formatCurrency, formatDuration } from '../types';
import { updateServiceExecution } from '../_actions/updateServiceExecution';

const serviceExecutionSchema = z.object({
  action: z.enum(['start', 'pause', 'resume', 'complete']),
  notes: z.string().optional(),
  evidence: z.array(z.instanceof(File)).optional(),
  completionNotes: z.string().optional(),
});

type ServiceExecutionFormData = z.infer<typeof serviceExecutionSchema>;

interface ServiceExecutionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  service: CorrectiveService;
  orderId: string;
}

export default function ServiceExecutionModal({
  isOpen,
  onClose,
  onSuccess,
  service,
  orderId
}: ServiceExecutionModalProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [currentAction, setCurrentAction] = useState<'start' | 'pause' | 'resume' | 'complete'>('start');
  const [serviceStartTime, setServiceStartTime] = useState<Date | null>(null);
  const [elapsedTime, setElapsedTime] = useState(0);

  const form = useForm<ServiceExecutionFormData>({
    resolver: zodResolver(serviceExecutionSchema),
    defaultValues: {
      action: 'start',
      notes: '',
      evidence: [],
      completionNotes: '',
    },
  });

  // Determine the current action based on service status
  useEffect(() => {
    if (service.status === 'not-started') {
      setCurrentAction('start');
    } else if (service.status === 'in-progress') {
      setCurrentAction('pause');
    } else if (service.status === 'completed') {
      setCurrentAction('complete');
    }
  }, [service.status]);

  // Timer for elapsed time when service is in progress
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (service.status === 'in-progress' && serviceStartTime) {
      interval = setInterval(() => {
        const now = new Date();
        const elapsed = Math.floor((now.getTime() - serviceStartTime.getTime()) / 1000 / 60); // minutes
        setElapsedTime(elapsed);
      }, 60000); // Update every minute
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [service.status, serviceStartTime]);

  const onSubmit = async (data: ServiceExecutionFormData) => {
    setIsSubmitting(true);
    try {
      const executionData = {
        action: data.action,
        notes: data.notes,
        completionNotes: data.completionNotes,
        evidence: selectedFiles,
        timestamp: new Date().toISOString(),
      };

      const response = await updateServiceExecution(service._id, orderId, executionData);

      if (response.success) {
        toast({
          title: 'Éxito',
          description: getSuccessMessage(data.action),
        });

        // Update local state based on action
        if (data.action === 'start') {
          setServiceStartTime(new Date());
        }

        form.reset();
        setSelectedFiles([]);
        onSuccess();
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Error al actualizar el servicio',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating service execution:', error);
      toast({
        title: 'Error',
        description: 'Error inesperado al actualizar el servicio',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getSuccessMessage = (action: string) => {
    switch (action) {
      case 'start': return 'Servicio iniciado exitosamente';
      case 'pause': return 'Servicio pausado exitosamente';
      case 'resume': return 'Servicio reanudado exitosamente';
      case 'complete': return 'Servicio completado exitosamente';
      default: return 'Servicio actualizado exitosamente';
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setSelectedFiles(prev => [...prev, ...files]);
  };

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleClose = () => {
    form.reset();
    setSelectedFiles([]);
    onClose();
  };

  const getProgressPercentage = () => {
    switch (service.status) {
      case 'not-started': return 0;
      case 'in-progress': return 50;
      case 'completed': return 100;
      case 'waiting-for-parts': return 25;
      case 'cancelled': return 0;
      default: return 0;
    }
  };

  const getActionButton = () => {
    switch (service.status) {
      case 'not-started':
        return {
          action: 'start' as const,
          label: 'Iniciar Servicio',
          icon: <Play className="h-4 w-4" />,
          color: 'bg-green-600 hover:bg-green-700'
        };
      case 'in-progress':
        return {
          action: 'complete' as const,
          label: 'Completar Servicio',
          icon: <CheckCircle className="h-4 w-4" />,
          color: 'bg-blue-600 hover:bg-blue-700'
        };
      case 'waiting-for-parts':
        return {
          action: 'start' as const,
          label: 'Iniciar (Partes Disponibles)',
          icon: <Play className="h-4 w-4" />,
          color: 'bg-green-600 hover:bg-green-700'
        };
      default:
        return null;
    }
  };

  const actionButton = getActionButton();

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Ejecución del Servicio</DialogTitle>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Service Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{service.serviceName}</span>
                <Badge variant="secondary" className={getStatusColor(service.status)}>
                  {getStatusLabel(service.status)}
                </Badge>
              </CardTitle>
              <p className="text-sm text-gray-600">{service.description}</p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Costo Estimado:</p>
                  <p className="font-medium">{formatCurrency(service.estimatedCost)}</p>
                </div>
                <div>
                  <p className="text-gray-500">Duración Estimada:</p>
                  <p className="font-medium">{formatDuration(service.estimatedDuration)}</p>
                </div>
                <div>
                  <p className="text-gray-500">Tiempo Transcurrido:</p>
                  <p className="font-medium">{elapsedTime} min</p>
                </div>
                <div>
                  <p className="text-gray-500">Refacciones:</p>
                  <p className="font-medium">{service.parts.length}</p>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Progreso</Label>
                  <span className="text-sm text-gray-500">{getProgressPercentage()}%</span>
                </div>
                <Progress value={getProgressPercentage()} className="h-2" />
              </div>
            </CardContent>
          </Card>

          {/* Action Section */}
          {actionButton && (
            <Card>
              <CardHeader>
                <CardTitle>Acción del Servicio</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-4">
                  <Button
                    type="button"
                    className={actionButton.color}
                    onClick={() => {
                      form.setValue('action', actionButton.action);
                      setCurrentAction(actionButton.action);
                    }}
                  >
                    {actionButton.icon}
                    {actionButton.label}
                  </Button>
                  <div className="text-sm text-gray-600">
                    <p>Estado actual: <strong>{getStatusLabel(service.status)}</strong></p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Notes Section */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notas del Progreso</Label>
            <Textarea
              {...form.register('notes')}
              placeholder="Describe el trabajo realizado, problemas encontrados, etc..."
              rows={4}
            />
          </div>

          {/* Completion Notes - Only show when completing */}
          {currentAction === 'complete' && (
            <div className="space-y-2">
              <Label htmlFor="completionNotes">Notas de Finalización *</Label>
              <Textarea
                {...form.register('completionNotes')}
                placeholder="Resumen del trabajo completado, resultados, recomendaciones..."
                rows={3}
                required
              />
            </div>
          )}

          {/* Evidence Upload */}
          <div className="space-y-4">
            <Label>Evidencia (Fotos/Videos)</Label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
              <div className="text-center">
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-4">
                  <label htmlFor="evidence-upload" className="cursor-pointer">
                    <span className="mt-2 block text-sm font-medium text-gray-900">
                      Subir archivos de evidencia
                    </span>
                    <span className="mt-1 block text-sm text-gray-500">
                      PNG, JPG, MP4 hasta 10MB cada uno
                    </span>
                  </label>
                  <input
                    id="evidence-upload"
                    type="file"
                    multiple
                    accept="image/*,video/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                </div>
              </div>
            </div>

            {/* Selected Files */}
            {selectedFiles.length > 0 && (
              <div className="space-y-2">
                <Label>Archivos Seleccionados</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {selectedFiles.map((file, index) => (
                    <div key={index} className="relative border rounded-lg p-2 bg-gray-50">
                      <div className="flex items-center space-x-2">
                        <Image className="h-4 w-4 text-gray-500" />
                        <span className="text-xs truncate flex-1">{file.name}</span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(index)}
                          className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {(file.size / 1024 / 1024).toFixed(2)} MB
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancelar
            </Button>
            <Button type="submit" disabled={isSubmitting || !actionButton}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {actionButton?.label || 'Actualizar'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

// Helper functions
function getStatusColor(status: string) {
  switch (status) {
    case 'not-started': return 'bg-gray-100 text-gray-800';
    case 'in-progress': return 'bg-blue-100 text-blue-800';
    case 'completed': return 'bg-green-100 text-green-800';
    case 'waiting-for-parts': return 'bg-yellow-100 text-yellow-800';
    case 'cancelled': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
}

function getStatusLabel(status: string) {
  switch (status) {
    case 'not-started': return 'No Iniciado';
    case 'in-progress': return 'En Progreso';
    case 'completed': return 'Completado';
    case 'waiting-for-parts': return 'Esperando Refacciones';
    case 'cancelled': return 'Cancelado';
    default: return 'Desconocido';
  }
}
