'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { 
  Clock, 
  Play, 
  Pause, 
  CheckCircle, 
  AlertTriangle, 
  Timer, 
  Activity,
  RefreshCw,
  Eye,
  Wrench
} from 'lucide-react';
import { CorrectiveMaintenanceOrder, CorrectiveService, formatCurrency, formatDuration } from '../types';
import { useServiceTimeTracking } from '../_hooks/useServiceTimeTracking';
import ServiceExecutionModal from './ServiceExecutionModal';
import ServiceEvidenceModal from './ServiceEvidenceModal';

interface RealTimeServiceDashboardProps {
  orders: CorrectiveMaintenanceOrder[];
  onRefresh: () => void;
  className?: string;
}

export default function RealTimeServiceDashboard({ 
  orders, 
  onRefresh, 
  className 
}: RealTimeServiceDashboardProps) {
  const [selectedService, setSelectedService] = useState<CorrectiveService | null>(null);
  const [selectedOrderId, setSelectedOrderId] = useState<string>('');
  const [showExecutionModal, setShowExecutionModal] = useState(false);
  const [showEvidenceModal, setShowEvidenceModal] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      onRefresh();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, onRefresh]);

  // Filter orders with active services
  const activeOrders = orders.filter(order => 
    order.services && 
    order.services.some(service => 
      service.status === 'in-progress' || 
      service.status === 'not-started' ||
      service.status === 'waiting-for-parts'
    )
  );

  const handleServiceAction = (service: CorrectiveService, orderId: string, action: 'execute' | 'complete') => {
    setSelectedService(service);
    setSelectedOrderId(orderId);
    
    if (action === 'execute') {
      setShowExecutionModal(true);
    } else if (action === 'complete') {
      setShowEvidenceModal(true);
    }
  };

  const handleModalClose = () => {
    setSelectedService(null);
    setSelectedOrderId('');
    setShowExecutionModal(false);
    setShowEvidenceModal(false);
  };

  const handleModalSuccess = () => {
    handleModalClose();
    onRefresh();
  };

  const getServiceStatusColor = (status: string) => {
    switch (status) {
      case 'not-started': return 'bg-gray-100 text-gray-800';
      case 'in-progress': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'waiting-for-parts': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getServiceStatusIcon = (status: string) => {
    switch (status) {
      case 'not-started': return <Clock className="h-4 w-4" />;
      case 'in-progress': return <Play className="h-4 w-4" />;
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'waiting-for-parts': return <Pause className="h-4 w-4" />;
      case 'cancelled': return <AlertTriangle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getServiceStatusLabel = (status: string) => {
    switch (status) {
      case 'not-started': return 'No Iniciado';
      case 'in-progress': return 'En Progreso';
      case 'completed': return 'Completado';
      case 'waiting-for-parts': return 'Esperando Refacciones';
      case 'cancelled': return 'Cancelado';
      default: return 'Desconocido';
    }
  };

  const calculateServiceProgress = (service: CorrectiveService) => {
    switch (service.status) {
      case 'not-started': return 0;
      case 'waiting-for-parts': return 25;
      case 'in-progress': return 50;
      case 'completed': return 100;
      default: return 0;
    }
  };

  const calculateElapsedTime = (service: CorrectiveService) => {
    if (service.status === 'not-started') return 0;
    
    const startTime = new Date(service.createdAt);
    const now = new Date();
    return Math.floor((now.getTime() - startTime.getTime()) / (1000 * 60)); // minutes
  };

  const calculateRemainingTime = (service: CorrectiveService) => {
    const elapsedMinutes = calculateElapsedTime(service);
    const estimatedMinutes = service.estimatedDuration * 60;
    return Math.max(0, estimatedMinutes - elapsedMinutes);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Activity className="h-6 w-6 text-blue-600" />
          <h2 className="text-2xl font-bold">Seguimiento en Tiempo Real</h2>
          <Badge variant="secondary" className="ml-2">
            {activeOrders.length} órdenes activas
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={autoRefresh ? 'bg-green-50 border-green-200' : ''}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${autoRefresh ? 'animate-spin' : ''}`} />
            Auto-actualizar {autoRefresh ? 'ON' : 'OFF'}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualizar
          </Button>
        </div>
      </div>

      {/* Active Orders */}
      {activeOrders.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8 text-gray-500">
              <Timer className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium">No hay servicios activos</p>
              <p className="text-sm">
                Los servicios en progreso aparecerán aquí para seguimiento en tiempo real
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6">
          {activeOrders.map((order) => {
            const activeServices = order.services?.filter(service => 
              service.status !== 'completed' && service.status !== 'cancelled'
            ) || [];

            return (
              <Card key={order._id} className="border-l-4 border-l-blue-400">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <Wrench className="h-5 w-5" />
                        Orden #{order._id.slice(-8).toUpperCase()}
                      </CardTitle>
                      <p className="text-sm text-gray-600 mt-1">
                        {order.vehicle?.brand} {order.vehicle?.model} - {order.vehicle?.carPlates?.plates}
                      </p>
                    </div>
                    <Badge variant="outline" className="bg-blue-50">
                      {activeServices.length} servicios activos
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  {/* Services List */}
                  <div className="space-y-3">
                    {activeServices.map((service) => {
                      const progress = calculateServiceProgress(service);
                      const elapsedMinutes = calculateElapsedTime(service);
                      const remainingMinutes = calculateRemainingTime(service);
                      const isOverdue = elapsedMinutes > (service.estimatedDuration * 60);

                      return (
                        <div key={service._id} className="border rounded-lg p-4 bg-gray-50">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-2">
                              {getServiceStatusIcon(service.status)}
                              <h4 className="font-medium">{service.serviceName}</h4>
                              {isOverdue && (
                                <AlertTriangle className="h-4 w-4 text-red-500" />
                              )}
                            </div>
                            <Badge 
                              variant="secondary" 
                              className={getServiceStatusColor(service.status)}
                            >
                              {getServiceStatusLabel(service.status)}
                            </Badge>
                          </div>

                          {/* Progress Bar */}
                          <div className="space-y-2 mb-3">
                            <div className="flex items-center justify-between text-sm">
                              <span>Progreso</span>
                              <span>{progress}%</span>
                            </div>
                            <Progress 
                              value={progress} 
                              className={`h-2 ${isOverdue ? 'bg-red-100' : ''}`}
                            />
                          </div>

                          {/* Time Information */}
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                            <div>
                              <p className="text-gray-500">Estimado:</p>
                              <p className="font-medium">{formatDuration(service.estimatedDuration)}</p>
                            </div>
                            <div>
                              <p className="text-gray-500">Transcurrido:</p>
                              <p className={`font-medium ${isOverdue ? 'text-red-600' : ''}`}>
                                {Math.floor(elapsedMinutes / 60)}h {elapsedMinutes % 60}m
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-500">Restante:</p>
                              <p className="font-medium">
                                {Math.floor(remainingMinutes / 60)}h {remainingMinutes % 60}m
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-500">Costo:</p>
                              <p className="font-medium">{formatCurrency(service.estimatedCost)}</p>
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex gap-2">
                            {service.status === 'not-started' && (
                              <Button
                                size="sm"
                                onClick={() => handleServiceAction(service, order._id, 'execute')}
                                className="bg-green-600 hover:bg-green-700"
                              >
                                <Play className="h-4 w-4 mr-2" />
                                Iniciar
                              </Button>
                            )}
                            
                            {service.status === 'in-progress' && (
                              <Button
                                size="sm"
                                onClick={() => handleServiceAction(service, order._id, 'complete')}
                                className="bg-blue-600 hover:bg-blue-700"
                              >
                                <CheckCircle className="h-4 w-4 mr-2" />
                                Completar
                              </Button>
                            )}
                            
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleServiceAction(service, order._id, 'execute')}
                            >
                              <Eye className="h-4 w-4 mr-2" />
                              Ver Detalles
                            </Button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Modals */}
      {selectedService && (
        <>
          <ServiceExecutionModal
            isOpen={showExecutionModal}
            onClose={handleModalClose}
            onSuccess={handleModalSuccess}
            service={selectedService}
            orderId={selectedOrderId}
          />
          
          <ServiceEvidenceModal
            isOpen={showEvidenceModal}
            onClose={handleModalClose}
            onSuccess={handleModalSuccess}
            service={selectedService}
            orderId={selectedOrderId}
          />
        </>
      )}
    </div>
  );
}
