'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Clock, Timer, AlertTriangle, CheckCircle, Play, Pause } from 'lucide-react';
import { CorrectiveService, formatDuration } from '../types';

interface ServiceTimeInfo {
  serviceId: string;
  serviceName: string;
  estimatedDuration: number; // in hours
  actualDuration?: number; // in hours
  startTime?: string;
  endTime?: string;
  status: string;
  isOverdue: boolean;
  timeRemaining?: number; // in hours
}

interface ServiceTimeTrackerProps {
  services: CorrectiveService[];
  orderId: string;
  className?: string;
}

export default function ServiceTimeTracker({ services, orderId, className }: ServiceTimeTrackerProps) {
  const [timeInfo, setTimeInfo] = useState<ServiceTimeInfo[]>([]);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time every minute
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, []);

  // Calculate time information for each service
  useEffect(() => {
    const calculateTimeInfo = () => {
      const info = services.map(service => {
        const estimatedDuration = service.estimatedDuration;
        let actualDuration: number | undefined;
        let timeRemaining: number | undefined;
        let isOverdue = false;

        // Calculate actual duration if service has started
        if (service.status === 'in-progress' || service.status === 'completed') {
          const startTime = service.createdAt; // Assuming this is when service started
          const endTime = service.status === 'completed' ? service.updatedAt : currentTime.toISOString();
          
          const start = new Date(startTime);
          const end = new Date(endTime);
          actualDuration = (end.getTime() - start.getTime()) / (1000 * 60 * 60); // Convert to hours

          if (service.status === 'in-progress') {
            timeRemaining = Math.max(0, estimatedDuration - actualDuration);
            isOverdue = actualDuration > estimatedDuration;
          }
        }

        return {
          serviceId: service._id,
          serviceName: service.serviceName,
          estimatedDuration,
          actualDuration,
          startTime: service.createdAt,
          endTime: service.status === 'completed' ? service.updatedAt : undefined,
          status: service.status,
          isOverdue,
          timeRemaining,
        };
      });

      setTimeInfo(info);
    };

    calculateTimeInfo();
  }, [services, currentTime]);

  // Calculate totals
  const totalEstimatedTime = timeInfo.reduce((sum, info) => sum + info.estimatedDuration, 0);
  const totalActualTime = timeInfo.reduce((sum, info) => sum + (info.actualDuration || 0), 0);
  const totalTimeRemaining = timeInfo.reduce((sum, info) => sum + (info.timeRemaining || 0), 0);
  const completedServices = timeInfo.filter(info => info.status === 'completed').length;
  const inProgressServices = timeInfo.filter(info => info.status === 'in-progress').length;
  const overdueServices = timeInfo.filter(info => info.isOverdue).length;

  const overallProgress = services.length > 0 ? (completedServices / services.length) * 100 : 0;

  const getStatusIcon = (status: string, isOverdue: boolean) => {
    if (isOverdue) return <AlertTriangle className="h-4 w-4 text-red-500" />;
    
    switch (status) {
      case 'not-started': return <Clock className="h-4 w-4 text-gray-500" />;
      case 'in-progress': return <Play className="h-4 w-4 text-blue-500" />;
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'waiting-for-parts': return <Pause className="h-4 w-4 text-yellow-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string, isOverdue: boolean) => {
    if (isOverdue) return 'bg-red-100 text-red-800';
    
    switch (status) {
      case 'not-started': return 'bg-gray-100 text-gray-800';
      case 'in-progress': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'waiting-for-parts': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string, isOverdue: boolean) => {
    if (isOverdue) return 'Retrasado';
    
    switch (status) {
      case 'not-started': return 'No Iniciado';
      case 'in-progress': return 'En Progreso';
      case 'completed': return 'Completado';
      case 'waiting-for-parts': return 'Esperando Refacciones';
      default: return 'Desconocido';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Overall Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Timer className="h-5 w-5" />
            Resumen de Tiempo Total
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <p className="text-gray-500">Tiempo Estimado:</p>
              <p className="font-bold text-lg">{formatDuration(totalEstimatedTime)}</p>
            </div>
            <div>
              <p className="text-gray-500">Tiempo Real:</p>
              <p className="font-bold text-lg">{formatDuration(totalActualTime)}</p>
            </div>
            <div>
              <p className="text-gray-500">Tiempo Restante:</p>
              <p className="font-bold text-lg">{formatDuration(totalTimeRemaining)}</p>
            </div>
            <div>
              <p className="text-gray-500">Progreso:</p>
              <p className="font-bold text-lg">{overallProgress.toFixed(1)}%</p>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Progreso General</span>
              <span className="text-sm text-gray-500">{completedServices}/{services.length} servicios</span>
            </div>
            <Progress value={overallProgress} className="h-2" />
          </div>

          {/* Status Summary */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
            <div className="flex items-center gap-1">
              <CheckCircle className="h-3 w-3 text-green-500" />
              <span>Completados: {completedServices}</span>
            </div>
            <div className="flex items-center gap-1">
              <Play className="h-3 w-3 text-blue-500" />
              <span>En progreso: {inProgressServices}</span>
            </div>
            <div className="flex items-center gap-1">
              <AlertTriangle className="h-3 w-3 text-red-500" />
              <span>Retrasados: {overdueServices}</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3 text-gray-500" />
              <span>Pendientes: {services.length - completedServices - inProgressServices}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Individual Service Times */}
      <Card>
        <CardHeader>
          <CardTitle>Tiempo por Servicio</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {timeInfo.map((info) => (
              <div key={info.serviceId} className="border rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(info.status, info.isOverdue)}
                    <span className="font-medium">{info.serviceName}</span>
                  </div>
                  <Badge variant="secondary" className={getStatusColor(info.status, info.isOverdue)}>
                    {getStatusLabel(info.status, info.isOverdue)}
                  </Badge>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500">Estimado:</p>
                    <p className="font-medium">{formatDuration(info.estimatedDuration)}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Real:</p>
                    <p className="font-medium">
                      {info.actualDuration ? formatDuration(info.actualDuration) : '-'}
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-500">Restante:</p>
                    <p className="font-medium">
                      {info.timeRemaining !== undefined ? formatDuration(info.timeRemaining) : '-'}
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-500">Variación:</p>
                    <p className={`font-medium ${
                      info.actualDuration && info.actualDuration > info.estimatedDuration 
                        ? 'text-red-600' 
                        : info.actualDuration 
                          ? 'text-green-600' 
                          : 'text-gray-500'
                    }`}>
                      {info.actualDuration 
                        ? `${info.actualDuration > info.estimatedDuration ? '+' : ''}${formatDuration(info.actualDuration - info.estimatedDuration)}`
                        : '-'
                      }
                    </p>
                  </div>
                </div>

                {/* Progress bar for individual service */}
                {info.status === 'in-progress' && info.actualDuration && (
                  <div className="mt-3 space-y-1">
                    <div className="flex items-center justify-between text-xs">
                      <span>Progreso del servicio</span>
                      <span>{Math.min(100, (info.actualDuration / info.estimatedDuration) * 100).toFixed(1)}%</span>
                    </div>
                    <Progress 
                      value={Math.min(100, (info.actualDuration / info.estimatedDuration) * 100)} 
                      className={`h-1 ${info.isOverdue ? 'bg-red-100' : ''}`}
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Time Alerts */}
      {overdueServices > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <div>
                <p className="font-medium text-red-800">
                  ⚠️ {overdueServices} servicio(s) retrasado(s)
                </p>
                <p className="text-sm text-red-700">
                  Algunos servicios están tomando más tiempo del estimado. Considera revisar el progreso.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
