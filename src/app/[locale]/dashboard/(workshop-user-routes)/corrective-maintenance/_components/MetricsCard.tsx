'use client';

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Clock, 
  CheckCircle, 
  AlertTriangle, 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  Calendar,
  Wrench
} from 'lucide-react';
import { formatCurrency, formatDuration } from '../types';

interface MetricsData {
  totalOrders: number;
  completedOrders: number;
  inProgressOrders: number;
  pendingOrders: number;
  averageCompletionTime: number; // in hours
  slaCompliance: number; // percentage
  totalRevenue: number;
  averageOrderValue: number;
  approvalRate: number; // percentage
  customerSatisfaction: number; // NPS score
}

interface MetricsCardProps {
  data: MetricsData;
  period: 'today' | 'week' | 'month' | 'year';
}

export default function MetricsCard({ data, period }: MetricsCardProps) {
  const completionRate = data.totalOrders > 0 ? (data.completedOrders / data.totalOrders) * 100 : 0;
  
  const getPeriodLabel = () => {
    switch (period) {
      case 'today': return 'Hoy';
      case 'week': return 'Esta Semana';
      case 'month': return 'Este Mes';
      case 'year': return 'Este Año';
      default: return 'Período';
    }
  };

  const metrics = [
    {
      title: 'Órdenes Totales',
      value: data.totalOrders,
      icon: <Wrench className="h-4 w-4" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Completadas',
      value: data.completedOrders,
      icon: <CheckCircle className="h-4 w-4" />,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      percentage: completionRate,
    },
    {
      title: 'En Progreso',
      value: data.inProgressOrders,
      icon: <Clock className="h-4 w-4" />,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
    {
      title: 'Pendientes',
      value: data.pendingOrders,
      icon: <AlertTriangle className="h-4 w-4" />,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
    },
  ];

  const kpis = [
    {
      title: 'Tiempo Promedio de Reparación',
      value: formatDuration(data.averageCompletionTime),
      icon: <Clock className="h-4 w-4" />,
      trend: data.averageCompletionTime < 24 ? 'up' : 'down',
      trendValue: '12%',
    },
    {
      title: 'Cumplimiento de SLA',
      value: `${data.slaCompliance.toFixed(1)}%`,
      icon: <TrendingUp className="h-4 w-4" />,
      trend: data.slaCompliance >= 90 ? 'up' : 'down',
      trendValue: '5%',
      progress: data.slaCompliance,
    },
    {
      title: 'Ingresos Totales',
      value: formatCurrency(data.totalRevenue),
      icon: <DollarSign className="h-4 w-4" />,
      trend: 'up',
      trendValue: '8%',
    },
    {
      title: 'Valor Promedio por Orden',
      value: formatCurrency(data.averageOrderValue),
      icon: <DollarSign className="h-4 w-4" />,
      trend: data.averageOrderValue > 5000 ? 'up' : 'down',
      trendValue: '3%',
    },
    {
      title: 'Tasa de Aprobación',
      value: `${data.approvalRate.toFixed(1)}%`,
      icon: <CheckCircle className="h-4 w-4" />,
      trend: data.approvalRate >= 80 ? 'up' : 'down',
      trendValue: '2%',
      progress: data.approvalRate,
    },
    {
      title: 'Satisfacción del Cliente (NPS)',
      value: data.customerSatisfaction.toFixed(1),
      icon: <TrendingUp className="h-4 w-4" />,
      trend: data.customerSatisfaction >= 7 ? 'up' : 'down',
      trendValue: '1.2',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Métricas de Mantenimiento Correctivo</h2>
        <Badge variant="outline" className="flex items-center gap-1">
          <Calendar className="h-3 w-3" />
          {getPeriodLabel()}
        </Badge>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                  <p className="text-2xl font-bold">{metric.value}</p>
                  {metric.percentage && (
                    <p className="text-xs text-gray-500">
                      {metric.percentage.toFixed(1)}% del total
                    </p>
                  )}
                </div>
                <div className={`p-2 rounded-full ${metric.bgColor}`}>
                  <div className={metric.color}>
                    {metric.icon}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* KPIs */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {kpis.map((kpi, index) => (
          <Card key={index}>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 flex items-center gap-2">
                {kpi.icon}
                {kpi.title}
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex items-center justify-between mb-2">
                <span className="text-2xl font-bold">{kpi.value}</span>
                <div className={`flex items-center gap-1 text-xs ${
                  kpi.trend === 'up' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {kpi.trend === 'up' ? (
                    <TrendingUp className="h-3 w-3" />
                  ) : (
                    <TrendingDown className="h-3 w-3" />
                  )}
                  {kpi.trendValue}
                </div>
              </div>
              {kpi.progress && (
                <div className="space-y-1">
                  <Progress value={kpi.progress} className="h-2" />
                  <p className="text-xs text-gray-500">
                    Meta: {kpi.title.includes('SLA') ? '95%' : '85%'}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
