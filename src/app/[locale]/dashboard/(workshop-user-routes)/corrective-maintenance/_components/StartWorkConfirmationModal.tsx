'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { 
  AlertTriangle, 
  Package, 
  Clock, 
  CheckCircle, 
  Wrench,
  Loader2
} from 'lucide-react';
import { CorrectiveMaintenanceOrder, CorrectiveService, formatCurrency } from '../types';
import { startWork } from '../_actions/startWork';

interface StartWorkConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  order: CorrectiveMaintenanceOrder;
}

export default function StartWorkConfirmationModal({
  isOpen,
  onClose,
  onSuccess,
  order
}: StartWorkConfirmationModalProps) {
  const { toast } = useToast();
  const [isStarting, setIsStarting] = useState(false);

  // Analyze services and parts
  const servicesWithParts = order.services?.filter(service => 
    service.parts && service.parts.length > 0
  ) || [];

  const pendingParts = servicesWithParts.flatMap(service => 
    service.parts.filter(part => part.availability === 'pending')
  );

  const availableParts = servicesWithParts.flatMap(service => 
    service.parts.filter(part => part.availability === 'available')
  );

  const servicesWithoutParts = order.services?.filter(service => 
    !service.parts || service.parts.length === 0
  ) || [];

  const servicesWithAvailableParts = order.services?.filter(service => 
    service.parts && service.parts.length > 0 && 
    service.parts.every(part => part.availability === 'available')
  ) || [];

  const servicesWithPendingParts = order.services?.filter(service => 
    service.parts && service.parts.length > 0 && 
    service.parts.some(part => part.availability === 'pending')
  ) || [];

  const canStartImmediately = servicesWithoutParts.length > 0 || servicesWithAvailableParts.length > 0;
  const hasPendingParts = pendingParts.length > 0;

  const handleStartWork = async (force = false) => {
    setIsStarting(true);
    try {
      const response = await startWork(order._id);

      if (response.success) {
        toast({
          title: 'Éxito',
          description: hasPendingParts 
            ? 'Trabajo iniciado. Los servicios con refacciones pendientes quedarán en espera.'
            : 'Trabajo iniciado exitosamente',
        });
        onSuccess();
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Error al iniciar el trabajo',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error starting work:', error);
      toast({
        title: 'Error',
        description: 'Error inesperado al iniciar el trabajo',
        variant: 'destructive',
      });
    } finally {
      setIsStarting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Wrench className="h-5 w-5" />
            Confirmar Inicio de Trabajo
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Warning if there are pending parts */}
          {hasPendingParts && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Atención:</strong> Algunos servicios requieren refacciones que no están disponibles. 
                Estos servicios quedarán en estado "Esperando Refacciones" hasta que lleguen las piezas.
              </AlertDescription>
            </Alert>
          )}

          {/* Services that can start immediately */}
          {canStartImmediately && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-700">
                  <CheckCircle className="h-5 w-5" />
                  Servicios que pueden iniciarse ({servicesWithoutParts.length + servicesWithAvailableParts.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {servicesWithoutParts.map(service => (
                  <div key={service._id} className="flex items-center justify-between p-2 bg-green-50 rounded">
                    <div>
                      <p className="font-medium">{service.serviceName}</p>
                      <p className="text-sm text-gray-600">Sin refacciones requeridas</p>
                    </div>
                    <Badge className="bg-green-100 text-green-800">
                      Listo
                    </Badge>
                  </div>
                ))}
                {servicesWithAvailableParts.map(service => (
                  <div key={service._id} className="flex items-center justify-between p-2 bg-green-50 rounded">
                    <div>
                      <p className="font-medium">{service.serviceName}</p>
                      <p className="text-sm text-gray-600">
                        {service.parts.length} refacción(es) disponible(s)
                      </p>
                    </div>
                    <Badge className="bg-green-100 text-green-800">
                      Listo
                    </Badge>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Services waiting for parts */}
          {servicesWithPendingParts.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-yellow-700">
                  <Clock className="h-5 w-5" />
                  Servicios en espera de refacciones ({servicesWithPendingParts.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {servicesWithPendingParts.map(service => {
                  const pendingPartsForService = service.parts.filter(part => part.availability === 'pending');
                  return (
                    <div key={service._id} className="p-2 bg-yellow-50 rounded">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <p className="font-medium">{service.serviceName}</p>
                          <p className="text-sm text-gray-600">
                            {pendingPartsForService.length} refacción(es) pendiente(s)
                          </p>
                        </div>
                        <Badge className="bg-yellow-100 text-yellow-800">
                          Esperando
                        </Badge>
                      </div>
                      <div className="space-y-1">
                        {pendingPartsForService.map((part, index) => (
                          <div key={index} className="flex items-center justify-between text-sm">
                            <span>{part.name} (x{part.quantity})</span>
                            <span className="text-gray-500">
                              {part.eta ? `ETA: ${new Date(part.eta).toLocaleDateString('es-ES')}` : 'Sin ETA'}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </CardContent>
            </Card>
          )}

          {/* Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Resumen
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Total de servicios:</p>
                  <p className="font-medium">{order.services?.length || 0}</p>
                </div>
                <div>
                  <p className="text-gray-500">Pueden iniciarse:</p>
                  <p className="font-medium text-green-600">
                    {servicesWithoutParts.length + servicesWithAvailableParts.length}
                  </p>
                </div>
                <div>
                  <p className="text-gray-500">Esperando refacciones:</p>
                  <p className="font-medium text-yellow-600">{servicesWithPendingParts.length}</p>
                </div>
                <div>
                  <p className="text-gray-500">Refacciones pendientes:</p>
                  <p className="font-medium text-red-600">{pendingParts.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action explanation */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-800 mb-2">¿Qué sucederá al iniciar el trabajo?</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              {canStartImmediately && (
                <li>• Los servicios sin refacciones o con refacciones disponibles comenzarán inmediatamente</li>
              )}
              {hasPendingParts && (
                <li>• Los servicios con refacciones pendientes quedarán en estado "Esperando Refacciones"</li>
              )}
              <li>• Podrás gestionar las refacciones pendientes desde el panel de cada servicio</li>
              <li>• Recibirás notificaciones cuando lleguen las refacciones</li>
            </ul>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button 
            onClick={() => handleStartWork()}
            disabled={isStarting}
            className="min-w-[120px]"
          >
            {isStarting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isStarting ? 'Iniciando...' : 'Iniciar Trabajo'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
