import React from 'react';
import { notFound } from 'next/navigation';
import { getOrderById } from '../_actions/getOrders';
import OrderDetailClient from './client';

interface PageProps {
  params: {
    orderId: string;
  };
}

export default async function OrderDetailPage({ params }: PageProps) {
  const { orderId } = params;

  const orderResponse = await getOrderById(orderId);

  if (!orderResponse?.success || !orderResponse.data) {
    notFound();
  }

  return (
    <section className="flex flex-col min-h-[90vh]">
      <OrderDetailClient order={orderResponse.data} />
    </section>
  );
}
