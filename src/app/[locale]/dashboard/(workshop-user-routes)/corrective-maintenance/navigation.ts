import { 
  BarChart3, 
  FileText, 
  Wrench, 
  History, 
  Bell,
  Settings,
  Home
} from 'lucide-react';

export interface NavigationItem {
  title: string;
  href: string;
  icon: any;
  description: string;
  badge?: string;
}

export const correctiveMaintenanceNavigation: NavigationItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard/corrective-maintenance/dashboard',
    icon: Home,
    description: 'Vista general y métricas del mantenimiento correctivo',
  },
  {
    title: 'Órdenes',
    href: '/dashboard/corrective-maintenance',
    icon: FileText,
    description: 'Gestionar órdenes de mantenimiento correctivo',
  },
  {
    title: 'Métricas',
    href: '/dashboard/corrective-maintenance?tab=metrics',
    icon: BarChart3,
    description: 'KPIs y análisis de rendimiento',
  },
  {
    title: 'Servicios Activos',
    href: '/dashboard/corrective-maintenance?status=in-progress',
    icon: Wrench,
    description: 'Servicios en progreso y pendientes',
    badge: 'En Progreso',
  },
  {
    title: 'Historial',
    href: '/dashboard/corrective-maintenance?status=completed',
    icon: History,
    description: '<PERSON>rden<PERSON> completadas y historial',
  },
  {
    title: 'Notificaciones',
    href: '/dashboard/corrective-maintenance/notifications',
    icon: Bell,
    description: 'Alertas y notificaciones del sistema',
  },
];

export const quickActions = [
  {
    title: 'Nueva Orden',
    description: 'Crear una nueva orden de mantenimiento correctivo',
    action: 'create-order',
    icon: FileText,
    color: 'bg-blue-500',
  },
  {
    title: 'Diagnóstico Rápido',
    description: 'Completar diagnóstico para orden existente',
    action: 'quick-diagnosis',
    icon: Wrench,
    color: 'bg-green-500',
  },
  {
    title: 'Ver Métricas',
    description: 'Revisar KPIs y rendimiento del taller',
    action: 'view-metrics',
    icon: BarChart3,
    color: 'bg-purple-500',
  },
];

export const statusFilters = [
  { value: 'all', label: 'Todas las órdenes', count: 0 },
  { value: 'pending', label: 'Pendientes', count: 0 },
  { value: 'diagnosed', label: 'Diagnosticadas', count: 0 },
  { value: 'quoted', label: 'Cotizadas', count: 0 },
  { value: 'approved', label: 'Aprobadas', count: 0 },
  { value: 'in-progress', label: 'En Progreso', count: 0 },
  { value: 'completed', label: 'Completadas', count: 0 },
  { value: 'cancelled', label: 'Canceladas', count: 0 },
];

export const priorityLevels = [
  { value: 'low', label: 'Baja', color: 'bg-gray-100 text-gray-800' },
  { value: 'medium', label: 'Media', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'high', label: 'Alta', color: 'bg-red-100 text-red-800' },
  { value: 'urgent', label: 'Urgente', color: 'bg-red-200 text-red-900' },
];

export const serviceCategories = [
  { value: 'brakes', label: 'Frenos', icon: '🛑' },
  { value: 'tires', label: 'Llantas', icon: '🛞' },
  { value: 'suspension', label: 'Suspensión', icon: '🔧' },
  { value: 'engine', label: 'Motor', icon: '⚙️' },
  { value: 'transmission', label: 'Transmisión', icon: '🔄' },
  { value: 'electrical', label: 'Eléctrico', icon: '⚡' },
  { value: 'bodywork', label: 'Carrocería', icon: '🚗' },
  { value: 'other', label: 'Otros', icon: '🔨' },
];

export const workshopCapabilities = [
  'Diagnóstico computarizado',
  'Reparación de frenos',
  'Cambio de llantas',
  'Reparación de suspensión',
  'Reparación de motor',
  'Reparación de transmisión',
  'Reparación eléctrica',
  'Trabajo de carrocería',
  'Pintura',
  'Soldadura',
];

export const slaTargets = {
  diagnosis: 4, // hours
  quotation: 24, // hours
  approval: 48, // hours
  simpleRepair: 8, // hours
  complexRepair: 72, // hours
  partsArrival: 168, // hours (1 week)
};

export const notificationTypes = [
  {
    type: 'sla_warning',
    label: 'SLA en Riesgo',
    color: 'bg-yellow-100 text-yellow-800',
    icon: '⚠️',
  },
  {
    type: 'parts_arrived',
    label: 'Refacciones Disponibles',
    color: 'bg-green-100 text-green-800',
    icon: '📦',
  },
  {
    type: 'approval_pending',
    label: 'Aprobación Pendiente',
    color: 'bg-orange-100 text-orange-800',
    icon: '⏳',
  },
  {
    type: 'order_completed',
    label: 'Orden Completada',
    color: 'bg-blue-100 text-blue-800',
    icon: '✅',
  },
  {
    type: 'payment_due',
    label: 'Pago Pendiente',
    color: 'bg-purple-100 text-purple-800',
    icon: '💰',
  },
];

export const reportTypes = [
  {
    id: 'daily-summary',
    name: 'Resumen Diario',
    description: 'Resumen de actividades del día',
    format: ['PDF', 'Excel'],
  },
  {
    id: 'weekly-performance',
    name: 'Rendimiento Semanal',
    description: 'Métricas de rendimiento de la semana',
    format: ['PDF', 'Excel'],
  },
  {
    id: 'monthly-analysis',
    name: 'Análisis Mensual',
    description: 'Análisis completo del mes',
    format: ['PDF', 'Excel', 'PowerPoint'],
  },
  {
    id: 'sla-compliance',
    name: 'Cumplimiento de SLA',
    description: 'Reporte de cumplimiento de SLA',
    format: ['PDF', 'Excel'],
  },
  {
    id: 'customer-satisfaction',
    name: 'Satisfacción del Cliente',
    description: 'Reporte de satisfacción y NPS',
    format: ['PDF', 'Excel'],
  },
];

export const defaultMetricsPeriods = [
  { value: 'today', label: 'Hoy' },
  { value: 'week', label: 'Esta Semana' },
  { value: 'month', label: 'Este Mes' },
  { value: 'quarter', label: 'Este Trimestre' },
  { value: 'year', label: 'Este Año' },
  { value: 'custom', label: 'Período Personalizado' },
];

export const kpiTargets = {
  slaCompliance: 95, // percentage
  approvalRate: 85, // percentage
  customerSatisfaction: 8.5, // NPS score
  averageRepairTime: 24, // hours
  firstTimeFixRate: 90, // percentage
  partsAvailability: 95, // percentage
};

export const currencyFormat = {
  locale: 'es-MX',
  currency: 'MXN',
  minimumFractionDigits: 2,
  maximumFractionDigits: 2,
};

export const dateFormats = {
  short: 'dd/MM/yyyy',
  long: 'dd/MM/yyyy HH:mm',
  time: 'HH:mm',
  monthYear: 'MM/yyyy',
};

export const fileUploadLimits = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'video/mp4', 'video/webm'],
  maxFiles: 5,
};
