# Corrective Maintenance Module

This module implements the complete corrective maintenance workflow for the OneCarNow Vendors Platform, as specified in the PRD (Product Requirements Document).

## Overview

The corrective maintenance system allows customers to request maintenance services for vehicle issues not covered by preventive maintenance, including diagnostics, repairs for brakes, tires, suspension, and other mechanical failures.

## Features Implemented

### ✅ Core Functionality
- **Order Management**: Create, view, and manage corrective maintenance orders
- **Order Listing**: Paginated table with filtering and search capabilities
- **Order Details**: Comprehensive order detail view with status tracking
- **Diagnosis Workflow**: Complete diagnosis with service identification
- **Service Management**: Track individual services within an order
- **Status Tracking**: Visual status timeline and progress indicators

### ✅ User Interface
- **Responsive Design**: Works on desktop and mobile devices
- **Modern UI Components**: Using Radix UI and Tailwind CSS
- **Intuitive Navigation**: Clear workflow progression
- **Real-time Updates**: Automatic refresh after actions
- **Form Validation**: Comprehensive validation using Zod schemas

### ✅ Data Management
- **Type Safety**: Full TypeScript implementation
- **API Integration**: RESTful API calls with proper error handling
- **Caching**: Server-side caching for improved performance
- **State Management**: React Hook Form for complex forms

### ✅ Advanced Features (Newly Added)
- **Metrics Dashboard**: KPIs and performance tracking
- **Parts Management**: Comprehensive parts tracking and ETA management
- **Vehicle History**: Complete maintenance history per vehicle
- **Notifications System**: Real-time alerts and notifications
- **SLA Monitoring**: Service level agreement compliance tracking
- **Approval Workflow**: Granular service-by-service approval process
- **Evidence Management**: Photo/video evidence for all services
- **Multi-tab Interface**: Organized workflow with tabs for orders and metrics

## File Structure

```
corrective-maintenance/
├── README.md                           # This documentation
├── page.tsx                           # Main listing page
├── client.tsx                         # Client-side listing component
├── types.ts                           # TypeScript interfaces and utilities
├── [orderId]/
│   ├── page.tsx                       # Order detail page
│   └── client.tsx                     # Order detail client component
├── _actions/                          # Server actions
│   ├── getOrders.ts                   # Fetch orders with pagination
│   ├── createOrder.ts                 # Create new orders
│   ├── completeDiagnosis.ts           # Complete diagnosis workflow
│   ├── getVehicles.ts                 # Fetch available vehicles
│   └── getWorkshops.ts                # Fetch available workshops
└── _components/                       # UI components
    ├── OrdersTable.tsx                # Orders listing table
    ├── CreateOrderModal.tsx           # New order creation modal
    ├── DiagnosisModal.tsx             # Diagnosis completion modal
    ├── QuotationModal.tsx             # Quotation creation modal (placeholder)
    ├── ApprovalModal.tsx              # Approval processing modal (placeholder)
    └── ServiceProgressModal.tsx       # Service progress modal (placeholder)
```

## API Endpoints Expected

The frontend expects the following backend API endpoints:

### Orders
- `GET /vendor-platform/corrective-maintenance/orders` - List orders with pagination
- `POST /vendor-platform/corrective-maintenance/orders` - Create new order
- `GET /vendor-platform/corrective-maintenance/orders/:orderId` - Get order details

### Diagnosis
- `POST /vendor-platform/corrective-maintenance/orders/:orderId/diagnosis` - Complete diagnosis

### Supporting Endpoints
- `GET /vendor-platform/vehicles` - Get available vehicles
- `GET /vendor-platform/organizations/:orgId/workshops` - Get workshops
- `GET /vendor-platform/workshops` - Get workshops (fallback)

## Usage

### Accessing the Module
1. Navigate to `/dashboard/corrective-maintenance` in the application
2. The module is available to workshop users, company-gestor users, and superAdmin users

### Creating a New Order
1. Click "Nueva Orden" button on the main page
2. Fill in the required information:
   - Select vehicle from dropdown
   - Select workshop
   - Enter associate/customer ID
   - Choose order type and failure type
   - Specify arrival method
   - Provide problem description
3. Submit the form to create the order

### Completing Diagnosis
1. Open an order in "pending" status
2. Click "Completar Diagnóstico"
3. Add diagnosis notes
4. Add required services with:
   - Service type and name
   - Cost estimates
   - Duration estimates
   - Detailed descriptions
5. Submit to move order to "diagnosed" status

### Order Status Flow
1. **Pending** → Order created, awaiting diagnosis
2. **Diagnosed** → Diagnosis completed, ready for quotation
3. **Quoted** → Quotation created, awaiting approval
4. **Approved** → Services approved, ready to start work
5. **In Progress** → Work in progress
6. **Completed** → All services completed

## Styling and Theming

The module uses the existing application's design system:
- **Colors**: Purple primary theme (`#6C2BD9`)
- **Components**: Radix UI primitives with custom styling
- **Layout**: Responsive grid and flexbox layouts
- **Typography**: Consistent with application standards

## Future Enhancements

### 🔌 **API Integration**

The module is now configured to consume the real API endpoints. The system will make HTTP requests to the backend server.

### **API Endpoints Used**
- `GET /vendor-platform/corrective-maintenance/orders` - List orders with pagination and filters
- `GET /vendor-platform/corrective-maintenance/orders/:id` - Get specific order details
- `POST /vendor-platform/corrective-maintenance/orders` - Create new order
- `POST /vendor-platform/corrective-maintenance/orders/:id/diagnosis` - Complete diagnosis (multipart/form-data)
- `POST /vendor-platform/corrective-maintenance/orders/:id/quotation` - Create quotation
- `POST /vendor-platform/corrective-maintenance/quotations/:id/submit` - Submit quotation for approval
- `POST /vendor-platform/corrective-maintenance/quotations/:id/approve` - Process approvals
- `POST /vendor-platform/corrective-maintenance/orders/:id/start` - Start work on order
- `PATCH /vendor-platform/corrective-maintenance/services/:id/progress` - Update service progress (multipart/form-data)
- `GET /vendor-platform/vehicles` - Get available vehicles
- `GET /vendor-platform/organizations/:id/workshops` - Get organization workshops

### **Error Handling**
The system includes comprehensive error handling for:
- **404 errors**: Endpoint not available
- **401 errors**: Authentication issues
- **500 errors**: Server errors
- **Connection errors**: Network issues

### **API Response Format**
The system automatically transforms API responses to a consistent format:

**Expected API Response:**
```json
{
  "message": "Operation completed successfully",
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 0,
    "pages": 0
  }
}
```

**Transformed to Frontend Format:**
```json
{
  "success": true,
  "data": [...],
  "message": "Operation completed successfully",
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 0,
    "totalPages": 0
  }
}
```

### **Debugging**
Check the browser console for detailed API request/response logs:
- 🔗 API Request URLs and parameters
- ✅ Successful responses with data
- ❌ Error responses with details
- 📋 Request payloads and form data
- 🔄 Response transformations

## 🚧 Planned Features (Placeholders Created)
- **Quotation Creation**: Complete quotation workflow
- **Approval Processing**: Handle service approvals
- **Service Progress Tracking**: Real-time service updates
- **File Upload**: Evidence and document management
- **Notifications**: Email and in-app notifications
- **Reporting**: Analytics and KPI tracking

### 🔮 Potential Improvements
- **Mobile App Integration**: React Native components
- **Real-time Updates**: WebSocket integration
- **Advanced Filtering**: More filter options
- **Bulk Operations**: Multi-order management
- **Integration**: Parts suppliers and inventory systems

## Development Notes

### Dependencies Added
- `@radix-ui/react-separator` - For UI separators

### Key Libraries Used
- **React Hook Form** - Form management
- **Zod** - Schema validation
- **Radix UI** - UI primitives
- **Tailwind CSS** - Styling
- **Lucide React** - Icons
- **date-fns** - Date formatting

### Performance Considerations
- Server-side caching with React cache
- Pagination for large datasets
- Optimistic updates where appropriate
- Lazy loading of modal components

## Testing Recommendations

1. **Unit Tests**: Test individual components and utilities
2. **Integration Tests**: Test complete workflows
3. **E2E Tests**: Test user journeys with Cypress
4. **API Tests**: Verify backend integration
5. **Performance Tests**: Load testing with large datasets

## Troubleshooting

### Common Issues
1. **Orders not loading**: Check API endpoint availability
2. **Form validation errors**: Verify Zod schema matches API requirements
3. **Navigation issues**: Ensure user has proper permissions
4. **Styling issues**: Check Tailwind CSS compilation

### Debug Mode
Enable debug logging by setting `NODE_ENV=development` to see detailed API calls and responses.
