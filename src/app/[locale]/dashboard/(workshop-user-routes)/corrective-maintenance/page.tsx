import React from 'react';
import CorrectiveMaintenanceClient from './client';
import { getOrders } from './_actions/getOrders';

interface PageProps {
  searchParams: {
    page?: string;
    limit?: string;
    status?: string;
    workshopId?: string;
    search?: string;
  };
}

export default async function CorrectiveMaintenancePage({ searchParams }: PageProps) {
  const page = searchParams.page ? parseInt(searchParams.page) : 1;
  const limit = searchParams.limit ? parseInt(searchParams.limit) : 10;
  const status = searchParams.status;
  const workshopId = searchParams.workshopId;
  const search = searchParams.search;

  const ordersData = await getOrders({
    page,
    limit,
    status,
    workshopId,
    search,
  });

  return (
    <section className="flex flex-col min-h-[90vh]">
      <div className="pb-4">
        <h1 className="font-bold text-3xl">Mantenimiento Correctivo</h1>
        <p className="text-gray-600 mt-2">
          Gestiona las solicitudes de mantenimiento correctivo de los vehículos
        </p>
      </div>

      <CorrectiveMaintenanceClient
        initialData={ordersData}
        initialFilters={{
          page,
          limit,
          status,
          workshopId,
          search,
        }}
      />
    </section>
  );
}
