'use client';

import { useState } from 'react';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Filter, Plus, BarChart3, History } from 'lucide-react';
import { CorrectiveMaintenanceOrder, ApiResponse, OrderFilters, ORDER_STATUS_LABELS } from './types';
import OrdersTable from './_components/OrdersTable';
import CreateOrderModal from './_components/CreateOrderModal';
import MetricsCard from './_components/MetricsCard';

interface CorrectiveMaintenanceClientProps {
  initialData: ApiResponse<CorrectiveMaintenanceOrder[]> | null;
  initialFilters: {
    page?: number;
    limit?: number;
    status?: string;
    workshopId?: string;
    search?: string;
  };
}

export default function CorrectiveMaintenanceClient({
  initialData,
  initialFilters
}: CorrectiveMaintenanceClientProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();



  const [filters, setFilters] = useState<OrderFilters>({
    status: initialFilters.status,
    search: initialFilters.search,
  });

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  const updateSearchParams = (newFilters: Partial<OrderFilters>) => {
    const params = new URLSearchParams(searchParams.toString());

    // Update or remove parameters
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && value !== '') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });

    // Reset to page 1 when filters change
    if (Object.keys(newFilters).some(key => key !== 'page')) {
      params.set('page', '1');
    }

    router.push(`${pathname}?${params.toString()}`);
  };

  const handleFilterChange = (key: keyof OrderFilters, value: string) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    updateSearchParams(newFilters);
  };

  const handleSearch = (searchTerm: string) => {
    handleFilterChange('search', searchTerm);
  };

  const clearFilters = () => {
    setFilters({});
    router.push(pathname);
  };

  const handlePageChange = (page: number) => {
    updateSearchParams({ page: page.toString() });
  };

  const handleOrderCreated = () => {
    setIsCreateModalOpen(false);
    // Refresh the page to show the new order
    router.refresh();
  };

  if (!initialData) {
    return (
      <div className="flex flex-col justify-center items-center py-10 space-y-4">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No se pudieron cargar las órdenes
          </h3>
          <p className="text-red-500 mb-4">
            Error al conectar con el servidor de mantenimiento correctivo
          </p>
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
          >
            Reintentar
          </Button>
        </div>
      </div>
    );
  }

  if (!initialData.success) {
    return (
      <div className="flex flex-col justify-center items-center py-10 space-y-4">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Error del servidor
          </h3>
          <p className="text-red-500 mb-4">
            {initialData.message}
          </p>
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
          >
            Reintentar
          </Button>
        </div>
      </div>
    );
  }

  // Mock metrics data - replace with actual API call
  const mockMetricsData = {
    totalOrders: initialData.data.length,
    completedOrders: initialData.data.filter(order => order.status === 'completed').length,
    inProgressOrders: initialData.data.filter(order => order.status === 'in-progress').length,
    pendingOrders: initialData.data.filter(order => ['pending', 'diagnosed', 'quoted'].includes(order.status)).length,
    averageCompletionTime: 18.5, // hours
    slaCompliance: 92.3, // percentage
    totalRevenue: initialData.data.reduce((sum, order) => sum + (order.totalEstimatedCost || 0), 0),
    averageOrderValue: initialData.data.length > 0
      ? initialData.data.reduce((sum, order) => sum + (order.totalEstimatedCost || 0), 0) / initialData.data.length
      : 0,
    approvalRate: 87.5, // percentage
    customerSatisfaction: 8.2, // NPS score
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button onClick={() => setIsCreateModalOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Nueva Orden
        </Button>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="orders" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="orders" className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            Órdenes
          </TabsTrigger>
          <TabsTrigger value="metrics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Métricas
          </TabsTrigger>
        </TabsList>

        <TabsContent value="orders" className="space-y-6">
          {/* Filters and Actions */}
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center flex-1">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Buscar por número de orden, vehículo..."
                  value={filters.search || ''}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Status Filter */}
              <Select
                value={filters.status || 'all'}
                onValueChange={(value) => handleFilterChange('status', value === 'all' ? '' : value)}
              >
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="Filtrar por estado" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los estados</SelectItem>
                  {Object.entries(ORDER_STATUS_LABELS).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Clear Filters */}
              {(filters.status || filters.search) && (
                <Button variant="outline" onClick={clearFilters}>
                  <Filter className="h-4 w-4 mr-2" />
                  Limpiar filtros
                </Button>
              )}
            </div>
          </div>

          {/* Orders Table */}
          <OrdersTable
            data={initialData.data}
            pagination={initialData.pagination}
            onPageChange={handlePageChange}
          />
        </TabsContent>

        <TabsContent value="metrics" className="space-y-6">
          <MetricsCard data={mockMetricsData} period="month" />
        </TabsContent>
      </Tabs>

      {/* Create Order Modal */}
      <CreateOrderModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleOrderCreated}
      />
    </div>
  );
}
