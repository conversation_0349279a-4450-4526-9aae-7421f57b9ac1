import { Suspense } from 'react';
import { Loader2 } from 'lucide-react';
import CorrectiveMaintenanceDashboard from '../_components/CorrectiveMaintenanceDashboard';
import { getOrders } from '../_actions/getOrders';

interface PageProps {
  searchParams: {
    period?: 'today' | 'week' | 'month' | 'year';
  };
}

async function DashboardContent({ period }: { period: 'today' | 'week' | 'month' | 'year' }) {
  try {
    // Fetch orders for the dashboard
    const ordersResponse = await getOrders({
      page: 1,
      limit: 50, // Get more orders for better metrics
    });

    if (!ordersResponse?.success) {
      return (
        <div className="flex flex-col justify-center items-center py-10 space-y-4">
          <div className="text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Error al cargar el dashboard
            </h3>
            <p className="text-red-500 mb-4">
              {ordersResponse?.message || 'No se pudieron cargar los datos'}
            </p>
          </div>
        </div>
      );
    }

    return (
      <CorrectiveMaintenanceDashboard 
        orders={ordersResponse.data} 
        period={period}
      />
    );
  } catch (error) {
    console.error('Error loading dashboard:', error);
    return (
      <div className="flex flex-col justify-center items-center py-10 space-y-4">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Error inesperado
          </h3>
          <p className="text-red-500 mb-4">
            No se pudo conectar con el servidor
          </p>
        </div>
      </div>
    );
  }
}

function DashboardSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <div className="flex items-center justify-between">
        <div>
          <div className="h-8 w-96 bg-gray-200 rounded animate-pulse mb-2"></div>
          <div className="h-4 w-64 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="h-10 w-32 bg-gray-200 rounded animate-pulse"></div>
      </div>

      {/* Quick Stats Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="bg-white p-6 rounded-lg border">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-8 w-16 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-3 w-20 bg-gray-200 rounded animate-pulse"></div>
              </div>
              <div className="h-12 w-12 bg-gray-200 rounded-full animate-pulse"></div>
            </div>
          </div>
        ))}
      </div>

      {/* Main Content Skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <div className="bg-white p-6 rounded-lg border">
            <div className="h-6 w-32 bg-gray-200 rounded animate-pulse mb-4"></div>
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="h-24 bg-gray-100 rounded animate-pulse"></div>
              ))}
            </div>
          </div>
        </div>
        <div className="lg:col-span-1">
          <div className="bg-white p-6 rounded-lg border">
            <div className="h-6 w-32 bg-gray-200 rounded animate-pulse mb-4"></div>
            <div className="space-y-3">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="h-16 bg-gray-100 rounded animate-pulse"></div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Recent Orders Skeleton */}
      <div className="bg-white p-6 rounded-lg border">
        <div className="h-6 w-40 bg-gray-200 rounded animate-pulse mb-4"></div>
        <div className="space-y-3">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-gray-200 rounded-full animate-pulse"></div>
                <div className="space-y-1">
                  <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
                  <div className="h-3 w-32 bg-gray-200 rounded animate-pulse"></div>
                </div>
              </div>
              <div className="text-right space-y-1">
                <div className="h-5 w-16 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-3 w-20 bg-gray-200 rounded animate-pulse"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default function CorrectiveMaintenanceDashboardPage({ searchParams }: PageProps) {
  const period = searchParams.period || 'month';

  return (
    <div className="container mx-auto py-6">
      <Suspense fallback={<DashboardSkeleton />}>
        <DashboardContent period={period} />
      </Suspense>
    </div>
  );
}
