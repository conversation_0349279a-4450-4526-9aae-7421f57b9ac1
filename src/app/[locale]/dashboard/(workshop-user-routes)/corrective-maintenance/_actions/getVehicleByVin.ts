'use server';

import { URL_API } from '@/constants';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
import { ApiResponse } from '../types';

interface VehicleData {
  _id: string;
  brand: string;
  model: string;
  year: number;
  carNumber: string;
  carPlates: {
    plates: string;
  };
  vin: string;
  color?: string;
  status: string;
  associates?: Array<{
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
  }>;
  organization?: {
    _id: string;
    name: string;
  };
}

export async function getVehicleByVin(vin: string): Promise<ApiResponse<VehicleData> | null> {
  const user = await getCurrentUser();
  if (!user) return null;

  try {
    console.log('🔗 Getting vehicle by VIN:', vin);

    // Use the correct search endpoint with VIN as search parameter
    const response = await axios.get(
      `${URL_API}/stock/search/`,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
        params: {
          search: vin, // VIN will be matched exactly in the backend
        },
      }
    );

    console.log('✅ Vehicle search response:', response.data);

    // The search endpoint returns an array, we need the first match
    const vehicles = response.data.data || response.data;
    
    if (vehicles && vehicles.length > 0) {
      // Find the exact VIN match (in case there are partial matches)
      const exactMatch = vehicles.find((vehicle: any) => 
        vehicle.vin && vehicle.vin.toUpperCase() === vin.toUpperCase()
      );
      
      if (exactMatch) {
        return {
          success: true,
          data: exactMatch,
          message: 'Vehículo encontrado exitosamente',
        };
      }
    }

    // No exact match found
    return {
      success: false,
      data: null as any,
      message: 'Vehículo no encontrado con este VIN',
    };

  } catch (error: any) {
    console.error('❌ Error getting vehicle by VIN:', error.response?.data || error);
    
    if (error.response?.status === 404) {
      return {
        success: false,
        data: null as any,
        message: 'Vehículo no encontrado con este VIN',
      };
    }

    if (error.response?.status === 401 || error.response?.status === 403) {
      return {
        success: false,
        data: null as any,
        message: 'Sin autorización para buscar vehículos',
      };
    }

    return {
      success: false,
      data: null as any,
      message: 'Error al buscar el vehículo',
    };
  }
}