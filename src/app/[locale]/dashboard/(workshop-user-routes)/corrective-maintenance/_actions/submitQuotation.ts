'use server';

import { URL_API } from '@/constants';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
import { ApiResponse, Quotation } from '../types';

export async function submitQuotation(
  quotationId: string
): Promise<ApiResponse<Quotation>> {
  const user = await getCurrentUser();

  if (!user) {
    return {
      success: false,
      data: null as any,
      message: 'Usuario no autenticado',
    };
  }

  try {
    console.log('🔗 Submitting quotation for approval:', quotationId);

    const response = await axios.post(
      `${URL_API}/vendor-platform/corrective-maintenance/quotations/${quotationId}/submit`,
      {},
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log('✅ Quotation submitted successfully:', response.data);

    // Transform API response to match our expected format
    const apiResponse = response.data;

    return {
      success: true,
      data: apiResponse.data || apiResponse,
      message: apiResponse.message || 'Cotización enviada para aprobación exitosamente',
    };
  } catch (error: any) {
    console.error('❌ Error submitting quotation:', error.response?.data || error);

    return {
      success: false,
      data: null as any,
      message: error.response?.data?.message || 'Error al enviar la cotización para aprobación',
    };
  }
}
