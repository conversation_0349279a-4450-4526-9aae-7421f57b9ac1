'use server';

import { URL_API } from '@/constants';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
import { ApiResponse, CorrectiveMaintenanceOrder } from '../types';

export async function forceStartWork(
  orderId: string,
  options?: {
    ignorePartsAvailability?: boolean;
    startOnlyAvailableServices?: boolean;
  }
): Promise<ApiResponse<CorrectiveMaintenanceOrder>> {
  const user = await getCurrentUser();

  if (!user) {
    return {
      success: false,
      data: null as any,
      message: 'Usuario no autenticado',
    };
  }

  try {
    console.log('🔗 Force starting work for order:', orderId, 'with options:', options);

    const response = await axios.post(
      `${URL_API}/vendor-platform/corrective-maintenance/orders/${orderId}/start`,
      {
        force: true,
        ignorePartsAvailability: options?.ignorePartsAvailability || true,
        startOnlyAvailableServices: options?.startOnlyAvailableServices || false,
      },
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log('✅ Work force started successfully:', response.data);

    // Transform API response to match our expected format
    const apiResponse = response.data;

    return {
      success: true,
      data: apiResponse.data || apiResponse,
      message: apiResponse.message || 'Trabajo iniciado exitosamente (forzado)',
    };
  } catch (error: any) {
    console.error('❌ Error force starting work:', error.response?.data || error);

    let errorMessage = 'Error al forzar el inicio del trabajo';
    
    if (error.response?.status === 404) {
      errorMessage = 'La orden no fue encontrada';
    } else if (error.response?.status === 401) {
      errorMessage = 'No autorizado para iniciar el trabajo';
    } else if (error.response?.status === 400) {
      errorMessage = error.response?.data?.message || 'La orden no está en un estado válido para iniciar el trabajo';
    } else if (error.response?.status === 500) {
      errorMessage = 'Error interno del servidor al iniciar el trabajo';
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = 'No se puede conectar con el servidor de la API';
    }

    return {
      success: false,
      data: null as any,
      message: errorMessage,
    };
  }
}
