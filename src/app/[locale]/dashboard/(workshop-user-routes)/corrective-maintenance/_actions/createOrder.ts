'use server';

import { URL_API } from '@/constants';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
import { CreateOrderRequest, ApiResponse, CorrectiveMaintenanceOrder } from '../types';

export async function createOrder(orderData: CreateOrderRequest): Promise<ApiResponse<CorrectiveMaintenanceOrder>> {
  const user = await getCurrentUser();

  if (!user) {
    return {
      success: false,
      data: null as any,
      message: 'Usuario no autenticado',
    };
  }

  try {
    console.log('🔗 Creating corrective maintenance order:', orderData);

    const response = await axios.post(
      `${URL_API}/vendor-platform/corrective-maintenance/orders`,
      orderData,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log('✅ Order created successfully:', response.data);

    // Transform API response to match our expected format
    const apiResponse = response.data;

    return {
      success: true,
      data: apiResponse.data || apiResponse,
      message: apiResponse.message || 'Orden creada exitosamente',
    };
  } catch (error: any) {
    console.error('❌ Error creating corrective maintenance order:', error.response?.data || error);

    return {
      success: false,
      data: null as any,
      message: error.response?.data?.message || 'Error al crear la orden de mantenimiento correctivo',
    };
  }
}
