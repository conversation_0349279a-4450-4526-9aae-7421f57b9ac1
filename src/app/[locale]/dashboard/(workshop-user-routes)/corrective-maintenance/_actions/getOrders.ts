'use server';

import { URL_API } from '@/constants';
import { cache } from 'react';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
import { CorrectiveMaintenanceOrder, ApiResponse } from '../types';

interface GetOrdersParams {
  page?: number;
  limit?: number;
  status?: string;
  workshopId?: string;
  search?: string;
}

export const getOrders = cache(async (params: GetOrdersParams = {}) => {
  const user = await getCurrentUser();

  if (!user) {
    return null;
  }

  // Real API call
  try {
    const url = new URL(`${URL_API}/vendor-platform/corrective-maintenance/orders`);

    // Add query parameters
    if (params.page) url.searchParams.append('page', params.page.toString());
    if (params.limit) url.searchParams.append('limit', params.limit.toString());
    if (params.status) url.searchParams.append('status', params.status);
    if (params.workshopId) url.searchParams.append('workshopId', params.workshopId);
    if (params.search) url.searchParams.append('search', params.search);

    const response = await axios.get(url.toString(), {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    // Transform API response to match our expected format
    const apiResponse = response.data;

    return {
      success: true,
      data: apiResponse.data || [],
      message: apiResponse.message || 'Órdenes obtenidas exitosamente',
      pagination: {
        page: apiResponse.pagination?.page || params.page || 1,
        limit: apiResponse.pagination?.limit || params.limit || 10,
        total: apiResponse.pagination?.total || 0,
        totalPages: apiResponse.pagination?.pages || apiResponse.pagination?.totalPages || 0,
      },
    } as ApiResponse<CorrectiveMaintenanceOrder[]>;
  } catch (error: any) {
    // Determine error message based on status code
    let errorMessage = 'Error al obtener las órdenes de mantenimiento correctivo';

    if (error.response?.status === 404) {
      errorMessage = 'El endpoint de mantenimiento correctivo no está disponible';
    } else if (error.response?.status === 401) {
      errorMessage = 'No autorizado para acceder a las órdenes de mantenimiento correctivo';
    } else if (error.response?.status === 500) {
      errorMessage = 'Error interno del servidor al obtener las órdenes';
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = 'No se puede conectar con el servidor de la API';
    }

    return {
      success: false,
      data: [],
      message: errorMessage,
      pagination: {
        page: params.page || 1,
        limit: params.limit || 10,
        total: 0,
        totalPages: 0,
      },
    } as ApiResponse<CorrectiveMaintenanceOrder[]>;
  }
});

export const getOrderById = cache(async (orderId: string) => {
  const user = await getCurrentUser();
  if (!user) return null;

  try {
    const response = await axios.get(
      `${URL_API}/vendor-platform/corrective-maintenance/orders/${orderId}`,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    // Transform API response to match our expected format
    const apiResponse = response.data;

    return {
      success: true,
      data: apiResponse.data || apiResponse,
      message: apiResponse.message || 'Orden obtenida exitosamente',
    } as ApiResponse<CorrectiveMaintenanceOrder>;
  } catch (error: any) {

    let errorMessage = 'Error al obtener la orden de mantenimiento correctivo';

    if (error.response?.status === 404) {
      errorMessage = 'La orden de mantenimiento correctivo no fue encontrada';
    } else if (error.response?.status === 401) {
      errorMessage = 'No autorizado para acceder a esta orden';
    }

    return {
      success: false,
      data: null as any,
      message: errorMessage,
    } as ApiResponse<CorrectiveMaintenanceOrder>;
  }
});
