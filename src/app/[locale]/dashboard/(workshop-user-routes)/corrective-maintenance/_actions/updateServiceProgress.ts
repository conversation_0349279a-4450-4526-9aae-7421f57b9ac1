'use server';

import { URL_API } from '@/constants';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
import { UpdateServiceProgressRequest, ApiResponse, CorrectiveService } from '../types';

export async function updateServiceProgress(
  serviceId: string,
  progressData: UpdateServiceProgressRequest
): Promise<ApiResponse<CorrectiveService>> {
  const user = await getCurrentUser();

  if (!user) {
    return {
      success: false,
      data: null as any,
      message: 'Usuario no autenticado',
    };
  }

  try {
    console.log('🔗 Updating service progress:', serviceId, progressData);

    // Create FormData for file upload
    const formData = new FormData();
    formData.append('status', progressData.status);

    if (progressData.notes) {
      formData.append('notes', progressData.notes);
    }

    if (progressData.evidence) {
      progressData.evidence.forEach((file) => {
        formData.append('evidence', file);
      });
    }

    const response = await axios.patch(
      `${URL_API}/vendor-platform/corrective-maintenance/services/${serviceId}/progress`,
      formData,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    console.log('✅ Service progress updated successfully:', response.data);

    // Transform API response to match our expected format
    const apiResponse = response.data;

    return {
      success: true,
      data: apiResponse.data || apiResponse,
      message: apiResponse.message || 'Progreso del servicio actualizado exitosamente',
    };
  } catch (error: any) {
    console.error('❌ Error updating service progress:', error.response?.data || error);

    return {
      success: false,
      data: null as any,
      message: error.response?.data?.message || 'Error al actualizar el progreso del servicio',
    };
  }
}
