'use server';

import { URL_API } from '@/constants';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
import { ApiResponse, CorrectiveService } from '../types';

export interface ServiceExecutionRequest {
  action: 'start' | 'pause' | 'resume' | 'complete';
  notes?: string;
  completionNotes?: string;
  evidence?: File[];
  timestamp: string;
}

export interface ServiceExecutionResponse {
  service: CorrectiveService;
  executionLog: {
    action: string;
    timestamp: string;
    notes?: string;
    evidence?: string[];
  };
}

export async function updateServiceExecution(
  serviceId: string,
  orderId: string,
  executionData: ServiceExecutionRequest
): Promise<ApiResponse<ServiceExecutionResponse>> {
  const user = await getCurrentUser();

  if (!user?.accessToken) {
    return {
      success: false,
      data: {} as ServiceExecutionResponse,
      message: 'Usuario no autenticado',
    };
  }

  try {
    console.log('🔧 Updating service execution:', { serviceId, orderId, action: executionData.action });

    // Prepare FormData for multipart upload
    const formData = new FormData();
    formData.append('action', executionData.action);
    formData.append('orderId', orderId);
    formData.append('timestamp', executionData.timestamp);

    if (executionData.notes) {
      formData.append('notes', executionData.notes);
    }

    if (executionData.completionNotes) {
      formData.append('completionNotes', executionData.completionNotes);
    }

    // Add evidence files if provided
    if (executionData.evidence && executionData.evidence.length > 0) {
      executionData.evidence.forEach((file) => {
        formData.append('evidence', file);
      });
    }

    const response = await axios.post(
      `${URL_API}/vendor-platform/corrective-maintenance/services/${serviceId}/execution`,
      formData,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    console.log('✅ Service execution updated successfully:', response.data);

    return {
      success: true,
      data: response.data.data || response.data,
      message: response.data.message || 'Ejecución del servicio actualizada exitosamente',
    };

  } catch (error: any) {
    console.error('❌ Error updating service execution:', error);

    if (error.response?.status === 404) {
      return {
        success: false,
        data: {} as ServiceExecutionResponse,
        message: 'Servicio no encontrado',
      };
    }

    if (error.response?.status === 400) {
      return {
        success: false,
        data: {} as ServiceExecutionResponse,
        message: error.response.data?.message || 'Datos de ejecución inválidos',
      };
    }

    if (error.response?.status === 409) {
      return {
        success: false,
        data: {} as ServiceExecutionResponse,
        message: 'El servicio no puede cambiar a este estado en este momento',
      };
    }

    return {
      success: false,
      data: {} as ServiceExecutionResponse,
      message: error.response?.data?.message || 'Error inesperado al actualizar la ejecución del servicio',
    };
  }
}

/**
 * Get service execution history
 */
export async function getServiceExecutionHistory(
  serviceId: string
): Promise<ApiResponse<any[]>> {
  const user = await getCurrentUser();

  if (!user?.accessToken) {
    return {
      success: false,
      data: [],
      message: 'Usuario no autenticado',
    };
  }

  try {
    const response = await axios.get(
      `${URL_API}/vendor-platform/corrective-maintenance/services/${serviceId}/execution-history`,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      }
    );

    return {
      success: true,
      data: response.data.data || response.data,
      message: 'Historial de ejecución obtenido exitosamente',
    };

  } catch (error: any) {
    console.error('❌ Error getting service execution history:', error);

    return {
      success: false,
      data: [],
      message: error.response?.data?.message || 'Error al obtener el historial de ejecución',
    };
  }
}

/**
 * Start multiple services in sequence
 */
export async function startServicesInSequence(
  orderId: string,
  serviceIds: string[]
): Promise<ApiResponse<any>> {
  const user = await getCurrentUser();

  if (!user?.accessToken) {
    return {
      success: false,
      data: {},
      message: 'Usuario no autenticado',
    };
  }

  try {
    const response = await axios.post(
      `${URL_API}/vendor-platform/corrective-maintenance/orders/${orderId}/start-services-sequence`,
      { serviceIds },
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    return {
      success: true,
      data: response.data.data || response.data,
      message: 'Secuencia de servicios iniciada exitosamente',
    };

  } catch (error: any) {
    console.error('❌ Error starting services sequence:', error);

    return {
      success: false,
      data: {},
      message: error.response?.data?.message || 'Error al iniciar la secuencia de servicios',
    };
  }
}

/**
 * Get real-time service progress
 */
export async function getServiceProgress(
  serviceId: string
): Promise<ApiResponse<{
  service: CorrectiveService;
  progress: {
    percentage: number;
    timeElapsed: number; // in minutes
    estimatedTimeRemaining: number; // in minutes
    currentPhase: string;
  };
}>> {
  const user = await getCurrentUser();

  if (!user?.accessToken) {
    return {
      success: false,
      data: {} as any,
      message: 'Usuario no autenticado',
    };
  }

  try {
    const response = await axios.get(
      `${URL_API}/vendor-platform/corrective-maintenance/services/${serviceId}/progress`,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      }
    );

    return {
      success: true,
      data: response.data.data || response.data,
      message: 'Progreso del servicio obtenido exitosamente',
    };

  } catch (error: any) {
    console.error('❌ Error getting service progress:', error);

    return {
      success: false,
      data: {} as any,
      message: error.response?.data?.message || 'Error al obtener el progreso del servicio',
    };
  }
}

/**
 * Complete service with evidence and comments
 */
export interface ServiceCompletionRequest {
  completionNotes: string;
  workPerformed: string;
  evidence: File[];
  recommendations?: string;
  nextMaintenanceDate?: string;
  partsUsed?: string;
  completedAt: string;
}

export async function completeServiceWithEvidence(
  serviceId: string,
  orderId: string,
  completionData: ServiceCompletionRequest
): Promise<ApiResponse<CorrectiveService>> {
  const user = await getCurrentUser();

  if (!user?.accessToken) {
    return {
      success: false,
      data: {} as CorrectiveService,
      message: 'Usuario no autenticado',
    };
  }

  try {
    console.log('🏁 Completing service with evidence:', { serviceId, orderId });

    // Prepare FormData for multipart upload
    const formData = new FormData();
    formData.append('orderId', orderId);
    formData.append('completionNotes', completionData.completionNotes);
    formData.append('workPerformed', completionData.workPerformed);
    formData.append('completedAt', completionData.completedAt);

    if (completionData.recommendations) {
      formData.append('recommendations', completionData.recommendations);
    }

    if (completionData.nextMaintenanceDate) {
      formData.append('nextMaintenanceDate', completionData.nextMaintenanceDate);
    }

    if (completionData.partsUsed) {
      formData.append('partsUsed', completionData.partsUsed);
    }

    // Add evidence files
    completionData.evidence.forEach((file) => {
      formData.append('evidence', file);
    });

    const response = await axios.post(
      `${URL_API}/vendor-platform/corrective-maintenance/services/${serviceId}/complete`,
      formData,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    console.log('✅ Service completed with evidence successfully:', response.data);

    return {
      success: true,
      data: response.data.data || response.data,
      message: response.data.message || 'Servicio completado exitosamente',
    };

  } catch (error: any) {
    console.error('❌ Error completing service with evidence:', error);

    if (error.response?.status === 404) {
      return {
        success: false,
        data: {} as CorrectiveService,
        message: 'Servicio no encontrado',
      };
    }

    if (error.response?.status === 400) {
      return {
        success: false,
        data: {} as CorrectiveService,
        message: error.response.data?.message || 'Datos de finalización inválidos',
      };
    }

    return {
      success: false,
      data: {} as CorrectiveService,
      message: error.response?.data?.message || 'Error inesperado al completar el servicio',
    };
  }
}
