'use server';

import { URL_API } from '@/constants';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
import { ProcessApprovalRequest, ApiResponse, Quotation } from '../types';

export async function processApproval(
  quotationId: string,
  approvalData: ProcessApprovalRequest
): Promise<ApiResponse<Quotation>> {
  const user = await getCurrentUser();

  if (!user) {
    return {
      success: false,
      data: null as any,
      message: 'Usuario no autenticado',
    };
  }

  try {
    console.log('🔗 Processing approval for quotation:', quotationId, approvalData);

    const response = await axios.post(
      `${URL_API}/vendor-platform/corrective-maintenance/quotations/${quotationId}/approve`,
      approvalData,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log('✅ Approval processed successfully:', response.data);

    // Transform API response to match our expected format
    const apiResponse = response.data;

    return {
      success: true,
      data: apiResponse.data || apiResponse,
      message: apiResponse.message || 'Aprobación procesada exitosamente',
    };
  } catch (error: any) {
    console.error('❌ Error processing approval:', error.response?.data || error);

    return {
      success: false,
      data: null as any,
      message: error.response?.data?.message || 'Error al procesar la aprobación',
    };
  }
}
