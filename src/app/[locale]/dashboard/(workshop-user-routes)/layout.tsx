import getUserById from "@/actions/getUserById";
import { redirect } from "next/navigation";



export default async function CompanyUserLayout({ children }: { children: React.ReactNode }) {

  // Check if user type is company, if not, redirect to dashboard

  const user = await getUserById();

  // Check if user is workshop, company-gestor or superAdmin
  if (!user || (user.userType !== 'workshop' && user.userType !== 'company-gestor' && user.userType !== 'superAdmin')) {
    console.log("User not authorized for workshop routes:", user?.userType);
    return redirect('/dashboard');
  }

  return (
    <>
      {children}
    </>
  )
}