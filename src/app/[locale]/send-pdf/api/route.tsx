import { NextRequest, NextResponse } from 'next/server';
import { sendEmail } from './send-mail';

const isLocal = process.env.NEXT_PUBLIC_IS_LOCAL === 'true';

export const POST = async (req: NextRequest) => {
  if (!isLocal) {
    return NextResponse.json({ status: 'error', message: 'Not allowed' }, { status: 500 });
  }

  const body = await req.json();

  const { to, bcc, subject, html, attachments, carNumber, realEmail } = body;

  // Convertir el contenido de base64 a Buffer
  const processedAttachments = attachments.map((attachment: any) => ({
    ...attachment,
    content: Buffer.from(attachment.content, 'base64'), // Convertir base64 a Buffer
  }));

  console.log('attachments after processing', processedAttachments);

  const fs = require('fs');
  const path = require('path');

  // validate if the file exists and check if the email received is already in the file
  // to avoid sending the email again

  const root = process.cwd();
  const filePath = path.join(root, 'emails.json');
  let emails = [];

  if (fs.existsSync(filePath)) {
    emails = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
  }
  console.log('emails', emails);
  console.log('to email', to);
  console.log('real email', realEmail);
  const emailExists = emails.find(
    (email: any) => email.realEmail === realEmail || email.carNumber === carNumber
  );

  if (emailExists) {
    console.log('Email already sent', emailExists);
    return NextResponse.json({ status: 'error', message: 'Email already sent' }, { status: 500 });
  }

  const res = await sendEmail({
    to,
    bcc,
    subject,
    html,
    attachments: processedAttachments,
  });

  // if the file exists, read it and add the new email
  // if the file does not exist, create it and add the new email

  if (fs.existsSync(filePath)) {
    emails = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
  }

  emails.push({
    email: to,
    realEmail,
    carNumber,
  });

  fs.writeFileSync(filePath, JSON.stringify(emails, null, 2));

  if (!res) {
    return NextResponse.json({ status: 'error' }, { status: 500 });
  }

  return NextResponse.json({ status: 'ok' }, { status: 200 });
};

// export async function POST2(req: NextRequest) {
//   try {
//     const formData = await req.formData();
//     const file = formData.getAll('files')[0];
//     const filePath = `./public/file/${file}`;
//     await pump(file.stream(), fs.createWriteStream(filePath));
//     return NextResponse.json({ status: 'success', data: file.size });
//   } catch (e) {
//     return NextResponse.json({ status: 'fail', data: e });
//   }
// }
