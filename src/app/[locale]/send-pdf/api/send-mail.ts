import nodemailer from 'nodemailer';

export const transporter = nodemailer.createTransport({
  // host: 'smtp.gmail.com',
  // port: 465,
  host: process.env.SMTP_HOST,
  pool: true,
  secure: true,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
});

export const sendEmail = async ({
  bcc,
  subject,
  html,
  to,
  attachments,
}: {
  to: string;
  subject: string;
  html: string;
  bcc?: string | string[];
  attachments?: { filename: string; content: Buffer }[];
}) => {
  try {
    const res = await transporter.sendMail({
      from: process.env.SMTP_FROM,
      to,
      bcc,
      subject,
      html,
      attachments,
    });
    console.log('Email sent to: ', res.accepted, res.response);
    return true;
  } catch (error: any) {
    console.log('Error sending email: ', error);
    // console.log('To: ', to, 'Subject: ', subject);
    return false;
  }
};
