/* eslint-disable jsx-a11y/alt-text */
'use client';
import React from 'react';
import { Document, Page, Text, View, StyleSheet, Font, pdf, Image } from '@react-pdf/renderer';
import axios from 'axios';
// import data from './data.json';
import signatureImg from './s (1).png';
import dynamic from 'next/dynamic';
import JSZip from 'jszip';
import moment from 'moment';
import 'moment/locale/es';
// import emailsjson from '../../../emails.json';

// const PDFViewer = dynamic(() => import('@react-pdf/renderer').then((mod) => mod.PDFViewer), { ssr: false });
const PDFViewer = dynamic(() => import('@react-pdf/renderer').then((mod) => mod.PDFViewer), {
  ssr: false,
  loading: () => <p>Loading...</p>,
});
// const PDFViewer = dynamic(() => import('@react-pdf/renderer'), {
//   ssr: false,
//   loading: () => <p>Loading...</p>,
// });
// import signatureImg from "../img/s.png";
// import signatureImg from "../img/s.png";

Font.register({ family: 'Helvetica', fonts: [] });
Font.register({ family: 'Helvetica-Oblique', fonts: [] });
Font.register({ family: 'Helvetica-BoldOblique', fonts: [] });
Font.register({ family: 'Helvetica-Bold', fonts: [] });

const data = [
  {
    domicilio: 'Calle 1',
    nombre: 'Jorge',
    fecha: '2024-06-29',
    email: '<EMAIL>',
    contractNumber: '1234',
    clabe: '1831919111011',
    fechaEntrega: '2024-06-29',
    cesionDate: '2024-06-29',
  },
  // abra una lista de muchos objetos aqui, con los datos de cada notificacion
];
// const dataaa = data.slice(0, 1);
const dataaa = data;

const firstData = dataaa[0];

type Props = typeof firstData;
// Create styles
const styles = StyleSheet.create({
  viewer: {
    width: '100%',
    height: '100vh',
  },

  img: {
    // left: "40px",
    top: '30px',
    height: '120px',
    width: '120px',
    position: 'fixed' as unknown as any,
  },

  viewBackground: {
    position: 'absolute',
    zIndex: '0',
    bottom: '10px',
    left: '170px',
    height: '100px',
    width: '35%',
    // transform: 'rotate(10deg)',
  },

  page: {
    paddingVertical: 60,
    // paddingBottom: 50,
    // paddingBottom: 30,
    flexDirection: 'column',
    alignContent: 'center',
  },

  body: {
    marginLeft: '10%',
    marginRight: '10%',
    // marginTop: "20px",
    rowGap: '20px' as unknown as number,
    flexDirection: 'column',
  },

  center: {
    flexDirection: 'column',
  },

  header: {
    width: '100vw',
  },

  titleText: {
    fontSize: '12px',
    marginTop: '50px',
    textAlign: 'center',
  },

  subTitleText: {
    fontSize: '12px',
    textAlign: 'center',
    fontFamily: 'Helvetica-Bold',
  },

  date: {
    alignSelf: 'flex-end',
    fontSize: '12px',
    fontWeight: 'ultralight',
    flexDirection: 'column',
  },

  text: {
    fontSize: 12,
    textAlign: 'justify',
    // lineHeight: "2px",
  },

  textBold: {
    fontSize: 12,
    fontFamily: 'Helvetica-Bold',
  },
  underlineText: {
    fontFamily: 'Helvetica-Bold',
    fontStyle: 'Italic',
    textDecoration: 'underline',
  },

  title: {
    fontWeight: 'bold',
    fontSize: '14px',
    // fontFamily: "Helvetica-Bold",
    textAlign: 'center',
  },

  signatures: {
    flexDirection: 'row',
    width: '100%',
    marginTop: '40px',
  },

  signatureBox: {
    flexDirection: 'column',
    width: '100%',
    rowGap: '2px',
  },

  signatureLine: {
    borderTop: '1px solid black',
    // marginBottom: "5px",
  },

  signatureText: {
    fontSize: 12,
  },

  footer: {
    position: 'absolute',
    bottom: 2,
    marginTop: '50px',
    height: '60px',
    width: '100vw',
  },
});

const months: any = {
  '01': 'Enero',
  '02': 'Febrero',
  '03': 'Marzo',
  '04': 'Abril',
  '05': 'Mayo',
  '06': 'Junio',
  '07': 'Julio',
  '08': 'Agosto',
  '09': 'Septiembre',
  10: 'Octubre',
  11: 'Noviembre',
  12: 'Diciembre',
};

const cesionDatesObj: any = {
  '28-Jun-24': '28 de junio de 2024',
  '3-Sep-24': '3 de septiembre de 2024',
  null: null,
};

function NofityPDF(props: Props) {
  // removeSpaces('J ')
  const fecha = props.fechaEntrega;
  console.log('props', props);
  // console.log('contractNumber', props.contractNumber);
  console.log('fechaEntrega', fecha);
  const [year, month, day] = fecha.split('-');
  // console.log('fecha', day, month, year, months[month]);
  // const todayDateFormat2 = moment().format('DD [de] MM [de] YYYY');
  const todayDate = moment().format('YYYY-MM-DD');
  const [todayYear, todayMonth, todayDay] = todayDate.split('-');
  // make that the first letter of the month is uppercase
  const todayDateFormat = `${todayDay} de ${months[todayMonth]} de ${todayYear}`;
  return (
    <Document>
      <Page style={styles.page} size="A4" wrap>
        <View style={styles.body}>
          {/* <Image src={img} style={styles.viewBackground} /> */}
          <Text style={styles.title}>Notificación de Cesión</Text>
          <Text style={styles.date}>
            Ciudad de México a {props.cesionDate ? cesionDatesObj[props.cesionDate] : todayDateFormat}
          </Text>

          <View style={{ rowGap: '5px' }}>
            <Text style={styles.text}>Domicilio: {props.domicilio}</Text>

            <Text style={styles.text}>Atención: {props.nombre}</Text>
          </View>

          <Text
            style={{
              fontFamily: 'Helvetica-Bold',
              fontSize: 12,
              alignSelf: 'flex-end',
            }}
          >
            Referencia: Notificación de Cesión
          </Text>
          <Text style={styles.text}>Estimados señores:</Text>
          <Text style={styles.text}>
            Hacemos referencia al Contrato de Arrendamiento de Vehículo, de fecha {day} de {months[month]} de{' '}
            {year} (según el mismo pueda ser modificado, reformado o adicionado de tiempo en tiempo, el &quot;
            <Text style={{ fontFamily: 'Helvetica-Bold' }}>Contrato de Arrendamiento</Text>&quot;), celebrado
            entre E-MKT GOODS DE MÉXICO, S.A.P.I. DE C.V. (el &quot;
            <Text style={{ fontFamily: 'Helvetica-Bold' }}>Arrendador</Text> &quot; y/u &quot;
            <Text style={{ fontFamily: 'Helvetica-Bold' }}>OCN</Text>&quot;), como arrendador, y{' '}
            {props.nombre}, como arrendatario (el &quot;
            <Text style={{ fontFamily: 'Helvetica-Bold' }}>Arrendatario</Text>
            &quot;). Salvo que se definan en la presente, cualquier término utilizado con mayúscula inicial, y
            no definido expresamente en la presente notificación, será utilizado tal y como se le define en el
            Contrato de Arrendamiento y/o el Contrato de Fideicomiso OCN (según dicho término se define más
            adelante).
          </Text>
          <Text style={styles.text}>
            Por este medio les informamos que con fecha{' '}
            {props.cesionDate ? cesionDatesObj[props.cesionDate] : todayDateFormat}, el OCN celebro, en su
            carácter de Fideicomitente y Aportante, un Convenio de Aportación al Fideicomiso Irrevocable de
            Administración y Fuente de Pago, identificado con el número F/11268 (el “
            <Text style={{ fontFamily: 'Helvetica-Bold' }}>Contrato de Fideicomiso OCN </Text>” y/o el “
            <Text style={{ fontFamily: 'Helvetica-Bold' }}>Fideicomiso OCN</Text>”) con Banco Monex, S.A.,
            Institución de Banca Múltiple, Monex Grupo Financiero, en su calidad de Fiduciario (el
            “Fiduciario”), conforme al cual, el Arrendador afectó, transmitió y cedió en forma total en favor
            del Beneficiario, en su carácter de Fiduciario del Fideicomiso OCN, para beneficio del Patrimonio
            del Fideicomiso, sin ninguna reserva, la propiedad de todos y cada uno de los derechos derivados
            de o relacionados con el Contrato de Arrendamiento, pero excluyendo las obligaciones del
            Arrendador, bajo dicho Contrato de Arrendamiento, por lo que a partir de la presente Notificación
            de Cesión el Arrendatario deberá de realizar cualesquier pagos derivados del Contrato de
            Arrendamiento en la siguiente cuenta bancaria:
          </Text>
          <Text style={styles.text}>
            Sin perjuicio de lo anterior, se les informa que todos los términos del Contrato de Arrendamiento
            continúan en pleno vigor y efecto, sin que la celebración del Contrato de Fideicomiso OCN o del
            Convenio de Aportación modifique, altere o extinga los derechos y obligaciones del Arrendador y
            del Arrendatario, bajo el Contrato de Arrendamiento.
          </Text>

          <View style={{ marginLeft: '30px' }}>
            <Text style={styles.text}>Banco: BMONEX</Text>
            <Text style={styles.text}>Titular: E-MKT GOODS DE MEXICO S.A.P.I DE C.V.</Text>
            <Text style={styles.text}>CLABE: {props.clabe}</Text>
          </View>

          {/* LA PRESENTE */}

          <Text style={styles.text}>
            La presente notificación se hace de manera electrónica en términos de la cláusula Vigésima Cuarta
            del Contrato de Arrendamiento. Mediante el primer pago realizado en la cuenta bancaria señalada en
            la presente notificación, el Arrendatario está de acuerdo en que dicha situación se interprete
            como que ha sido debidamente notificado y ha aceptado la mencionada transmisión de derechos para
            todos los efectos aplicables.
          </Text>

          <Text style={styles.text}>
            Sin perjuicio de lo anterior, se les informa que todos los términos del Contrato de Arrendamiento
            continúan en pleno vigor y efecto, sin que la celebración del Contrato de Fideicomiso OCN o del
            Convenio de Aportación modifique, altere o extinga los derechos y obligaciones del Arrendador y
            del Arrendatario, bajo el Contrato de Arrendamiento.
          </Text>

          <Text style={{ ...styles.text, width: '100%', textAlign: 'center' }}>
            Atentamente,{' '}
            <Text style={{ ...styles.textBold, marginLeft: '10px' }}>
              E-MKT GOODS DE MEXICO, S.A.P.I. DE C.V
            </Text>
          </Text>
          <View
            style={{
              rowGap: '0px' as unknown as number,
              width: '100%',
              flexDirection: 'column',
              justifyContent: 'center',
            }}
          >
            {/* <Text
              style={{
                ...styles.title,
                fontFamily: "Helvetica-Bold",
                // fontStyle: "Italic",
                textDecoration: "underline",
              }}
            >
              ACEPTA DE CONFORMIDAD
            </Text> */}

            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'center',
                width: '100%',
                marginTop: '50px',
              }}
            >
              <View
                style={{
                  width: '100%',
                  flexDirection: 'column',
                  alignItems: 'center',
                  marginTop: '50px',
                }}
              >
                <Image src={signatureImg.src} style={styles.viewBackground} />

                <Text style={styles.signatureText}>Por: ______________________________</Text>
                <Text style={styles.signatureText}>Nombre: Mairon Esteban Sandoval Gómez</Text>
                <Text style={styles.signatureText}>Cargo: Apoderado</Text>
              </View>
            </View>

            <View style={styles.signatures}>
              {/* <View
                style={{
                  ...styles.signatureBox,
                  width: '100%',
                  justifyContent: 'center',
                  // marginTop: "50px",
                }}
              >

                <Text style={styles.signatureText}>Por: ___________________________</Text>
                <Text style={styles.signatureText}>Nombre: Mairon Esteban Sandoval Gómez</Text>
                <Text style={styles.signatureText}>Cargo: Apoderado</Text>
              </View> */}
              {/* <View
                style={{
                  ...styles.signatureBox,
                  marginTop: "50px",
                }}
              >
                <Text style={styles.signatureText}>
                  Por: ___________________________
                </Text>
                <Text style={styles.signatureText}>Nombre: {props.nombre}</Text>
                <Text style={styles.signatureText}>Cargo: Apoderado</Text>
              </View> */}
            </View>
          </View>

          {/* <View style={styles.center}></View> */}
        </View>
      </Page>
    </Document>
  );
}
const isLocal = process.env.NEXT_PUBLIC_IS_LOCAL === 'true';
export default function NotifyPDFPage() {
  // const data = require('./data.json');

  const find3Sep = data.find((item: any) => item.cesionDate === '3-Sep-24');
  // console.log('find3Sep', find3Sep);

  // const find28Jun = data.find((item) => item.cesionDate === '28-Jun-24');
  // console.log('find28Jun', find28Jun);
  // console.log('data length', data.length);

  const props = find3Sep || data[0];
  return (
    <>
      {isLocal && (
        <>
          <button
            onClick={sendAllPdfs}
            style={{
              width: '100px',
              height: '40px',
              backgroundColor: 'blue',
              color: 'white',
              borderRadius: '5px',
            }}
          >
            Enviar todos los PDF
          </button>
          <PDFViewer style={styles.viewer}>
            <NofityPDF {...props} />
          </PDFViewer>
        </>
      )}
    </>
  );
}

async function sendAllPdfs() {
  const zip = new JSZip();

  let index = 0;

  // let dataFilter = data.filter((item) => item.cesionDate === '3-Sep-24');

  // let dataFilter28Jun = data.filter((item) => item.cesionDate === '28-Jun-24');

  for (const item of dataaa) {
    // Crear el documento PDF para cada elemento
    const doc = <NofityPDF {...item} />;
    // if (index >= 1) break;
    // Generar el PDF como Blob
    const pdfBlob = await pdf(doc).toBlob();

    // download locally in the browser

    // const url = URL.createObjectURL(pdfBlob);
    // const a = document.createElement('a');
    // a.href = url;
    // a.download = `notificacion-${item.nombre}.pdf`;
    // a.click();

    // URL.revokeObjectURL(url);

    // Convertir el Blob en un ArrayBuffer para enviar
    const arrayBuffer = await pdfBlob.arrayBuffer();

    zip.file(`${index + 1}-${item.nombre}.pdf`, arrayBuffer);

    // Convertir el ArrayBuffer a base64
    const base64String = arrayBufferToBase64(arrayBuffer);

    // Crear el objeto de attachment
    const attachment = {
      filename: `notificacion-${item.nombre}.pdf`,
      content: base64String, // enviar como base64
      encoding: 'base64', // especificar que está en base64
    };

    // Enviar la solicitud POST al backend con los archivos adjuntos

    try {
      await axios.post('/send-pdf/api', {
        to: item.email,
        // to: '<EMAIL>',
        realEmail: item.email,
        carNumber: item.contractNumber,
        // bcc: ['<EMAIL>'],
        bcc: ['<EMAIL>', '<EMAIL>'],
        subject: `Notificación de Cesión - Contrato ${item.contractNumber}`,
        html: `
          <p>Hola ${item.nombre},</p>
          <p>Adjunto encontrarás el aviso de cesión.</p>
          <p>Saludos,</p>
          <p>OneCarNow</p>
        `,
        attachments: [attachment], // enviar los archivos adjuntos
      });
      console.log('enviado a ', item.contractNumber, item.email, index);
    } catch (error) {
      console.log('No enviado a ', item.contractNumber, item.email, index);
    }

    index++;
  }

  zip.generateAsync({ type: 'blob' }).then((content) => {
    const url = window.URL.createObjectURL(content);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'notificaciones-cesión.zip';
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
  });
}

// Función para convertir ArrayBuffer a base64
function arrayBufferToBase64(buffer: ArrayBuffer) {
  let binary = '';
  const bytes = new Uint8Array(buffer);
  const len = bytes.byteLength;
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return window.btoa(binary);
}
