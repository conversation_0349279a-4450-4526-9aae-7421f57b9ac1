'use client';
import InputPassword from '@/components/Inputs/InputPassword';
import { changePassSchema } from '@/validatorSchemas/changePassSchema';
import { Form, Formik, FormikValues } from 'formik';
import axios from 'axios';
import Image from 'next/image';
import { useSearchParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { LuCamera } from 'react-icons/lu';
import Swal from 'sweetalert2';
import useCurrentUrl from '../../../hooks/useCurrentUrl';
import Spinner from '@/components/Loading/Spinner';
import { URL_API } from '@/constants';
import { acceptInvitation } from './actions';
import { useToast } from '@chakra-ui/react';

export default function AcceptInvitation() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const toast = useToast();

  const code = searchParams.get('code');

  const [error, setError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  const verifyToken = async () => {
    setIsLoading(true);
    try {
      const result = await axios.get(`${URL_API}/auth/validateToken?code=${code}&method=inv`);
      return result;
    } catch (err: any) {
      setErrorMessage(err.response?.data?.message || 'Hubo un error');
      setError(true);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (code) {
      verifyToken();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [code]);

  const onSubmit = async (form: FormikValues) => {

    const result = await acceptInvitation(form, code!);
    console.log('result', result);
    if (result.success) {

      toast({
        title: 'Contraseña creada con éxito',
        description: 'Ahora puedes iniciar sesión',
        status: 'success',
        position: 'top',
        duration: 6000,
        isClosable: true,
      })

      return router.push('/');
    }


    toast({
      title: 'Error',
      description: result.error,
      position: 'top',
      status: 'error',
      duration: 6000,
      isClosable: true,
    })

    return result
  };


  if (!code)
    return (
      <div>
        <p>no es posible acceder sin una invitación</p>
        <div className="flex">
          <p>ir a inicio de sesión</p>
          <button onClick={() => router.push('/')}>Regresar</button>
        </div>
      </div>
    );

  if (isLoading) return <Spinner />;

  if (error)
    return (
      <div className="w-[100%] h-[100vh] flex justify-center items-center">
        <p className="text-[40px]">{errorMessage}</p>
      </div>
    );

  if (!isLoading && !error)
    return (
      <div className="w-[100%] h-[100vh] relative bg-[#FAFAFF] overflow-hidden ">
        <div className="w-[100%] flex flex-col items-center gap-4 pt-[5vh] relative">
          <div
            className="
            w-[158px] h-[158px]
            border-[#9E8EFF]
            border-[20px]
            absolute
            rounded-full
            z-10
            top-[10vh]
            left-[-79px]
          "
          />

          <div className=" w-[297px] h-[140px] ">
            <Image alt="logo" width="1000" height="1000" priority src="/images/Logo.png" />
          </div>

          <div className="relative">
            <div
              className="
                  w-[70px] h-[70px]
                  border-[15px]
                  border-[#47EB84BD]
                  absolute
                  rounded-full
                  z-10
                  top-[-35px]
                  right-[-35px]
                  "
            />
            <div
              className="
              w-[509px]
              min-h-[563px]
              flex
              flex-col
              items-center
              rounded
              relative
              p-[30px]
              z-20
              gap-[25px]
              bg-[#FFFFFF]
              border-[#EAECEE]
              border-[1px] "
            >
              <p>Crear contraseña</p>

              <div className="w-[140px] h-[140px] rounded-full relative border-[2px] border-[#EAECEE]">
                <Image
                  alt="logo"
                  width="1000"
                  height="1000"
                  priority
                  className="rounded-full border-[1px] border-[#EAECEE] "
                  src="/images/avatar.jpg"
                />
                <div
                  className="
                  w-[40px]
                  h-[40px]
                  bg-[#5800F7]
                  flex justify-center items-center
                  rounded-full
                  absolute
                  right-[4px]
                  bottom-0
                  "
                >
                  <LuCamera color="white" size={24} />
                </div>
              </div>

              <p>
                Tu contraseña debe tener mínimo 8 caracteres y al menos una mayúscula, una minúscula y un
                número.
              </p>

              <Formik
                initialValues={{ password: '', confirmPassword: '' }}
                onSubmit={onSubmit}
                validationSchema={changePassSchema}
              >
                {({ isValid, dirty }) => {
                  const validate = dirty && isValid;
                  return (
                    <Form className="flex flex-col w-full gap-4">
                      <InputPassword name="password" label="Contraseña" />
                      <InputPassword name="confirmPassword" label="Confirmar Contraseña" />
                      <button
                        type="submit"
                        disabled={!validate}
                        className={`
                          ${!validate ? 'bg-[#9CA3AF]' : 'bg-[#5800F7]'}
                          h-[40px]
                          rounded
                          text-white
                        `}
                      >
                        Confirmar cuenta
                      </button>
                    </Form>
                  );
                }}
              </Formik>
            </div>
          </div>
        </div>

        <div
          className="
            w-[155px] h-[155px]
            bg-[#47EB84]
            absolute
            rounded-full
            z-10
            bottom-[-40px]
            left-[-35px]
          "
        />

        <div
          className="
            w-[70px] h-[70px]
            border-[#9E8EFF]
            absolute
            rounded-full
            z-10
            bottom-[45px]
            right-[26px]
            border-[15px]
          "
        />
      </div>
    );
}
