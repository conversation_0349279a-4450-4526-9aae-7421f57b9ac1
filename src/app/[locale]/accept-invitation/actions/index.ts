'use server';
import { URL_API } from '@/constants';
import axios from 'axios';
import { FormikValues } from 'formik';

export async function acceptInvitation(form: FormikValues, code: string) {

  try {
    const result = await axios.post(`${URL_API}/vendor/accept-invitation?code=${code}`, form);

    return {
      success: true,
      data: result.data
    }
  } catch (error: any) {
    console.log('error', error?.response?.data);
    return {
      success: false,
      // data: error.response?.data?.message || 'Hubo un error',
      data: error?.response?.data,
      error: error?.response?.data?.message || 'Hubo un error'
    }
  }

}