@tailwind base;
  @tailwind components;
  @tailwind utilities;

  @layer base {
    :root {
      --background: 0 0% 100%;
      --foreground: 222.2 84% 4.9%;

      --card: 0 0% 100%;
      --card-foreground: 222.2 84% 4.9%;

      --popover: 0 0% 100%;
      --popover-foreground: 222.2 84% 4.9%;

      --primary: 222.2 47.4% 11.2%;
      --primary-foreground: 210 40% 98%;

      --secondary: 210 40% 96.1%;
      --secondary-foreground: 222.2 47.4% 11.2%;

      --muted: 210 40% 96.1%;
      --muted-foreground: 215.4 16.3% 46.9%;

      --accent: 210 40% 96.1%;
      --accent-foreground: 222.2 47.4% 11.2%;

      --destructive: 0 84.2% 60.2%;
      --destructive-foreground: 210 40% 98%;

      --border: 214.3 31.8% 91.4%;
      --input: 214.3 31.8% 91.4%;
      --ring: 222.2 84% 4.9%;

      --radius: 0.5rem;
    }

    .dark {
      --background: 222.2 84% 4.9%;
      --foreground: 210 40% 98%;

      --card: 222.2 84% 4.9%;
      --card-foreground: 210 40% 98%;

      --popover: 222.2 84% 4.9%;
      --popover-foreground: 210 40% 98%;

      --primary: 210 40% 98%;
      --primary-foreground: 222.2 47.4% 11.2%;

      --secondary: 217.2 32.6% 17.5%;
      --secondary-foreground: 210 40% 98%;

      --muted: 217.2 32.6% 17.5%;
      --muted-foreground: 215 20.2% 65.1%;

      --accent: 217.2 32.6% 17.5%;
      --accent-foreground: 210 40% 98%;

      --destructive: 0 62.8% 30.6%;
      --destructive-foreground: 210 40% 98%;

      --border: 217.2 32.6% 17.5%;
      --input: 217.2 32.6% 17.5%;
      --ring: 212.7 26.8% 83.9%;
    }
  }

  @layer base {
    * {
      @apply border-border;
    }
    body {
      @apply bg-background text-foreground;
    }
  }

  .swal2-custom-class {
    z-index: 999999999999999 !important
  }
  /* 
  @layer utilities {
    @variants responsive {
      Hide scrollbar for Chrome, Safari and Opera
      .no-scrollbar::-webkit-scrollbar {
          display: none;
      }
  
      Hide scrollbar for IE, Edge and Firefox
      .no-scrollbar {
          -ms-overflow-style: none;  IE and Edge
          scrollbar-width: none;  Firefox
      }
    }
  } */
  
  .no-scrollbar{
    --webkit-scrollbar: none !important;
    -ms-overflow-style: none !important;
    scrollbar-width: none !important;
  }
  
  .custom-scroll {
    scrollbar-width: thin;
    /* scrollbar-color: #4a5568 #2d3748; */
  }
  
  .custom-scroll::-webkit-scrollbar {
    height: 12px;
  }
  
  .custom-scroll::-webkit-scrollbar-thumb {
    background-color: rgb(160, 165, 175);
    border-radius: 6px;
  }
  
  .custom-scroll::-webkit-scrollbar-track {
    background-color: rgb(219, 222, 229);
    border-radius: 6px;
  }
  
  .sweetalert-custom-info-icon{
    color: #58007F !important;
    border-color: #58007F !important;
  }
  
  div:where(.swal2-container) .swal2-input{
    height: 40px !important;
  }
/*  */
.rbc-agenda-table>tbody>tr {
  background-color: transparent !important;
}

.rbc-agenda-time-cell,
.rbc-agenda-event-cell {
  background-color: var(--event-color, #3B82F6) !important;
  color: white !important;
  border-radius: 4px;
}

.rbc-agenda-date-cell {
  background-color: transparent !important;
}


/* Estilos solo para la Agenda */
.calendar-container.agenda-view .rbc-agenda-table>tbody>tr {
  background-color: transparent !important;
}

/* Estilos para Semana, Mes, Día */
.calendar-container.week-view .rbc-event,
.calendar-container.day-view .rbc-event,
.calendar-container.month-view .rbc-event {
  background-color: var(--event-color, #3B82F6) !important;
}

.rbc-agenda-view {
  /*  overflow visible: */
  overflow: visible !important;
}

.rbc-month-row {
  overflow: visible !important;
}
/* Contenedor del loader */
.rbc-time-view {
  position: relative !important;
}

.calendar-loader {
  position: absolute;
  inset: 0;
  background: rgba(255, 255, 255, 0.5);
  z-index: 1;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
.rbc-current-time-indicator {
  height: 2px !important;
}