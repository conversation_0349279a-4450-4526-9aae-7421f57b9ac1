"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { ArrowLeft, Edit, Users } from "lucide-react"
// import type { Crew, City } from "@/lib/data"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { CrewForm } from "@/app/_components/crews/crew-form"
import { City, Crew } from "@/constants/companyService"

interface CrewDetailProps {
  crew: Crew
  city: City
}

export function CrewDetail({ crew, city }: CrewDetailProps) {
  const [isEditing, setIsEditing] = useState(false)
  const router = useRouter()
  const params = useParams<{ locale: string }>()

  const handleSuccess = () => {
    setIsEditing(false)
    router.refresh()
  }

  return (
    <div>
      <div className="flex items-center mb-6">
        <Link href={`/${params.locale}/dashboard/cities/${city._id}`} passHref>
          <Button variant="ghost" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Volver a {city.name}
          </Button>
        </Link>
      </div>

      <Card className="bg-white">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-muted-foreground mb-1">Cuadrilla de {city.name}</p>
              <CardTitle className="text-2xl">{crew.name}</CardTitle>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={crew.active ? "default" : "destructive"}>{crew.active ? "Activa" : "Inactiva"}</Badge>
              <Button variant="outline" size="sm" onClick={() => setIsEditing(!isEditing)}>
                <Edit className="h-4 w-4 mr-2" />
                {isEditing ? "Cancelar" : "Editar"}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isEditing ? (
            <CrewForm crew={crew} cityId={city._id} onSuccess={handleSuccess} />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium text-sm text-muted-foreground">Ciudad</h3>
                <p>{city.name}</p>
              </div>
              <div>
                <h3 className="font-medium text-sm text-muted-foreground">Estado</h3>
                <p>{crew.active ? "Activa" : "Inactiva"}</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

