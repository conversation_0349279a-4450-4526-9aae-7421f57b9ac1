"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { createCrew, updateCrew } from "@/lib/city-actions copy"
// import type { Crew } from "@/lib/data"
import { useCurrentUser } from "@/Providers/CurrentUserProvider"
import { Crew } from "@/constants/companyService"

const crewSchema = z.object({
  name: z.string().min(2, "El nombre debe tener al menos 2 caracteres"),
  active: z.boolean().default(true),
})

type CrewFormValues = z.infer<typeof crewSchema>

interface CrewFormProps {
  crew?: Crew
  cityId: string
  onSuccess?: (crewId: string) => void
}

export function CrewForm({ crew, cityId, onSuccess }: CrewFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const { user } = useCurrentUser<'company'>();

  const form = useForm<CrewFormValues>({
    resolver: zodResolver(crewSchema),
    defaultValues: {
      name: crew?.name || "",
      // members: crew?.members || 1,
      active: crew?.active ?? true,
    },
  })

  async function onSubmit(values: CrewFormValues) {
    setIsLoading(true)

    try {
      if (crew) {
        // Update existing squad
        const updatedCrew = await updateCrew(crew._id, values)
        if (updatedCrew && onSuccess) {
          onSuccess(updatedCrew._id)
        }
      } else {
        // Create new crew
        // console.log('Creating new squad', {
        //   ...values,
        //   cityId,
        //   companyId: user.companyId,
        // })
        // return;
        const newCrew = await createCrew({
          ...values,
          cityId,
          companyId: user.companyId,
        })
        if (newCrew && onSuccess) {
          onSuccess(newCrew._id)
        }
      }

      router.refresh()
    } catch (error) {
      console.error("Error saving squad:", error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nombre</FormLabel>
              <FormControl>
                <Input placeholder="Nombre de la cuadrilla" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="active"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
              <FormControl>
                <Checkbox checked={field.value} defaultChecked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>Activa</FormLabel>
              </div>
            </FormItem>
          )}
        />

        <Button type="submit" disabled={isLoading} className="w-full">
          {isLoading ? "Guardando..." : crew ? "Actualizar Equipo" : "Crear Equipo"}
        </Button>
      </form>
    </Form>
  )
}

