"use client"

import { useState } from "react"
import { usePathname, useRouter } from "next/navigation"
import { Plus } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { CrewForm } from "@/app/_components/crews/crew-form"

interface CreateCrewButtonProps {
  cityId: string
}

export function CreateCrewButton({ cityId }: CreateCrewButtonProps) {
  const [open, setOpen] = useState(false)
  const router = useRouter()

  const pathname = usePathname();

  const handleSuccess = (crewId: string) => {
    setOpen(false)
    router.push(`${pathname}/crews/${crewId}`)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Nueva Cuadrilla
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Crear Nueva Cuadrilla</DialogTitle>
        </DialogHeader>
        <CrewForm cityId={cityId} onSuccess={handleSuccess} />
      </DialogContent>
    </Dialog>
  )
}

