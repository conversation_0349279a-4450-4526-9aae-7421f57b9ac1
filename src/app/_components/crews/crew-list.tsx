import Link from "next/link"
// import type { Crew } from "@/lib/data"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowR<PERSON>, Users } from "lucide-react"
import { Crew } from "@/constants/companyService"

interface CrewListProps {
  crews: Crew[];
  cityId: string;
  locale: string;
}

export function CrewList({ crews, cityId, locale }: CrewListProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {crews.map((crew) => (
        <Card key={crew._id} className="overflow-hidden bg-white">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-xl">{crew.name}</CardTitle>
              <Badge variant={crew.active ? "default" : "destructive"}>{crew.active ? "Activa" : "Inactiva"}</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 mb-4">

              {/* <p className="text-sm flex items-center text-muted-foreground">
                <Users className="h-4 w-4 mr-1" />
                <span className="font-medium">Miembros:</span> {crew.members}
              </p> */}
            </div>
            <Link href={`/${locale}/dashboard/cities/${cityId}/crews/${crew._id}`} passHref>
              <Button variant="outline" className="w-full">
                Ver detalles
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </CardContent>
        </Card>
      ))}

      {crews.length === 0 && (
        <div className="col-span-full text-center py-10">
          <p className="text-muted-foreground">No hay cuadrillas disponibles para esta ciudad</p>
        </div>
      )}
    </div>
  )
}

