"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Plus } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { CityForm } from "@/app/_components/city/city-form"
import { useParams } from "next/navigation"

export function CreateCityButton() {
  const [open, setOpen] = useState(false)
  const router = useRouter();
  const params = useParams<{ locale: string }>()

  const handleSuccess = (cityId: string) => {
    setOpen(false)
    router.push(`/${params.locale}/dashboard/cities/${cityId}`)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Nueva Ciudad
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Crear Nueva Ciudad</DialogTitle>
        </DialogHeader>
        <CityForm onSuccess={handleSuccess} />
      </DialogContent>
    </Dialog>
  )
}

