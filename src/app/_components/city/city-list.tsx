import Link from "next/link"
// import type { City } from "@/lib/data"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"
import { type City } from "@/constants/companyService"

interface CityListProps {
  cities: City[]
  locale: string
}

export function CityList({ cities, locale }: CityListProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {cities.map((city) => (
        <Card key={city._id} className="overflow-hidden bg-white">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-xl">{city.name}</CardTitle>
              <Badge variant={city.active ? "default" : "destructive"} status={city.active ? "active" : "inactive"}>{city.active ? "Activa" : "Inactiva"}</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 mb-4">
              <p className="text-sm text-muted-foreground">
                <span className="font-medium">Estado:</span> {city.state}
              </p>
              <p className="text-sm text-muted-foreground">
                <span className="font-medium">País:</span> {city.country}
              </p>
            </div>
            <Link href={`/${locale}/dashboard/cities/${city._id}`} passHref>
              <Button variant="outline" className="w-full">
                Ver detalles
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </CardContent>
        </Card>
      ))}

      {cities.length === 0 && (
        <div className="col-span-full text-center py-10">
          <p className="text-muted-foreground">No hay ciudades disponibles</p>
        </div>
      )}
    </div>
  )
}

