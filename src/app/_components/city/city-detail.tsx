"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { ArrowLeft, Edit } from "lucide-react"
// import type { City } from "@/lib/data"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { CityForm } from "@/app/_components/city/city-form"
import { City } from "@/constants/companyService"

interface CityDetailProps {
  city: City
}

export function CityDetail({ city }: CityDetailProps) {
  const [isEditing, setIsEditing] = useState(false)
  const router = useRouter();
  const params = useParams<{ locale: string }>()

  const handleSuccess = () => {
    setIsEditing(false)
    router.refresh()
  }

  return (
    <div>
      <div className="flex items-center mb-6">
        <Link href={`/${params.locale}/dashboard/cities`} passHref>
          <Button variant="ghost" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Volver a Ciudades
          </Button>
        </Link>
      </div>

      <Card className="bg-white">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <CardTitle className="text-2xl">{city.name}</CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant={city.active ? "default" : "destructive"} status={city.active ? "active" : "inactive"} >{city.active ? "Activa" : "Inactiva"}</Badge>
              <Button variant="outline" size="sm" onClick={() => setIsEditing(!isEditing)}>
                <Edit className="h-4 w-4 mr-2" />
                {isEditing ? "Cancelar" : "Editar"}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isEditing ? (
            <CityForm city={city} onSuccess={handleSuccess} />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium text-sm text-muted-foreground">Estado</h3>
                <p>{city.state}</p>
              </div>
              <div>
                <h3 className="font-medium text-sm text-muted-foreground">País</h3>
                <p>{city.country}</p>
              </div>
                <div>
                <h3 className="font-medium text-sm text-muted-foreground">Estado</h3>
                <p>{city.active ? "Activa" : "Inactiva"}</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

