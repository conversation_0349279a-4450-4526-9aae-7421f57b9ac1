"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
// import { createCity, updateCity } from "@/lib/city-actions"
import { createCity, updateCity } from "@/lib/city-actions copy"
import { City } from "@/constants/companyService"
import { useCurrentUser } from "@/Providers/CurrentUserProvider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { getStateByLabel, stateSelectOptions } from "@/constants/company"

const citySchema = z.object({
  name: z.string().min(2, "El nombre debe tener al menos 2 caracteres"),
  state: z.string().min(2, "El estado debe tener al menos 2 caracteres"),
  country: z.string().min(2, "El país debe tener al menos 2 caracteres"),
  // add validation to postal code to be only numbers
  active: z.boolean().default(true),
})

type CityFormValues = z.infer<typeof citySchema>

interface CityFormProps {
  city?: City
  onSuccess?: (cityId: string) => void
}

export function CityForm({ city, onSuccess }: CityFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter();
  const { user } = useCurrentUser<'company'>();

  const form = useForm<CityFormValues>({
    resolver: zodResolver(citySchema),
    defaultValues: {
      name: city?.name || "",
      state: city?.state || "",
      country: city?.country || "MX",
      active: city?.active ?? true,
    },
  })

  async function onSubmit(values: CityFormValues) {
    setIsLoading(true)

    try {
      if (city) {
        // Update existing city
        const updatedCity = await updateCity(city._id, values)
        if (updatedCity && onSuccess) {
          onSuccess(updatedCity._id)
        }
      } else {
        // Create new city
        const newCity = await createCity({
          ...values,
          companyId: user.companyId,
        })
        if (newCity && onSuccess) {
          onSuccess(newCity._id)
        }
      }

      router.refresh()
    } catch (error: any) {
      console.log("Error saving city:", error?.response?.data || error.message)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nombre</FormLabel>
              <FormControl>
                <Input placeholder="Nombre de la ciudad" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="state"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Estado</FormLabel>
              <FormControl>
                {/* <Input placeholder="Estado" {...field} /> */}
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Seleccione un estado" />
                  </SelectTrigger>
                  <SelectContent>
                    {stateSelectOptions.map((state) => (
                      <SelectItem key={state.value} value={state.value}>
                        {state.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="country"
          render={({ field }) => {
            return (
              <FormItem>
                <FormLabel>País</FormLabel>
                <FormControl>

                  <Select value={field.value} defaultValue={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Seleccione un país" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="MX">México</SelectItem>
                      <SelectItem value="US">Estados Unidos</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )
          }}
        />


        <FormField
          control={form.control}
          name="active"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
              <FormControl>
                <Checkbox checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>Activa</FormLabel>
              </div>
            </FormItem>
          )}
        />

        <Button type="submit" disabled={isLoading} className="w-full">
          {isLoading ? "Guardando..." : city ? "Actualizar Ciudad" : "Crear Ciudad"}
        </Button>
      </form>
    </Form>
  )
}

