"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { ArrowLeft, Edit, MapPin } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { NeighborhoodForm } from "@/app/_components/neighborhood/neighborhood-form"
import { City, Crew, Neighborhood } from "@/constants/companyService"

interface NeighborhoodDetailProps {
  neighborhood: Neighborhood
  crew: Crew
  city: City
}

export function NeighborhoodDetail({ neighborhood, crew, city }: NeighborhoodDetailProps) {
  const [isEditing, setIsEditing] = useState(false)
  const router = useRouter()
  const params = useParams<{ locale: string }>()

  const handleSuccess = () => {
    setIsEditing(false)
    router.refresh()
  }

  return (
    <div>

      <Card className="bg-white">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-muted-foreground mb-1">
                Colonia de {city.name} - Cuadrilla {crew.name}
              </p>
              <CardTitle className="text-2xl">{neighborhood.name}</CardTitle>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={neighborhood.active ? "default" : "destructive"}>
                {neighborhood.active ? "Activa" : "Inactiva"}
              </Badge>
              <Button variant="outline" size="sm" onClick={() => setIsEditing(!isEditing)}>
                <Edit className="h-4 w-4 mr-2" />
                {isEditing ? "Cancelar" : "Editar"}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isEditing ? (
            <NeighborhoodForm
              neighborhood={neighborhood}
              cityId={city._id}
              crewId={crew._id}
              onSuccess={handleSuccess}
            />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium text-sm text-muted-foreground">Ciudad</h3>
                <p>{city.name}</p>
              </div>
              <div>
                <h3 className="font-medium text-sm text-muted-foreground">Cuadrilla</h3>
                <p>{crew.name}</p>
              </div>
              <div>
                <h3 className="font-medium text-sm text-muted-foreground">Estado</h3>
                <p>{neighborhood.active ? "Activa" : "Inactiva"}</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

