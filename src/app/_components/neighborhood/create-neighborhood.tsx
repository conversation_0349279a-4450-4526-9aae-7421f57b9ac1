"use client"

import { useState } from "react"
import { usePathname, useRouter } from "next/navigation"
import { Plus } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { NeighborhoodForm } from "@/app/_components/neighborhood/neighborhood-form"

interface CreateNeighborhoodButtonProps {
  cityId: string
  crewId: string
}

export function CreateNeighborhoodButton({ cityId, crewId }: CreateNeighborhoodButtonProps) {
  const [open, setOpen] = useState(false)
  const router = useRouter();
  const pathname = usePathname();

  const handleSuccess = (neighborhoodId: string) => {
    setOpen(false)
    // router.push(`/cities/${cityId}/crews/${crewId}/neighborhoods/${neighborhoodId}`)
    router.push(`${pathname}/neighborhoods/${neighborhoodId}`)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Nueva Colonia
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Crear Nueva Colonia</DialogTitle>
        </DialogHeader>
        <NeighborhoodForm cityId={cityId} crewId={crewId} onSuccess={handleSuccess} />
      </DialogContent>
    </Dialog>
  )
}

