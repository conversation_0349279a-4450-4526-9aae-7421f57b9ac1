"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { createNeighborhood, updateNeighborhood } from "@/lib/city-actions copy"
import { useCurrentUser } from "@/Providers/CurrentUserProvider"
import { Neighborhood } from "@/constants/companyService"

const neighborhoodSchema = z.object({
  name: z.string().min(2, "El nombre debe tener al menos 2 caracteres"),
  active: z.boolean().default(true),
  crewId: z.string().optional(),
  cityId: z.string().optional(),
})

type NeighborhoodFormValues = z.infer<typeof neighborhoodSchema>

interface NeighborhoodFormProps {
  neighborhood?: Neighborhood
  cityId: string
  crewId: string
  onSuccess?: (neighborhoodId: string) => void
}

export function NeighborhoodForm({ neighborhood, cityId, crewId, onSuccess }: NeighborhoodFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter();
  const { user } = useCurrentUser<'company'>();

  const form = useForm<NeighborhoodFormValues>({
    resolver: zodResolver(neighborhoodSchema),
    defaultValues: {
      name: neighborhood?.name || "",
      active: neighborhood?.active ?? true,
      crewId: neighborhood?.crewId || crewId,
      cityId: neighborhood?.cityId || cityId,
    },
  })

  async function onSubmit(values: NeighborhoodFormValues) {
    setIsLoading(true)
    console.log('Creating new neighborhood', {
      ...values,
      cityId,
      crewId,
    })
    try {
      if (neighborhood) {
        // Update existing neighborhood
        const updatedNeighborhood = await updateNeighborhood(neighborhood._id, values)
        if (updatedNeighborhood && onSuccess) {
          onSuccess(updatedNeighborhood._id)
        }
      } else {

        // Create new neighborhood
        const newNeighborhood = await createNeighborhood({
          ...values,
          cityId,
          crewId,
          companyId: user.companyId,
        })
        if (newNeighborhood && onSuccess) {
          onSuccess(newNeighborhood._id)
        }
      }

      router.refresh()
    } catch (error) {
      console.error("Error saving neighborhood:", error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nombre</FormLabel>
              <FormControl>
                <Input placeholder="Nombre de la colonia" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />



        <FormField
          control={form.control}
          name="active"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
              <FormControl>
                <Checkbox checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>Activa</FormLabel>
              </div>
            </FormItem>
          )}
        />

        <Button type="submit" disabled={isLoading} className="w-full">
          {isLoading ? "Guardando..." : neighborhood ? "Actualizar Colonia" : "Crear Colonia"}
        </Button>
      </form>
    </Form>
  )
}

