"use client"

import { DAYS_OF_WEEK_FULL } from '@/constants'
import { Card, CardContent } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Controller, useForm } from "react-hook-form"
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Plus, RotateCcw, X } from 'lucide-react'
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from '@/components/ui/select'
import { Neighborhood } from '@/constants/companyService'
import { updateNeighborhood } from '@/lib/city-actions copy'
import { useParams } from 'next/navigation'
import { useToast } from '@chakra-ui/react'


const ScheduleConfigSchema = z.object({
    weeklySchedule: z.object({
        monday: z.object({
            start: z.string(),
            end: z.string()
        }).optional(),
        tuesday: z.object({
            start: z.string(),
            end: z.string()
        }).optional(),
        wednesday: z.object({
            start: z.string(),
            end: z.string()
        }).optional(),
        thursday: z.object({
            start: z.string(),
            end: z.string()
        }).optional(),
        friday: z.object({
            start: z.string(),
            end: z.string()
        }).optional(),
        saturday: z.object({
            start: z.string(),
            end: z.string()
        }).optional(),
        sunday: z.object({
            start: z.string(),
            end: z.string()
        }).optional()
    }).optional(),
    timezone: z.string().optional(),
    breakTime: z.object({
        start: z.string().optional(),
        end: z.string().optional()
    }).optional(),
    maxSimultaneousInstallations: z.number().min(1).optional().default(1),
    installationDuration: z.number().min(1).optional().default(180)
});

interface NeighborhoodSettingsProps {
    // data: z.infer<typeof ScheduleConfigSchema>
    scheduleConfig?: Neighborhood['scheduleConfig']
    // isLoading: boolean
}

export function NeighborhoodSettings({ scheduleConfig }: NeighborhoodSettingsProps) {

    const params = useParams<{ neighborhoodId: string }>()
    const toast = useToast();

    const defaultValues = {
        weeklySchedule: {
            monday: scheduleConfig?.weeklySchedule?.monday || undefined,
            tuesday: scheduleConfig?.weeklySchedule?.tuesday || undefined,
            wednesday: scheduleConfig?.weeklySchedule?.wednesday || undefined,
            thursday: scheduleConfig?.weeklySchedule?.thursday || undefined,
            friday: scheduleConfig?.weeklySchedule?.friday || undefined,
            saturday: scheduleConfig?.weeklySchedule?.saturday || undefined,
            sunday: scheduleConfig?.weeklySchedule?.sunday || undefined
        },
        timezone: scheduleConfig?.timezone,
        breakTime: scheduleConfig?.breakTime,
        maxSimultaneousInstallations: scheduleConfig?.maxSimultaneousInstallations ?? 1,
        installationDuration: scheduleConfig?.installationDuration ?? 180
    }

    const { control, handleSubmit, watch, setValue } = useForm<ScheduleConfigCompany>({
        defaultValues,
        resolver: zodResolver(ScheduleConfigSchema)
    })
    const weeklySchedule = watch('weeklySchedule')
    const handleToggleDay = (day: keyof WeeklySchedule) => {
        if (weeklySchedule[day]) {
            setValue(`weeklySchedule.${day}`, undefined)
        } else {
            setValue(`weeklySchedule.${day}`, { start: "09:00", end: "17:00" })
        }
    }

    const handleReset = (day: keyof WeeklySchedule) => {
        setValue(`weeklySchedule.${day}`, { start: "09:00", end: "17:00" })
    }

    const onSubmit = async (data: ScheduleConfigCompany) => {
        console.log('data', data);

        await updateNeighborhood(params.neighborhoodId, {
            scheduleConfig: data
        })

        toast({
            title: "Configuración guardada",
            description: "Recargando la página...",
            status: "success",
            duration: 3000,
            isClosable: true,
            position: 'top',
        })
    }

    return (
        <>

            <Card className="border-0 shadow-none p-6">
                <CardContent className="space-y-6 p-0">
                    <div className="space-y-4">
                        <div className="flex items-center justify-between">
                            <h3 className="text-sm font-medium text-neutral-900">
                                Configura la disponibilidad de la colonia
                            </h3>
                            <Button type="submit" onClick={handleSubmit(onSubmit)} className="bg-[#6C2BD9] hover:bg-[#5a24b5]">
                                {/* Guardar */}
                                {
                                    scheduleConfig ? "Actualizar" : "Guardar"
                                }
                            </Button>
                        </div>
                        {DAYS_OF_WEEK_FULL.map(({ label, value: day }) => {
                            return (
                                <div key={day} className="grid grid-cols-[120px_1fr] gap-4 items-center pl-6">
                                    <Label className="capitalize text-sm font-normal">{label}</Label>
                                    <div className="flex items-center gap-4">
                                        {weeklySchedule?.[day]?.start ? (
                                            <>
                                                <div className="flex items-center gap-2">
                                                    <Controller
                                                        name={`weeklySchedule.${day}.start`}
                                                        control={control}
                                                        render={({ field }) => (
                                                            <Input
                                                                {...field}
                                                                type="time"
                                                                className="w-32 border-neutral-200"
                                                                placeholder="Start Time"
                                                            />
                                                        )}
                                                    />
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <Controller
                                                        name={`weeklySchedule.${day}.end`}
                                                        control={control}
                                                        render={({ field }) => (
                                                            <Input
                                                                {...field}
                                                                type="time"
                                                                className="w-32 border-neutral-200"
                                                                placeholder="End Time"
                                                            />
                                                        )}
                                                    />
                                                </div>
                                                <div className="flex gap-2">
                                                    <Button
                                                        type="button"
                                                        size="icon"
                                                        variant="ghost"
                                                        onClick={() => handleToggleDay(day)}
                                                        className="text-neutral-500 hover:text-neutral-900"
                                                    >
                                                        <X className="w-4 h-4" />
                                                    </Button>
                                                    <Button
                                                        type="button"
                                                        size="icon"
                                                        variant="ghost"
                                                        onClick={() => handleReset(day)}
                                                        className="text-neutral-500 hover:text-neutral-900"
                                                    >
                                                        <RotateCcw className="w-4 h-4" />
                                                    </Button>
                                                </div>
                                            </>
                                        ) : (
                                            <div className="flex gap-2">
                                                <Button
                                                    type="button"
                                                    size="icon"
                                                    variant="ghost"
                                                    onClick={() => handleToggleDay(day)}
                                                    className="text-neutral-500 hover:text-neutral-900"
                                                >
                                                    <Plus className="w-4 h-4" />
                                                </Button>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )
                        })}
                    </div>

                    {/* Installation duration */}
                    <div className="space-y-4">
                        <h3 className="text-sm font-medium text-neutral-900">Duración de Instalación (minutos) </h3>
                        <div className='pl-6'>

                            <Controller
                                name="installationDuration"
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        {...field}
                                        type="number"
                                        className="border-neutral-200"
                                    />
                                )}
                            />
                        </div>
                    </div>


                    {/* Break time */}

                    <div className="space-y-4">
                        <h3 className="text-sm font-medium text-neutral-900">Tiempo de descanso/comida</h3>
                        <div className="flex items-center gap-4 pl-6">
                            <div className="flex items-center gap-2">
                                <Controller
                                    name="breakTime.start"
                                    control={control}
                                    render={({ field }) => (
                                        <Input
                                            {...field}
                                            type="time"
                                            className="w-32 border-neutral-200"
                                            placeholder="Start Time"
                                        />
                                    )}
                                />
                            </div>
                            <div className="flex items-center gap-2">
                                <Controller
                                    name="breakTime.end"
                                    control={control}
                                    render={({ field }) => (
                                        <Input
                                            {...field}
                                            type="time"
                                            className="w-32 border-neutral-200"
                                            placeholder="End Time"
                                        />
                                    )}
                                />
                            </div>
                            <div className="flex gap-2">
                                <Button
                                    type="button"
                                    size="icon"
                                    variant="ghost"
                                    className="text-neutral-500 hover:text-neutral-900"
                                >
                                    <Plus className="w-4 h-4" />
                                </Button>
                                <Button
                                    type="button"
                                    size="icon"
                                    variant="ghost"
                                    className="text-neutral-500 hover:text-neutral-900"
                                >
                                    <RotateCcw className="w-4 h-4" />
                                </Button>
                            </div>
                        </div>
                    </div>

                    {/* Max simultaneous installations */}
                    <div className="space-y-4">
                        <h3 className="text-sm font-medium text-neutral-900">Instalaciones simultaneas</h3>
                        <div className='pl-6'>
                            <Controller
                                name="maxSimultaneousInstallations"
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        {...field}
                                        type="number"
                                        className="border-neutral-200"
                                    />
                                )}
                            />
                        </div>
                    </div>

                    <div className="space-y-4">
                        <h3 className="text-sm font-medium text-neutral-900">Zona horaria</h3>
                        <div className='pl-6'>

                            <Controller
                                name="timezone"
                                control={control}
                                render={({ field }) => {

                                    return (
                                        <Select onValueChange={field.onChange} value={field.value}>
                                            <SelectTrigger className="w-full border-neutral-200">
                                                <SelectValue placeholder="Select timezone" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="America/Mexico_City">Mexico City</SelectItem>
                                                <SelectItem value="America/New_York">New York</SelectItem>
                                                <SelectItem value="America/Los_Angeles">Los Angeles</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    )
                                }}
                            />
                        </div>
                    </div>


                </CardContent>
            </Card>
        </>
    )
}
