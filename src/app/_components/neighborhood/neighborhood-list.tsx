import Link from "next/link"
// import type { Neighborhood } from "@/lib/data"
import { Neighborhood } from "@/constants/companyService"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ArrowRight, MapPin } from "lucide-react"

interface NeighborhoodListProps {
  neighborhoods: Neighborhood[]
  cityId: string
  crewId: string
  locale: string
}

export function NeighborhoodList({ neighborhoods, cityId, crewId, locale }: NeighborhoodListProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {neighborhoods.map((neighborhood) => (
        <Card key={neighborhood._id} className="overflow-hidden bg-white">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-xl">{neighborhood.name}</CardTitle>
              <Badge variant={neighborhood.active ? "default" : "destructive"}>
                {neighborhood.active ? "Activa" : "Inactiva"}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>

            <Link href={`/${locale}/dashboard/cities/${cityId}/crews/${crewId}/neighborhoods/${neighborhood._id}`} passHref>
              <Button variant="outline" className="w-full">
                Ver detalles
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </CardContent>
        </Card>
      ))}

      {neighborhoods.length === 0 && (
        <div className="col-span-full text-center py-10">
          <p className="text-muted-foreground">No hay colonias disponibles para esta cuadrilla</p>
        </div>
      )}
    </div>
  )
}

