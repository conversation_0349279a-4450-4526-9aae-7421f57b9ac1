"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { CompanyUserRole } from "@/lib/user-types"
import { companyService } from "@/constants/companyService"
import { toast } from "@/components/ui/use-toast"

const userFormSchema = z.object({
  name: z.string().min(2, "El nombre debe tener al menos 2 caracteres"),
  email: z.string().email("Email inválido"),
  role: z.enum([
    // CompanyUserRole.OWNER,
    CompanyUserRole.ADMIN,
    // CompanyUserRole.MANAGER,
    // CompanyUserRole.SUPERVISOR,
    CompanyUserRole.OPERATOR,
  ]),
})

type UserFormValues = z.infer<typeof userFormSchema>

interface UserFormProps {
  user?: any
  companyId: string
  onSuccess?: () => void
}

export function UserForm({ user, companyId, onSuccess }: UserFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const [cities, setCities] = useState<any[]>([])
  const [crews, setCrews] = useState<any[]>([])

  const form = useForm<UserFormValues>({
    resolver: zodResolver(userFormSchema),
    defaultValues: user
      ? {
        name: user.name,
        email: user.email,
        role: user.role,
      }
      : {
        name: "",
        email: "",
        role: CompanyUserRole.OPERATOR,
      },
  })

  async function onSubmit(values: UserFormValues) {
    setIsLoading(true)

    try {
      if (user) {
        // Update existing user permissions
        await companyService.updateUserCompanyPermissions(companyId, user.id, {
          role: values.role,
          allowedCities: user.allowedCities || [],
          allowedCrews: user.allowedCrews || [],
        })
        toast({
          title: "Usuario actualizado",
          description: "Los datos del usuario han sido actualizados correctamente.",
        })
      } else {
        // Invite new user
        await companyService.inviteUserToCompany(companyId, {
          email: values.email,
          name: values.name,
          role: values.role,
        })
        toast({
          title: "Invitación enviada",
          description: "Se ha enviado la invitación al usuario correctamente.",
        })
      }

      if (onSuccess) {
        onSuccess()
      }
      router.refresh()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Hubo un error al procesar la solicitud",
        variant: "destructive",
      })
      console.error("Error saving user:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const roleOptions = [
    // { value: CompanyUserRole.OWNER, label: "Propietario" },
    { value: CompanyUserRole.ADMIN, label: "Administrador" },
    // { value: CompanyUserRole.MANAGER, label: "Gerente" },
    // { value: CompanyUserRole.SUPERVISOR, label: "Supervisor" },
    { value: CompanyUserRole.OPERATOR, label: "Operador" },
  ]

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nombre</FormLabel>
              <FormControl>
                <Input placeholder="Nombre completo" {...field} disabled={isLoading} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="<EMAIL>" {...field} disabled={isLoading} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="role"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Rol</FormLabel>
              <Select disabled={isLoading} onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Seleccione un rol" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {roleOptions.map((role) => (
                    <SelectItem key={role.value} value={role.value}>
                      {role.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription>El rol determina los permisos que tendrá el usuario en el sistema.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" disabled={isLoading} className="w-full">
          {isLoading ? "Guardando..." : user ? "Actualizar Usuario" : "Invitar Usuario"}
        </Button>
      </form>
    </Form>
  )
}

