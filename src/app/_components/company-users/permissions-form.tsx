"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zodR<PERSON>ol<PERSON> } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { CompanyUserRole } from "@/lib/user-types"
import { companyService } from "@/constants/companyService"
import { toast } from "@/components/ui/use-toast"

const permissionsSchema = z.object({
  role: z.enum([
    // CompanyUserRole.OWNER,
    CompanyUserRole.ADMIN,
    // CompanyUserRole.MANAGER,
    // CompanyUserRole.SUPERVISOR,
    CompanyUserRole.OPERATOR,
  ]),
  allowedCities: z.array(z.string()),
  allowedCrews: z.array(z.string()),
})

type PermissionsFormValues = z.infer<typeof permissionsSchema>

interface PermissionsFormProps {
  user: any
  companyId: string
  onSuccess?: () => void
}

export function PermissionsForm({ user, companyId, onSuccess }: PermissionsFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [cities, setCities] = useState<any[]>([])
  const [crews, setCrews] = useState<any[]>([])
  const router = useRouter()

  useEffect(() => {
    const fetchData = async () => {
      try {
        const citiesResponse = await companyService.getAllCities()
        setCities(citiesResponse.data)

        // Fetch crews for selected cities
        const crewsPromises = user.allowedCities.map((cityId: string) =>
          companyService.getCrewsByCityId(cityId)
        )
        const crewsResponses = await Promise.all(crewsPromises)
        const allCrews = crewsResponses.flatMap(response => response.data)
        setCrews(allCrews)
      } catch (error) {
        // console.error("Error fetching data:", error)
        toast({
          title: "Error",
          description: "No se pudieron cargar los datos de ciudades y cuadrillas",
          variant: "destructive",
        })
      }
    }

    fetchData()
  }, [user.allowedCities])

  const form = useForm<PermissionsFormValues>({
    resolver: zodResolver(permissionsSchema),
    defaultValues: {
      role: user.role,
      allowedCities: user.allowedCities,
      allowedCrews: user.allowedCrews,
    },
  })

  const selectedRole = form.watch("role")
  const selectedCities = form.watch("allowedCities")

  // Filter crews based on selected cities
  const filteredCrews = crews.filter((crew) => selectedCities.includes(crew.cityId))

  async function onSubmit(values: PermissionsFormValues) {
    setIsLoading(true)

    try {
      await companyService.updateUserCompanyPermissions(companyId, user.id, {
        role: values.role,
        allowedCities: values.allowedCities,
        allowedCrews: values.allowedCrews,
      })

      toast({
        title: "Permisos actualizados",
        description: "Los permisos del usuario han sido actualizados correctamente.",
      })

      if (onSuccess) {
        onSuccess()
      }
      router.refresh()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Error al actualizar los permisos",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const roleOptions = [
    // { value: CompanyUserRole.OWNER, label: "Propietario" },
    { value: CompanyUserRole.ADMIN, label: "Administrador" },
    // { value: CompanyUserRole.MANAGER, label: "Gerente" },
    // { value: CompanyUserRole.SUPERVISOR, label: "Supervisor" },
    { value: CompanyUserRole.OPERATOR, label: "Operador" },
  ]
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
            <span className="font-medium">{user.name.charAt(0)}</span>
          </div>
          <div>
            <div className="font-medium">{user.name}</div>
            <div className="text-sm text-muted-foreground">{user.email}</div>
          </div>
        </div>

        <FormField
          control={form.control}
          name="role"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Rol</FormLabel>
              <Select disabled={isLoading} onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Seleccione un rol" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {roleOptions.map((role) => (
                    <SelectItem key={role.value} value={role.value}>
                      {role.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription>El rol determina los permisos que tendrá el usuario en el sistema.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Tabs defaultValue="cities" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="cities">Ciudades</TabsTrigger>
            <TabsTrigger value="crews">Cuadrillas</TabsTrigger>
          </TabsList>

          <TabsContent value="cities" className="space-y-4 pt-4">
            <FormField
              control={form.control}
              name="allowedCities"
              render={() => (
                <FormItem>
                  <div className="mb-4">
                    <FormLabel className="text-base">Ciudades permitidas</FormLabel>
                    <FormDescription>Seleccione las ciudades a las que este usuario tendrá acceso.</FormDescription>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    {cities.map((city) => (
                      <FormField
                        key={city._id}
                        control={form.control}
                        name="allowedCities"
                        render={({ field }) => {
                          return (
                            <FormItem key={city.id} className="flex flex-row items-start space-x-3 space-y-0">
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(city._id)}
                                  onCheckedChange={async (checked) => {
                                    if (checked) { // If the checkbox is checked set the value
                                      field.onChange([...field.value, city._id])
                                      const result = await companyService.getCrewsByCityId(city._id);
                                      setCrews([...crews, ...result.data]);
                                    } else {
                                      field.onChange(field.value?.filter((value) => value !== city._id))
                                      setCrews(crews.filter((crew) => crew.cityId !== city._id));
                                    }
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="font-normal">{city.name}</FormLabel>
                            </FormItem>
                          )
                        }}
                      />
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </TabsContent>

          <TabsContent value="crews" className="space-y-4 pt-4">
            <FormField
              control={form.control}
              name="allowedCrews"
              render={() => (
                <FormItem>
                  <div className="mb-4">
                    <FormLabel className="text-base">Cuadrillas permitidas</FormLabel>
                    <FormDescription>Seleccione las cuadrillas a las que este usuario tendrá acceso.</FormDescription>
                  </div>
                  {selectedCities.length === 0 ? (
                    <div className="text-center py-4 text-muted-foreground">
                      Seleccione al menos una ciudad para ver las cuadrillas disponibles.
                    </div>
                  ) : filteredCrews.length === 0 ? (
                    <div className="text-center py-4 text-muted-foreground">
                      No hay cuadrillas disponibles para las ciudades seleccionadas.
                    </div>
                  ) : (
                    <div className="grid grid-cols-2 gap-4">
                          {filteredCrews.map((crew) => {
                            const cityName = cities.find((city) => city._id === crew.cityId)?.name
                            return (
                              <FormField
                                key={crew._id}
                                control={form.control}
                                name="allowedCrews"
                                render={({ field }) => {
                                  return (
                                    <FormItem key={crew.id} className="flex flex-row items-start space-x-3 space-y-0">
                                      <FormControl>
                                        <Checkbox
                                          checked={field.value?.includes(crew._id)}
                                          onCheckedChange={(checked) => {
                                            // return checked
                                            //   ? field.onChange([...field.value, crew.id])
                                            //   : field.onChange(field.value?.filter((value) => value !== crew.id))
                                            return checked
                                              ? field.onChange([...field.value, crew._id])
                                              : field.onChange(field.value?.filter((value) => value !== crew._id))
                                          }}
                                        />
                                      </FormControl>
                                      <FormLabel className="font-normal">
                                        {crew.name} de:{' '}
                                        <span className="text-muted-foreground">
                                          ({cityName})
                                        </span>
                                      </FormLabel>
                                    </FormItem>
                                  )
                                }}
                              />
                            )
                          })}
                    </div>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />
          </TabsContent>
        </Tabs>

        <Button type="submit" disabled={isLoading} className="w-full">
          {isLoading ? "Guardando..." : "Actualizar Permisos"}
        </Button>
      </form>
    </Form>
  )
}

