"use client"

import { User, <PERSON>, <PERSON>, <PERSON>, Ban } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { CompanyUserRole } from "@/lib/user-types"
import { useState } from "react"
import { UserForm } from "./user-form"
import { PermissionsForm } from "./permissions-form"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useRouter } from "next/navigation"
import { companyService } from "@/constants/companyService"
import { toast } from "@/components/ui/use-toast"

interface UserListProps {
  users: any[]
  companyId: string
}

export function UserList({ users, companyId }: UserListProps) {
  const [editingUser, setEditingUser] = useState<any | null>(null)
  const [changingPermissions, setChangingPermissions] = useState<any | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isPermissionsDialogOpen, setIsPermissionsDialogOpen] = useState(false)
  const router = useRouter();

  const handleStatusChange = async (userId: string, newStatus: 'active' | 'suspended') => {
    try {
      await companyService.updateUserCompanyPermissions(companyId, userId, {
        // status: newStatus,
        role: users.find(u => u.id === userId)?.role || 'operator',
        allowedCities: users.find(u => u.id === userId)?.allowedCities || [],
        allowedCrews: users.find(u => u.id === userId)?.allowedCrews || [],
      })

      toast({
        title: "Estado actualizado",
        description: `El usuario ha sido ${newStatus === 'active' ? 'activado' : 'suspendido'} correctamente.`,
      })
      router.refresh()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Error al actualizar el estado del usuario",
        variant: "destructive",
      })
    }
  }

  const handleEditSuccess = () => {
    setIsEditDialogOpen(false)
    setEditingUser(null)
    router.refresh()
  }

  const handlePermissionsSuccess = () => {
    setIsPermissionsDialogOpen(false)
    setChangingPermissions(null)
    router.refresh()
  }

  const handleEditUser = (user: any) => {
    setEditingUser(user)
    setIsEditDialogOpen(true)
  }

  const handleChangePermissions = (user: any) => {
    setChangingPermissions(user)
    setIsPermissionsDialogOpen(true)
  }

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case CompanyUserRole.OWNER:
        return "default"
      case CompanyUserRole.ADMIN:
        return "secondary"
      case CompanyUserRole.MANAGER:
        return "outline"
      case CompanyUserRole.SUPERVISOR:
        return "destructive"
      case CompanyUserRole.OPERATOR:
        return "default"
      default:
        return "outline"
    }
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 hover:bg-green-100"
      case "invited":
        return "bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
      case "suspended":
        return "bg-red-100 text-red-800 hover:bg-red-100"
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-100"
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "active":
        return "Activo"
      case "invited":
        return "Invitado"
      case "suspended":
        return "Suspendido"
      default:
        return status
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case CompanyUserRole.OWNER:
        return "Propietario"
      case CompanyUserRole.ADMIN:
        return "Administrador"
      case CompanyUserRole.MANAGER:
        return "Gerente"
      case CompanyUserRole.SUPERVISOR:
        return "Supervisor"
      case CompanyUserRole.OPERATOR:
        return "Operador"
      default:
        return role
    }
  }

  return (
    <>
      <Card className="bg-white">
        <CardHeader className="pb-2">
          <CardTitle>Lista de Usuarios</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Usuario</TableHead>
                <TableHead>Rol</TableHead>
                <TableHead>Permisos</TableHead>
                <TableHead>Estado</TableHead>
                <TableHead className="text-right">Acciones</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="h-24 text-center">
                    <div className="flex flex-col items-center justify-center space-y-2">
                      <p className="text-lg font-medium">No hay usuarios registrados</p>
                      <p className="text-sm text-muted-foreground">
                        Usa el botón "Invitar Usuario" para agregar nuevos usuarios a esta compañía.
                      </p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                          <User className="h-5 w-5 text-gray-500" />
                        </div>
                        <div>
                          <div className="font-medium">{user.name}</div>
                          <div className="text-sm text-muted-foreground">{user.email}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getRoleBadgeVariant(user.role)}>{getRoleLabel(user.role)}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>
                          <span className="font-medium">Ciudades:</span> {user.allowedCities.length}
                        </div>
                        <div>
                          <span className="font-medium">Cuadrillas:</span> {user.allowedCrews.length}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge status={user.status} className={getStatusBadgeVariant(user.status)}>{getStatusLabel(user.status)}</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => handleEditUser(user)}
                          title="Editar usuario"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => handleChangePermissions(user)}
                          title="Cambiar permisos"
                        >
                          <Shield className="h-4 w-4" />
                        </Button>
                        {user.status === "active" && user.role !== "owner" && (
                          <Button
                            variant="outline"
                            size="icon"
                            className="text-red-600"
                            onClick={() => handleStatusChange(user.id, 'suspended')}
                          >
                            <Ban className="h-4 w-4" />
                          </Button>
                        )}
                        {user.status === "suspended" && user.role !== "owner" && (
                          <Button
                            variant="outline"
                            size="icon"
                            className="text-green-600"
                            onClick={() => handleStatusChange(user.id, 'active')}
                          >
                            <Check className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Modal para editar usuario */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Editar Usuario</DialogTitle>
          </DialogHeader>
          {editingUser && (
            <UserForm
              user={editingUser}
              companyId={companyId}
              onSuccess={handleEditSuccess}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Modal para cambiar permisos */}
      <Dialog open={isPermissionsDialogOpen} onOpenChange={setIsPermissionsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Cambiar Permisos de Usuario</DialogTitle>
          </DialogHeader>
          {changingPermissions && (
            <PermissionsForm
              user={changingPermissions}
              companyId={companyId}
              onSuccess={handlePermissionsSuccess}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}

