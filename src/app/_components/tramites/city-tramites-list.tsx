'use client';

import { Box, Heading, SimpleGrid, Text, Badge } from '@chakra-ui/react';
import { useRouter } from 'next/navigation';

interface CityTramites {
  id: string;
  name: string;
  pendingTramites: number;
  completedTramites: number;
}

interface CityTramitesListProps {
  cities: CityTramites[];
  locale: string;
}

export function CityTramitesList({ cities, locale }: CityTramitesListProps) {
  const router = useRouter();

  const handleCityClick = (cityId: string) => {
    router.push(`/${locale}/dashboard/gestor/tramites/${cityId}`);
  };

  return (
    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
      {cities.map((city) => (
        <Box
          key={city.id}
          p={6}
          borderWidth="1px"
          borderRadius="lg"
          _hover={{ shadow: 'md' }}
          cursor="pointer"
          onClick={() => handleCityClick(city.id)}
        >
          <Heading size="md" mb={2}>{city.name}</Heading>
          <Box display="flex" gap={2} mb={2}>
            <Badge colorScheme="yellow">Pendientes: {city.pendingTramites}</Badge>
            <Badge colorScheme="green">Completados: {city.completedTramites}</Badge>
          </Box>
        </Box>
      ))}
    </SimpleGrid>
  );
}