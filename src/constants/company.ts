
export const stateSelectOptions = [
    {
        label: 'Ciudad de México',
        value: 'Ciudad de México',
        code: 'cdmx',
        color: 'rgba(220, 38, 38, 0.8)', // Rojo más intenso
    },
    {
        label: 'Estado de México',
        value: 'Estado de México',
        code: 'edomex',
        color: 'rgba(8, 145, 178, 0.8)', // Cian más intenso
    },
    // Nuevo León
    {
        label: 'Nuevo León',
        value: 'Nuevo León',
        code: 'nle',
        color: 'rgba(234, 88, 12, 0.8)', // Naranja más intenso
    },
    // Jalisco
    {
        label: 'Jalisco',
        value: 'Jalisco',
        code: 'jal',
        color: 'rgba(101, 163, 13, 0.8)', // Verde lima más intenso
    },
    // Baja California
    {
        label: 'Baja California',
        value: 'Baja California',
        code: 'bca',
        color: 'rgba(124, 58, 237, 0.8)', // Púrpura más intenso
    },
    // Puebla
    {
        label: 'Puebla',
        value: '<PERSON><PERSON><PERSON>',
        code: 'pbe',
        color: 'rgba(5, 150, 105, 0.8)', // Verde más intenso
    },
    // Saltillo
    {
        label: 'Saltillo (Coahuila)',
        value: 'Saltillo',
        code: 'sal',
        color: 'rgba(217, 119, 6, 0.8)', // Amarillo más intenso
    },
    // Queretaro
    {
        label: 'Querétaro',
        value: 'Querétaro',
        code: 'qro',
        color: 'rgba(219, 39, 119, 0.8)', // Rosa más intenso
    },
    // Mexicali
    {
        label: 'Mexicali (Baja California)',
        value: 'Mexicali',
        code: 'mxli',
        color: 'rgba(128, 0, 128, 0.8)', // Morado
    },
]

export const getStateByCode = (code: string) => {
    return stateSelectOptions.find((state) => state.code === code);
}

export const getStateByLabel = (label: string) => {
    return stateSelectOptions.find((state) => state.label === label);
}

export const getStateByValue = (value: string) => {
    return stateSelectOptions.find((state) => state.value === value);
}
