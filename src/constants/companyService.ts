import axios from 'axios';
import { URL_API } from '.';

export const apiCompanyPlatform = axios.create({
  baseURL: URL_API + '/vendor-platform',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request Interfaces
export interface CompanyData {
  name: string;
  address: {
    street: string;
    number: string;
    colony: string;
    city: string;
    patchalCode: string;
  };
  contactInfo?: {
    email?: string;
    phone?: string;
  };
}

export interface CityData {
  name: string;
  active: boolean;
  state: string;
  country: string;
  timezone?: string;
  companyId: string;
}

export interface CrewData {
  name: string;
  cityId: string;
  companyId: string;
  active: boolean;
  members: any[]
}

export interface NeighborhoodData {
  name: string;
  crewId: string;
  cityId: string;
  companyId: string;
  scheduleConfig?: {
    maxSimultaneousInstallations: number;
    installationDuration: number;
    weeklySchedule: WeeklySchedule;
    timezone?: string;
    breakTime?: TimeRange;
  };
  active: boolean;
}

export interface UserPermissionData {
  role: string;
  allowedCities?: string[];
  allowedCrews?: string[];
}

export interface InstallationAppointmentData {
  neighborhoodId: string;
  startTime: string;
  customerInfo: {
    name: string;
    email: string;
    phone: string;
  };
  address: Address;
}

// Response Interfaces
export interface ApiResponse<T> {
  message?: string;
  data: T;
}

export interface Company extends CompanyData {
  _id: string;
  createdAt: string;
  updatedAt: string;
}

export interface City extends CityData {
  _id: string;
  crews: Crew[];
  createdAt: string;
  updatedAt: string;
}

export interface Crew extends CrewData {
  _id: string;
  createdAt: string;
  updatedAt: string;
}

export interface Neighborhood extends NeighborhoodData {
  _id: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserPermissions {
  _id: string;
  userId: string;
  companyId: string;
  role: string;
  status: 'active' | 'invited' | 'suspended';
  allowedCities: City[];
  allowedCrews: Crew[];
  createdAt: string;
  updatedAt: string;
}

export interface CompanyUser {
  _id: string;
  email: string;
  name: string;
  status: 'active' | 'invited' | 'suspended';
  userType: 'company' | 'admin';
  createdAt: string;
  updatedAt: string;
}

export interface WeeklySchedule {
  monday?: DaySchedule;
  tuesday?: DaySchedule;
  wednesday?: DaySchedule;
  thursday?: DaySchedule;
  friday?: DaySchedule;
  saturday?: DaySchedule;
  sunday?: DaySchedule;
}

export interface DaySchedule {
  start: string;
  end: string;
}

export interface Address {
  street: string;
  number: string;
  colony: string;
  city: string;
  patchalCode: string;
}

export interface InstallationSlot {
  startTime: string;
  endTime: string;
  available: boolean;
}

export interface InstallationAppointment {
  _id: string;
  neighborhoodId: string;
  startTime: string;
  endTime: string;
  status: 'scheduled' | 'completed' | 'cancelled';
  customerInfo: {
    name: string;
    email: string;
    phone: string;
  };
  address: Address;
  createdAt: string;
  updatedAt: string;
}

export const companyService = {
  setHeaders: (headers: Record<string, string>) => {
    apiCompanyPlatform.defaults.headers = {
      ...apiCompanyPlatform.defaults.headers,
      ...headers,
    };
  },

  // Companies
  createCompany: async (companyData: CompanyData): Promise<ApiResponse<Company>> => {
    const { data } = await apiCompanyPlatform.post('/companies', companyData);
    return data;
  },

  getAllCompanies: async (): Promise<ApiResponse<Company[]>> => {
    const { data } = await apiCompanyPlatform.get('/companies');
    return data;
  },

  getCompanyById: async (companyId: string): Promise<ApiResponse<Company>> => {
    const { data } = await apiCompanyPlatform.get(`/companies/${companyId}`);
    return data;
  },

  updateCompany: async (
    companyId: string,
    companyData: Partial<CompanyData>
  ): Promise<ApiResponse<Company>> => {
    const { data } = await apiCompanyPlatform.put(`/companies/${companyId}`, companyData);
    return data;
  },

  deleteCompany: async (companyId: string): Promise<ApiResponse<void>> => {
    const { data } = await apiCompanyPlatform.delete(`/companies/${companyId}`);
    return data;
  },

  // Cities
  createCity: async (cityData: CityData): Promise<ApiResponse<City>> => {
    const { data } = await apiCompanyPlatform.post('/cities', cityData);
    return data;
  },

  getAllCities: async (): Promise<ApiResponse<City[]>> => {
    try {
      const { data } = await apiCompanyPlatform.get('/cities');
      return data;
    } catch (error) {
      console.error('Error fetching cities:', error);
      throw error;
    }
  },

  getCityById: async (cityId: string): Promise<ApiResponse<City>> => {
    const { data } = await apiCompanyPlatform.get(`/cities/${cityId}`);
    return data;
  },

  updateCity: async (cityId: string, cityData: Partial<CityData>): Promise<ApiResponse<City>> => {
    const { data } = await apiCompanyPlatform.put(`/cities/${cityId}`, cityData);
    return data;
  },

  deleteCity: async (cityId: string): Promise<ApiResponse<void>> => {
    const { data } = await apiCompanyPlatform.delete(`/cities/${cityId}`);
    return data;
  },

  // Crews
  createCrew: async (crewData: Omit<CrewData, 'members'>): Promise<ApiResponse<Crew>> => {
    const { data } = await apiCompanyPlatform.post('/crews', crewData);
    return data;
  },

  getAllCrews: async (): Promise<ApiResponse<Crew[]>> => {
    const { data } = await apiCompanyPlatform.get('/crews');
    return data;
  },

  getCrewsByCityId: async (cityId: string): Promise<ApiResponse<Crew[]>> => {
    const { data } = await apiCompanyPlatform.get(`/cities/${cityId}/crews`);
    return data;
  },

  getCrewById: async (crewId: string): Promise<ApiResponse<Crew>> => {
    const { data } = await apiCompanyPlatform.get(`/crews/${crewId}`);
    return data;
  },

  updateCrew: async (crewId: string, crewData: Partial<CrewData>): Promise<ApiResponse<Crew>> => {
    const { data } = await apiCompanyPlatform.put(`/crews/${crewId}`, crewData);
    return data;
  },

  deleteCrew: async (crewId: string): Promise<ApiResponse<void>> => {
    const { data } = await apiCompanyPlatform.delete(`/crews/${crewId}`);
    return data;
  },

  // Neighborhoods
  createNeighborhood: async (neighborhoodData: NeighborhoodData): Promise<ApiResponse<Neighborhood>> => {
    const { data } = await apiCompanyPlatform.post('/neighborhoods', neighborhoodData);
    return data;
  },

  getAllNeighborhoods: async ({ params }: { params?: { companyId?: string; crewId?: string } } = {}): Promise<ApiResponse<Neighborhood[]>> => {
    const { data } = await apiCompanyPlatform.get('/neighborhoods', { params });
    console.log('getAllNeighborhoods data: ', data);
    return data;
  },

  getNeighborhoodById: async (neighborhoodId: string): Promise<ApiResponse<Neighborhood>> => {
    const { data } = await apiCompanyPlatform.get(`/neighborhoods/${neighborhoodId}`);
    return data;
  },

  updateNeighborhood: async (
    neighborhoodId: string,
    neighborhoodData: Partial<NeighborhoodData>
  ): Promise<ApiResponse<Neighborhood>> => {
    try {
      const { data } = await apiCompanyPlatform.put(`/neighborhoods/${neighborhoodId}`, neighborhoodData);
      return data;
    } catch (error) {
      console.error('Error updating neighborhood:', error);
      throw error;
    }
  },

  deleteNeighborhood: async (neighborhoodId: string): Promise<ApiResponse<void>> => {
    const { data } = await apiCompanyPlatform.delete(`/neighborhoods/${neighborhoodId}`);
    return data;
  },

  // Installation Appointments
  getInstallationSlots: async (
    neighborhoodId: string,
    date: string
  )/* : Promise<ApiResponse<InstallationSlot[]>> */ => {
    const { data } = await apiCompanyPlatform.get(`/installation-slots/${neighborhoodId}/${date}`);
    console.log('getInstallationSlots data: ', data);
    return data;
  },

  createInstallationAppointment: async (
    appointmentData: InstallationAppointmentData
  ): Promise<ApiResponse<InstallationAppointment>> => {
    const { data } = await apiCompanyPlatform.post('/installation-appointments', appointmentData);
    return data;
  },

  getInstallationAppointments: async (date: string): Promise<ApiResponse<InstallationAppointment[]>> => {
    const { data } = await apiCompanyPlatform.get(`/installation-appointments/${date}`);
    return data;
  },
  /*
    Get installation Appointments by date range and crewId, neighborhoodId
  GET {{baseUrl}}/vendor-platform/installation-appointments?startDate=2025-04-08&endDate=2025-04-08T09:00:00.000-06:00
 */

  getInstallationAppointmentsByDateRange: async ({
    startDate,
    endDate,
    crewId,
    neighborhoodId,
  }: {
    startDate?: string;
    endDate?: string;
    crewId?: string;
    neighborhoodId?: string;
  } = {}): Promise<ApiResponse<InstallationAppointment[]>> => {
    const { data } = await apiCompanyPlatform.get(`/installation-appointments`, { params: { startDate, endDate, crewId, neighborhoodId } });
    return data;
  },

  getInstallationAppointmentById: async (
    appointmentId: string
  ): Promise<ApiResponse<InstallationAppointment>> => {
    const { data } = await apiCompanyPlatform.get(`/installation-appointments/${appointmentId}`);
    return data;
  },

  cancelInstallationAppointment: async (appointmentId: string): Promise<ApiResponse<void>> => {
    const { data } = await apiCompanyPlatform.delete(`/installation-appointments/${appointmentId}`);
    return data;
  },

  updateInstallationAppointmentStatus: async (
    appointmentId: string,
    status: 'completed' | 'cancelled',
    notes?: string
  ): Promise<ApiResponse<InstallationAppointment>> => {
    const { data } = await apiCompanyPlatform.patch(`/installation-appointments/${appointmentId}/status`, {
      status,
      notes,
    });
    return data;
  },

  // Company Users and Permissions
  inviteUserToCompany: async (
    companyId: string,
    userData: { email: string; name: string; role: string }
  ): Promise<ApiResponse<UserPermissions>> => {
    const { data } = await apiCompanyPlatform.post(`/companies/${companyId}/users/invite`, userData);
    console.log('data', data);
    return data;
  },

  getUserCompanyPermissions: async (
    companyId: string,
    userId: string
  ): Promise<ApiResponse<UserPermissions>> => {
    const { data } = await apiCompanyPlatform.get(`/companies/${companyId}/users/${userId}/permissions`);
    return data;
  },

  updateUserCompanyPermissions: async (
    companyId: string,
    userId: string,
    permissionData: UserPermissionData
  ): Promise<ApiResponse<UserPermissions>> => {
    const { data } = await apiCompanyPlatform.patch(
      `/companies/${companyId}/users/${userId}/permissions`,
      permissionData
    );
    return data;
  },

  deleteUserCompanyPermissions: async (companyId: string, userId: string): Promise<ApiResponse<void>> => {
    const { data } = await apiCompanyPlatform.delete(`/companies/${companyId}/users/${userId}/permissions`);
    return data;
  },

  getUsersByCompany: async (companyId: string): Promise<ApiResponse<CompanyUser[]>> => {
    try {
      // Log the headers being sent
      const { data } = await apiCompanyPlatform.get(`/companies/${companyId}/users`);
      return data;

    } catch (error: any) {
      console.error("Error in getUsersByCompany:", error.response?.data || error);
      console.error("Status code:", error.response?.status);
      console.error("Headers:", error.response?.headers);

      // Return an empty array as data to avoid breaking the UI
      return { message: "Error fetching users", data: [] };
    }
  },

  getAllUsers: async (): Promise<ApiResponse<CompanyUser[]>> => {
    const { data } = await apiCompanyPlatform.get('/users');
    return data;
  },

  getUserById: async (userId: string): Promise<ApiResponse<CompanyUser>> => {
    const { data } = await apiCompanyPlatform.get(`/users/${userId}`);
    return data;
  },

  uploadEvidence: async (
    appointmentId: string,
    data: { proofImages: File[], notes?: string }
  )/* : Promise<ApiResponse<InstallationAppointment>> */ => {
    const formData = new FormData();

    data.proofImages.forEach((file) => {
      formData.append('proofImages', file);
    });
    formData.append('source', 'company');

    if (data.notes) {
      formData.append('notes', data.notes);
    }

    // log form data values
    for (const [key, value] of formData.entries()) {
      console.log(key, value);
    }

    const { data: responseData } = await apiCompanyPlatform.patch(
      `/installation-appointments/${appointmentId}/proof`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return responseData;
  },

  markArrived: async (appointmentId: string) => {
    const { data } = await apiCompanyPlatform.patch(`/installation-appointments/${appointmentId}/arrived`);
    return data;
  },

  getCompanyNeighborhoods: async () => {
    const response = await apiCompanyPlatform.get('/company/neighborhoods');
    return response.data;
  },

  getAvailableInstallationSlots: async (neighborhoodId: string, date: string) => {
    const response = await apiCompanyPlatform.get(`/company/neighborhoods/${neighborhoodId}/available-slots/${date}`);
    return response.data;
  },

  rescheduleInstallationAppointment: async (appointmentId: string, startTime: string, neighborhoodId: string) => {
    const response = await apiCompanyPlatform.patch(`/installation-appointments/${appointmentId}/reschedule`, {
      startTime,
      neighborhoodId
    });
    return response.data;
  },

  getInstallationsFiltered: async (startDate: string, endDate: string, crewId?: string, neighborhoodId?: string, status?: string) => {
    const response = await apiCompanyPlatform.get(`/installation-appointments/filter`, { params: { startDate, endDate, crewId, neighborhoodId, status } });
    return response.data;
  },

  markNotAttended: async (appointmentId: string, notAttendedReason: string) => {
    const response = await apiCompanyPlatform.patch(`/installation-appointments/${appointmentId}/not-attended-reason`, { notAttendedReason });
    return response.data;
  },

  searchInstallationAppointments: async (params: {
    page?: number;
    limit?: number;
    query?: string;
    startDate?: string;
    endDate?: string;
    status?: string;
  }) => {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.query) queryParams.append('query', params.query);
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    if (params.status) queryParams.append('status', params.status);

    const response = await apiCompanyPlatform.get(
      `/installation-appointments/search?${queryParams.toString()}`
    );
    return response.data;
  },
};

