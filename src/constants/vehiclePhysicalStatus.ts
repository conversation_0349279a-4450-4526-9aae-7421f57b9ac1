export enum PhysicalStatus {
  AWAITING_RECEIPT = 'AWAITING_RECEIPT',
  RECEIVED_AT_OCN_WAREHOUSE_FROM_DEALERSHIP = 'RECEIVED_AT_OCN_WAREHOUSE_FROM_DEALERSHIP',
  AVAILABLE_IN_STOCK = 'AVAILABLE_IN_STOCK',
  VEHICLE_TO_BE_REPAIRED = 'VEHICLE_TO_BE_REPAIRED',
  COLLECTED_FROM_STOCK = 'COLLECTED_FROM_STOCK',
  DELIVERED_TO_CUSTOMER = 'DELIVERED_TO_CUSTOMER',
  IN_TRANSIT_TO_VENDOR_WORKSHOP = 'IN_TRANSIT_TO_VENDOR_WORKSHOP',
  RECEIVED_BY_VENDOR_WORKSHOP = 'RECEIVED_BY_VENDOR_WORKSHOP',
  UNDER_REPAIR_AT_VENDOR_WORKSHOP = 'UNDER_REPAIR_AT_VENDOR_WORKSHOP',
  REPAIR_COMPLETE_BY_VENDOR = 'REPAIR_COMPLETE_BY_VENDOR',
  COLLECTED_FROM_VENDOR_BY_OCN_AGENT = 'COLLECTED_FROM_VENDOR_BY_OCN_AGENT',
  COLLECTED_BY_CUSTOMER = 'COLLECTED_BY_CUSTOMER',
}

// Physical status translations and modal texts for vehicle status update flows

export const PHYSICAL_STATUS_MAP: Record<PhysicalStatus, string> = {
  [PhysicalStatus.AWAITING_RECEIPT]: 'En espera de recepción',
  [PhysicalStatus.RECEIVED_AT_OCN_WAREHOUSE_FROM_DEALERSHIP]: 'Recibido en almacén OCN desde concesionario',
  [PhysicalStatus.AVAILABLE_IN_STOCK]: 'Disponible en stock',
  [PhysicalStatus.VEHICLE_TO_BE_REPAIRED]: 'Vehículo para reparar',
  [PhysicalStatus.COLLECTED_FROM_STOCK]: 'Recogido del stock',
  [PhysicalStatus.DELIVERED_TO_CUSTOMER]: 'Entregado al cliente',
  [PhysicalStatus.IN_TRANSIT_TO_VENDOR_WORKSHOP]: 'En tránsito al taller del proveedor para reparaciones',
  [PhysicalStatus.RECEIVED_BY_VENDOR_WORKSHOP]: 'Recibido por taller del proveedor',
  [PhysicalStatus.UNDER_REPAIR_AT_VENDOR_WORKSHOP]: 'En reparación en taller del proveedor',
  [PhysicalStatus.REPAIR_COMPLETE_BY_VENDOR]: 'Reparación completada por el proveedor',
  [PhysicalStatus.COLLECTED_FROM_VENDOR_BY_OCN_AGENT]: 'Recogido del proveedor por agente OCN',
  [PhysicalStatus.COLLECTED_BY_CUSTOMER]: 'Recogido por el cliente',
};

export const translatedText = {
  modalTitle: 'Actualizar Estado Físico del Vehículo',
  currentStatus: 'Estado actual:',
  proposedStatus: 'Estado propuesto:',
  selectNextStatus: 'Seleccione el siguiente estado:',
  cancel: 'Cancelar',
  confirm: 'Confirmar',
  confirming: 'Confirmando...',
  selectOption: 'Seleccione una opción',
  incompleteInfo: 'Información incompleta para actualizar el estado',
  updateError: 'Error al actualizar el estado del vehículo',
  genericError: 'Ocurrió un error al procesar la solicitud',
};

export const photoTexts = {
  takePhoto: 'Tomar Foto',
  retakePhoto: 'Volver a Tomar',
  photoRequired: 'Verificación Fotográfica',
  photoInstruction: 'Tomar una foto del vehículo',
  photoDescription: 'Requerido para verificación de estado',
  photoMissing: 'Se requiere una foto para confirmar el cambio de estado',
  uploading: 'Subiendo foto...',
}; 