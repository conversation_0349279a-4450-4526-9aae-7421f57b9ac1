// API URLS

export const URL_API_DEV = 'https://dev-api.onecarnow.com';
export const URL_API_PROD = 'https://api.onecarnow.com';
export const URL_API = process.env.NEXT_PUBLIC_API_URL as string;
export const PAYMENTS_API_URL = process.env.NEXT_PUBLIC_PAYMENTS_API_URL as string;
export const PAYMENT_API_SECRET = process.env.NEXT_PUBLIC_PAYMENT_API_SECRET as string;

// FRONT URLS

export const PROD_URL = 'https://administrador.onecarnow.com';
export const DEV_URL = 'https://develop.administrador.onecarnow.com';

// drivers app
export const DRIVERS_APP_URL = process.env.NEXT_PUBLIC_DRIVERS_APP_URL;

//contract regions

export const emailUsersAllowed = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];
export const emailUsersAllowedRegion = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];
export const adminsPayFlow = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

export const isProd = process.env.NODE_ENV === 'production';
// export const isProd = true; // for testing purposes

export const PAGOS_PAYMENT_URL = 'https://pagos.onecarnow.com/pago/';

export const CONTRACT_REGIONS = [
  {
    value: 'cdmx',
    label: 'CDMX',
    code: 'cdmx',
    number: 1,
  },
  {
    value: 'gdl',
    label: 'Guadalajara',
    code: 'gdl',
    number: 2,
  },
  {
    value: 'mty',
    label: 'Monterrey',
    code: 'mty',
    number: 3,
  },
  {
    value: 'qro',
    label: 'Querétaro',
    code: 'qro',
    number: 4,
  },
  {
    value: 'tij',
    label: 'Tijuana',
    code: 'tij',
    number: 5,
  },
  {
    value: 'moka',
    label: 'MOKA',
    code: 'moka',
    number: 6,
  },
  {
    value: 'pbe',
    label: 'Puebla',
    code: 'pbe',
    number: 7,
  },
  // {
  //   value: 'tol',
  //   label: 'Toluca',
  //   code: 'tol',
  //   number: 8,
  // },
  {
    value: 'ptv',
    label: 'Puerto Vallarta',
    code: 'ptv',
    number: 9,
  },
  {
    value: 'tep',
    label: 'Tepic',
    code: 'tep',
    number: 10,
  },
  {
    value: 'col',
    label: 'Colima',
    code: 'col',
    number: 11,
  },
  {
    value: 'sal',
    label: 'Saltillo',
    code: 'sal',
    number: 12,
  },
  {
    value: 'torr',
    label: 'Torreon',
    code: 'torr',
    number: 13,
  },
  {
    value: 'dur',
    label: 'Durango',
    code: 'dur',
    number: 14,
  },
  {
    value: 'mxli',
    label: 'Mexicali',
    code: 'mxli',
    number: 15,
  },
  {
    value: 'her',
    label: 'Hermosillo',
    code: 'her',
    number: 16,
  },
  {
    value: 'chi',
    label: 'Chihuahua',
    code: 'chi',
    number: 17,
  },
  {
    value: 'leo',
    label: 'León',
    code: 'leo',
    number: 18,
  },
  {
    value: 'ags',
    label: 'Aguas Calientes',
    code: 'ags',
    number: 19,
  },
  {
    value: 'slp',
    label: 'San Luis Potosí',
    code: 'slp',
    number: 20,
  },
  {
    value: 'mer',
    label: 'Mérida',
    code: 'mer',
    number: 21,
  },
];

export const CONTRACT_REGIONS_IATA = [
  {
    value: 'cdmx',
    label: 'CDMX',
    code: 'cdmx',
  },
  {
    value: 'gdl',
    label: 'Guadalajara',
    code: 'gdl',
  },
  {
    value: 'mty',
    label: 'Monterrey',
    code: 'mty',
  },
  {
    value: 'qro',
    label: 'Querétaro',
    code: 'qro',
  },
  {
    value: 'tij',
    label: 'Tijuana',
    code: 'tij',
  },
  {
    value: 'moka',
    label: 'MOKA',
    code: 'moka',
  },
  {
    value: 'pbe',
    label: 'Puebla',
    code: 'pbe',
  },
  // {
  //   value: 'tol',
  //   label: 'Toluca',
  //   code: 'tol',
  // },
  {
    value: 'ptv',
    label: 'Puerto Vallarta',
    code: 'ptv',
  },
  {
    value: 'tep',
    label: 'Tepic',
    code: 'tep',
  },
  {
    value: 'col',
    label: 'Colima',
    code: 'col',
  },
  {
    value: 'sal',
    label: 'Saltillo',
    code: 'sal',
  },
  {
    value: 'torr',
    label: 'Torreon',
    code: 'torr',
  },
  {
    value: 'dur',
    label: 'Durango',
    code: 'dur',
  },
  {
    value: 'mxli',
    label: 'Mexicali',
    code: 'mxli',
  },
  {
    value: 'her',
    label: 'Hermosillo',
    code: 'her',
  },
  {
    value: 'chi',
    label: 'Chihuahua',
    code: 'chi',
  },
  {
    value: 'leo',
    label: 'León',
    code: 'leo',
  },
  {
    value: 'ags',
    label: 'Aguas Calientes',
    code: 'ags',
  },
  {
    value: 'slp',
    label: 'San Luis Potosí',
    code: 'slp',
  },
  {
    value: 'mer',
    label: 'Mérida',
    code: 'mer',
  },
];

export const editTextBtn = 'Guardar';

export const regions: { [key: string]: string } = {
  1: 'CDMX',
  2: 'Guadalajara',
  3: 'Monterrey',
  4: 'Querétaro',
  5: 'Tijuana',
  6: 'MOKA',
  7: 'Puebla',
  // 8: 'Toluca',
  9: 'Puerto Vallarta',
  10: 'Tepic',
  11: 'Colima',
  12: 'Saltillo',
  13: 'Torreon',
  14: 'Durango',
  15: 'Mexicali',
  16: 'Hermosillo',
  17: 'Chihuahua',
  18: 'León',
  19: 'Aguas Calientes',
  20: 'San Luis Potosí',
  21: 'Mérida',
};

export const cities: { [key: string]: { label: string; value: string } } = {
  cdmx: { label: 'Ciudad De México', value: 'cdmx' },
  edomx: { label: 'Estado De México', value: 'edomx' },
  gdl: { label: 'Guadalajara', value: 'gdl' },
  mty: { label: 'Monterrey', value: 'mty' },
  pbc: { label: 'Puebla', value: 'puebla' },
  qro: { label: 'Querétaro', value: 'qro' },
  tij: { label: 'Tijuana', value: 'tij' },
  moka: { label: 'Moka', value: 'moka' },
  pbe: { label: 'Puebla', value: 'pbe' },
  tol: { label: 'Toluca', value: 'tol' },
  ptv: { label: 'Puerto Vallarta', value: 'ptv' },
  tep: { label: 'Tepic', value: 'tep' },
  col: { label: 'Colima', value: 'col' },
  sal: { label: 'Saltillo', value: 'sal' },
  torr: { label: 'Torreon', value: 'torr' },
  dur: { label: 'Durango', value: 'dur' },
  mxli: { label: 'Mexicali', value: 'mxli' },
  her: { label: 'Hermosillo', value: 'her' },
  chi: { label: 'Chihuahua', value: 'chi' },
  leo: { label: 'León', value: 'leo' },
  ags: { label: 'Aguas Calientes', value: 'ags' },
  slp: { label: 'San Luis Potosí', value: 'slp' },
  mer: { label: 'Mérida', value: 'mer' },
};

export const cities2: { [key: string]: { label: string; value: string } } = {
  cdmx: { label: 'CDMX', value: 'cdmx' },
  edomx: { label: 'EDOMX', value: 'edomx' },
  gdl: { label: 'Guadalajara', value: 'gdl' },
  mty: { label: 'Monterrey', value: 'mty' },
  pbc: { label: 'Puebla', value: 'puebla' },
  qro: { label: 'Querétaro', value: 'qro' },
  tij: { label: 'Tijuana', value: 'tij' },
  moka: { label: 'Moka', value: 'moka' },
  pbe: { label: 'Puebla', value: 'pbe' },
  tol: { label: 'Toluca', value: 'tol' },
  ptv: { label: 'Puerto Vallarta', value: 'ptv' },
  tep: { label: 'Tepic', value: 'tep' },
  col: { label: 'Colima', value: 'col' },
  sal: { label: 'Saltillo', value: 'sal' },
  torr: { label: 'Torreon', value: 'torr' },
  dur: { label: 'Durango', value: 'dur' },
  mxli: { label: 'Mexicali', value: 'mxli' },
  her: { label: 'Hermosillo', value: 'her' },
  chi: { label: 'Chihuahua', value: 'chi' },
  leo: { label: 'León', value: 'leo' },
  ags: { label: 'Aguas Calientes', value: 'ags' },
  slp: { label: 'San Luis Potosí', value: 'slp' },
  mer: { label: 'Mérida', value: 'mer' },
};

export const citiesSelect = [
  { label: 'Ciudad De México', value: 'cdmx' },
  { label: 'Estado De México', value: 'edomx' },
  { label: 'Guadalajara', value: 'gdl' },
  { label: 'Monterrey', value: 'mty' },
  { label: 'Puebla', value: 'puebla' },
  { label: 'Querétaro', value: 'qro' },
  { label: 'Tijuana', value: 'tij' },
  { label: 'Moka', value: 'moka' },
  { label: 'Puebla', value: 'pbe' },
  { label: 'Toluca', value: 'tol' },
  { label: 'Puerto Vallarta', value: 'ptv' },
  { label: 'Tepic', value: 'tep' },
  { label: 'Colima', value: 'col' },
  { label: 'Saltillo', value: 'sal' },
  { label: 'Torreon', value: 'torr' },
  { label: 'Durango', value: 'dur' },
  { label: 'Mexicali', value: 'mxli' },
  { label: 'Hermosillo', value: 'her' },
  { label: 'Chihuahua', value: 'chi' },
  { label: 'León', value: 'leo' },
  { label: 'Aguas Calientes', value: 'ags' },
  { label: 'San Luis Potosí', value: 'slp' },
  { label: 'Mérida', value: 'mer' },
];

export type StateOption = {
  label: string;
  value: string;
};

export const statesSelect: StateOption[] = [
  { label: 'Ciudad de México', value: 'Ciudad de México' },
  { label: 'Estado de México', value: 'Estado de México' },
  { label: 'Jalisco', value: 'Jalisco' },
  { label: 'Nuevo León', value: 'Nuevo León' },
  { label: 'Querétaro', value: 'Querétaro' },
  { label: 'Baja California', value: 'Baja California' },
  { label: 'Moka', value: 'Moka' },
  { label: 'Puebla', value: 'Puebla' },
  { label: 'Toluca', value: 'Toluca' },
  { label: 'Puerto Vallarta', value: 'Puerto Vallarta' },
  { label: 'Tepic', value: 'Tepic' },
  { label: 'Colima', value: 'Colima' },
  { label: 'Saltillo', value: 'Saltillo' },
  { label: 'Torreon', value: 'Torreon' },
  { label: 'Durango', value: 'Durango' },
  { label: 'Mexicali', value: 'Mexicali' },
  { label: 'Hermosillo', value: 'Hermosillo' },
  { label: 'Chihuahua', value: 'Chihuahua' },
  { label: 'León', value: 'León' },
  { label: 'Aguas Calientes', value: 'Aguas Calientes' },
  { label: 'San Luis Potosí', value: 'San Luis Potosí' },
  { label: 'Mérida', value: 'Mérida' },
];

export const citiesWithMoka: { [key: string]: { label: string; value: string } } = {
  cdmx: { label: 'Ciudad De México', value: 'cdmx' },
  edomx: { label: 'Estado De México', value: 'edomx' },
  gdl: { label: 'Guadalajara', value: 'gdl' },
  mty: { label: 'Monterrey', value: 'mty' },
  pbc: { label: 'Puebla', value: 'puebla' },
  qro: { label: 'Querétaro', value: 'qro' },
  tij: { label: 'Tijuana', value: 'tij' },
  moka: { label: 'Moka', value: 'moka' },
  pbe: { label: 'Puebla', value: 'pbe' },
  tol: { label: 'Toluca', value: 'tol' },
  ptv: { label: 'Puerto Vallarta', value: 'ptv' },
  tep: { label: 'Tepic', value: 'tep' },
  col: { label: 'Colima', value: 'col' },
  sal: { label: 'Saltillo', value: 'sal' },
  torr: { label: 'Torreon', value: 'torr' },
  dur: { label: 'Durango', value: 'dur' },
  mxli: { label: 'Mexicali', value: 'mxli' },
  her: { label: 'Hermosillo', value: 'her' },
  chi: { label: 'Chihuahua', value: 'chi' },
  leo: { label: 'León', value: 'leo' },
  ags: { label: 'Aguas Calientes', value: 'ags' },
  slp: { label: 'San Luis Potosí', value: 'slp' },
  mer: { label: 'Mérida', value: 'mer' },
};

export const federalEntities: { [key: string]: { label: string; value: string } } = {
  'Ciudad de México': { label: 'Ciudad de México', value: 'Ciudad de México' },
  'Estado de México': { label: 'Estado de México', value: 'Estado de México' },
  Jalisco: { label: 'Jalisco', value: 'Jalisco' },
  'Nuevo León': { label: 'Nuevo León', value: 'Nuevo León' },
  Puebla: { label: 'Puebla', value: 'Puebla' },
  Querétaro: { label: 'Querétaro', value: 'Querétaro' },
  'Baja California': { label: 'Baja California', value: 'Baja California' },
  Moka: { label: 'Moka', value: 'Moka' },
  Toluca: { label: 'Toluca', value: 'Toluca' },
  'Puerto Vallarta': { label: 'Puerto Vallarta', value: 'Puerto Vallarta' },
  Tepic: { label: 'Tepic', value: 'Tepic' },
  Colima: { label: 'Colima', value: 'Colima' },
  Saltillo: { label: 'Saltillo', value: 'Saltillo' },
  Torreon: { label: 'Torreon', value: 'Torreon' },
  Durango: { label: 'Durango', value: 'Durango' },
  Mexicali: { label: 'Mexicali', value: 'Mexicali' },
  Hermosillo: { label: 'Hermosillo', value: 'Hermosillo' },
  Chihuahua: { label: 'Chihuahua', value: 'Chihuahua' },
  León: { label: 'León', value: 'León' },
  'Aguas Calientes': { label: 'Aguas Calientes', value: 'Aguas Calientes' },
  'San Luis Potosí': { label: 'San Luis Potosí', value: 'San Luis Potosí' },
  Mérida: { label: 'Mérida', value: 'Mérida' },
};

export const ferderalEntitiesSelect = Object.keys(federalEntities).map((key) => federalEntities[key]);

export const citiesDependingState: { [key: string]: { label: string; value: string }[] } = {
  'Ciudad de México': [{ label: 'Ciudad de México', value: 'cdmx' }],
  'Estado de México': [{ label: 'Estado de México', value: 'edomx' }],
  Jalisco: [{ label: 'Guadalajara', value: 'gdl' }],
  'Nuevo León': [{ label: 'Monterrey', value: 'mty' }],
  Querétaro: [{ label: 'Querétaro', value: 'qro' }],
  'Baja California': [{ label: 'Tijuana', value: 'tij' }],
  Moka: [{ label: 'Moka', value: 'moka' }],
  Puebla: [{ label: 'Puebla', value: 'pbe' }],
  Toluca: [{ label: 'Toluca', value: 'tol' }],
  'Puerto Vallarta': [{ label: 'Puerto Vallarta', value: 'ptv' }],
  Tepic: [{ label: 'Tepic', value: 'tep' }],
  Colima: [{ label: 'Colima', value: 'col' }],
  Saltillo: [{ label: 'Saltillo', value: 'sal' }],
  Torreon: [{ label: 'Torreon', value: 'torr' }],
  Durango: [{ label: 'Durango', value: 'dur' }],
  Mexicali: [{ label: 'Mexicali', value: 'mxli' }],
  Hermosillo: [{ label: 'Hermosillo', value: 'her' }],
  Chihuahua: [{ label: 'Chihuahua', value: 'chi' }],
  León: [{ label: 'León', value: 'leo' }],
  'Aguas Calientes': [{ label: 'Aguas Calientes', value: 'ags' }],
  'San Luis Potosí': [{ label: 'San Luis Potosí', value: 'slp' }],
  Mérida: [{ label: 'Mérida', value: 'mer' }],
};

export const stepsSelect = [
  { value: '1', label: 'Stock' },
  { value: '2', label: 'Vehiculo listo' },
  { value: '3', label: 'Conductor asignado' },
  { value: '4', label: 'Contrato generado' },
  { value: '5', label: 'Entregado' },
];

export const statusSelected = [
  { value: 'active', label: 'Activos' },
  { value: 'activo', label: 'Activos' },
  { value: 'in-service', label: 'Taller' },
  { value: 'legal-process', label: 'Proceso legal' },
  { value: 'awaiting-insurance', label: 'Espera de seguro' },
  { value: 'overhauling', label: 'Revisión' },
  { value: 'stock', label: 'Stock' },
];

export const svgColors: { [key: string]: string } = {
  BLANCO: '#f2f2f2',
  NEGRO: 'black',
  GRIS: '#808080',
  // ROJO: '#c0392b',
  ROJO: '#dc143c' /* #dc143c */,
  BEIGE: '#E1C699',
  // BEIGE: '#E1C699',
  AZUL: '#0c0cdaf9',
  ESCARLATA: '	#FD2D1C',
};

export const colors = [
  { label: 'ROJO', value: 'ROJO' },
  { label: 'BLANCO', value: 'BLANCO' },
  { label: 'NEGRO', value: 'NEGRO' },
  { label: 'GRIS', value: 'GRIS' },
  { label: 'BEIGE', value: 'BEIGE' },
  { label: 'AZUL', value: 'AZUL' },
  { label: 'ESCARLATA', value: 'ESCARLATA' },
];

export const allStatus = {
  stock: 'stock',
  active: 'active',
  activo: 'active',
  readmissions: 'readmissions',
  discharged: 'discharged',
  'in-service': 'in-service',
  'legal-process': 'legal-process',
  'awaiting-insurance': 'awaiting-insurance',
  overhauling: 'overhauling',
};

export const pagesSearchRedirect: { [key: string]: string } = {
  stock: 'stock',
  active: 'activos',
  activo: 'activos',
  discharged: 'bajas',
  readmissions: 'reingresos',
  bloqueo: 'activos',
  desbloqueo: 'activos',
  'in-service': 'activos',
  'legal-process': 'activos',
  'awaiting-insurance': 'activos',
  overhauling: 'stock',
  invoiced: 'invoiced',
};

export const roleOptions = [
  { value: 'administrador', label: 'Administrator' },
  { value: 'superadmin', label: 'Superadmin' },
  { value: 'agent', label: 'Agente' },
  { value: 'lead', label: 'Lead' },
  { value: 'sales', label: 'Ventas' },
  { value: 'fleet-manager', label: 'Fleet Manager' },
  { value: 'auditor', label: 'Auditor' },
  { value: 'collection', label: 'Cobranza' },
];

export enum Countries {
  'United States' = 'United States',
  Mexico = 'Mexico',
}

export const CountriesOptions = {
  [Countries['United States']]: {
    value: Countries['United States'],
    label: Countries['United States'],
  },
  [Countries.Mexico]: {
    value: Countries.Mexico,
    label: Countries.Mexico,
  },
};

export const countries = [CountriesOptions[Countries['United States']], CountriesOptions[Countries.Mexico]];

export enum USSTATES {
  Alabama = 'Alabama',
  Alaska = 'Alaska',
  Arizona = 'Arizona',
  Arkansas = 'Arkansas',
  California = 'California',
  Colorado = 'Colorado',
  Connecticut = 'Connecticut',
  Delaware = 'Delaware',
  Florida = 'Florida',
  Georgia = 'Georgia',
  Hawaii = 'Hawaii',
  Idaho = 'Idaho',
  Illinois = 'Illinois',
  Indiana = 'Indiana',
  Iowa = 'Iowa',
  Kansas = 'Kansas',
  Kentucky = 'Kentucky',
  Louisiana = 'Louisiana',
  Maine = 'Maine',
  Maryland = 'Maryland',
  Massachusetts = 'Massachusetts',
  Michigan = 'Michigan',
  Minnesota = 'Minnesota',
  Mississippi = 'Mississippi',
  Missouri = 'Missouri',
  Montana = 'Montana',
  Nebraska = 'Nebraska',
  Nevada = 'Nevada',
  NewHampshire = 'New Hampshire',
  NewJersey = 'New Jersey',
  NewMexico = 'New Mexico',
  NewYork = 'New York',
  NorthCarolina = 'North Carolina',
  NorthDakota = 'North Dakota',
  Ohio = 'Ohio',
  Oklahoma = 'Oklahoma',
  Oregon = 'Oregon',
  Pennsylvania = 'Pennsylvania',
  RhodeIsland = 'Rhode Island',
  SouthCarolina = 'South Carolina',
  SouthDakota = 'South Dakota',
  Tennessee = 'Tennessee',
  Texas = 'Texas',
  Utah = 'Utah',
  Vermont = 'Vermont',
  Virginia = 'Virginia',
  Washington = 'Washington',
  WestVirginia = 'West Virginia',
  Wisconsin = 'Wisconsin',
  Wyoming = 'Wyoming',
}

export const US_STATES_OBJ = {
  [USSTATES.Florida]: {
    value: USSTATES.Florida,
    label: USSTATES.Florida,
  },
};

export const US_STATES_OPTIONS = [US_STATES_OBJ[USSTATES.Florida]];

export const US_CITIES = [
  {
    value: 'MIA',
    label: 'Miami',
    code: 'MIA',
    number: 22,
  },
];

export const US_COLORS = [
  { label: 'RED', value: 'RED' },
  { label: 'WHITE', value: 'WHITE' },
  { label: 'BLACK', value: 'BLACK' },
  { label: 'GRAY', value: 'GRAY' },
  { label: 'BEIGE', value: 'BEIGE' },
  { label: 'BLUE', value: 'BLUE' },
  { label: 'SCARLET', value: 'SCARLET' },
];

export enum USCITIESNAMES {
  Miami = 'Miami',
}

export const US_CITIES_NAMES_SHORT_CODES = {
  [USCITIESNAMES.Miami]: 'MIA',
};

export const US_CITIES_OPTIONS = {
  [USCITIESNAMES.Miami]: {
    value: USCITIESNAMES.Miami,
    label: USCITIESNAMES.Miami,
  },
};

export const US_STATES_CITIES = {
  'New York': [
    'New York',
    'Buffalo',
    'Rochester',
    'Yonkers',
    'Syracuse',
    'Albany',
    'New Rochelle',
    'Mount Vernon',
    'Schenectady',
    'Utica',
    'White Plains',
    'Hempstead',
    'Troy',
    'Niagara Falls',
    'Binghamton',
    'Freeport',
    'Valley Stream',
  ],
  California: [
    'Los Angeles',
    'San Diego',
    'San Jose',
    'San Francisco',
    'Fresno',
    'Sacramento',
    'Long Beach',
    'Oakland',
    'Bakersfield',
    'Anaheim',
    'Santa Ana',
    'Riverside',
    'Stockton',
    'Chula Vista',
    'Irvine',
    'Fremont',
    'San Bernardino',
    'Modesto',
    'Fontana',
    'Oxnard',
    'Moreno Valley',
    'Huntington Beach',
    'Glendale',
    'Santa Clarita',
    'Garden Grove',
    'Oceanside',
    'Rancho Cucamonga',
    'Santa Rosa',
    'Ontario',
    'Lancaster',
    'Elk Grove',
    'Corona',
    'Palmdale',
    'Salinas',
    'Pomona',
    'Hayward',
    'Escondido',
    'Torrance',
    'Sunnyvale',
    'Orange',
    'Fullerton',
    'Pasadena',
    'Thousand Oaks',
    'Visalia',
    'Simi Valley',
    'Concord',
    'Roseville',
    'Victorville',
    'Santa Clara',
    'Vallejo',
    'Berkeley',
    'El Monte',
    'Downey',
    'Costa Mesa',
    'Inglewood',
    'Carlsbad',
    'San Buenaventura (Ventura)',
    'Fairfield',
    'West Covina',
    'Murrieta',
    'Richmond',
    'Norwalk',
    'Antioch',
    'Temecula',
    'Burbank',
    'Daly City',
    'Rialto',
    'Santa Maria',
    'El Cajon',
    'San Mateo',
    'Clovis',
    'Compton',
    'Jurupa Valley',
    'Vista',
    'South Gate',
    'Mission Viejo',
    'Vacaville',
    'Carson',
    'Hesperia',
    'Santa Monica',
    'Westminster',
    'Redding',
    'Santa Barbara',
    'Chico',
    'Newport Beach',
    'San Leandro',
    'San Marcos',
    'Whittier',
    'Hawthorne',
    'Citrus Heights',
    'Tracy',
    'Alhambra',
    'Livermore',
    'Buena Park',
    'Menifee',
    'Hemet',
    'Lakewood',
    'Merced',
    'Chino',
    'Indio',
    'Redwood City',
    'Lake Forest',
    'Napa',
    'Tustin',
    'Bellflower',
    'Mountain View',
    'Chino Hills',
    'Baldwin Park',
    'Alameda',
    'Upland',
    'San Ramon',
    'Folsom',
    'Pleasanton',
    'Union City',
    'Perris',
    'Manteca',
    'Lynwood',
    'Apple Valley',
    'Redlands',
    'Turlock',
    'Milpitas',
    'Redondo Beach',
    'Rancho Cordova',
    'Yorba Linda',
    'Palo Alto',
    'Davis',
    'Camarillo',
    'Walnut Creek',
    'Pittsburg',
    'South San Francisco',
    'Yuba City',
    'San Clemente',
    'Laguna Niguel',
    'Pico Rivera',
    'Montebello',
    'Lodi',
    'Madera',
    'Santa Cruz',
    'La Habra',
    'Encinitas',
    'Monterey Park',
    'Tulare',
    'Cupertino',
    'Gardena',
    'National City',
    'Rocklin',
    'Petaluma',
    'Huntington Park',
    'San Rafael',
    'La Mesa',
    'Arcadia',
    'Fountain Valley',
    'Diamond Bar',
    'Woodland',
    'Santee',
    'Lake Elsinore',
    'Porterville',
    'Paramount',
    'Eastvale',
    'Rosemead',
    'Hanford',
    'Highland',
    'Brentwood',
    'Novato',
    'Colton',
    'Cathedral City',
    'Delano',
    'Yucaipa',
    'Watsonville',
    'Placentia',
    'Glendora',
    'Gilroy',
    'Palm Desert',
    'Cerritos',
    'West Sacramento',
    'Aliso Viejo',
    'Poway',
    'La Mirada',
    'Rancho Santa Margarita',
    'Cypress',
    'Dublin',
    'Covina',
    'Azusa',
    'Palm Springs',
    'San Luis Obispo',
    'Ceres',
    'San Jacinto',
    'Lincoln',
    'Newark',
    'Lompoc',
    'El Centro',
    'Danville',
    'Bell Gardens',
    'Coachella',
    'Rancho Palos Verdes',
    'San Bruno',
    'Rohnert Park',
    'Brea',
    'La Puente',
    'Campbell',
    'San Gabriel',
    'Beaumont',
    'Morgan Hill',
    'Culver City',
    'Calexico',
    'Stanton',
    'La Quinta',
    'Pacifica',
    'Montclair',
    'Oakley',
    'Monrovia',
    'Los Banos',
    'Martinez',
  ],
  Illinois: [
    'Chicago',
    'Aurora',
    'Rockford',
    'Joliet',
    'Naperville',
    'Springfield',
    'Peoria',
    'Elgin',
    'Waukegan',
    'Cicero',
    'Champaign',
    'Bloomington',
    'Arlington Heights',
    'Evanston',
    'Decatur',
    'Schaumburg',
    'Bolingbrook',
    'Palatine',
    'Skokie',
    'Des Plaines',
    'Orland Park',
    'Tinley Park',
    'Oak Lawn',
    'Berwyn',
    'Mount Prospect',
    'Normal',
    'Wheaton',
    'Hoffman Estates',
    'Oak Park',
    'Downers Grove',
    'Elmhurst',
    'Glenview',
    'DeKalb',
    'Lombard',
    'Belleville',
    'Moline',
    'Buffalo Grove',
    'Bartlett',
    'Urbana',
    'Quincy',
    'Crystal Lake',
    'Plainfield',
    'Streamwood',
    'Carol Stream',
    'Romeoville',
    'Rock Island',
    'Hanover Park',
    'Carpentersville',
    'Wheeling',
    'Park Ridge',
    'Addison',
    'Calumet City',
  ],
  Texas: [
    'Houston',
    'San Antonio',
    'Dallas',
    'Austin',
    'Fort Worth',
    'El Paso',
    'Arlington',
    'Corpus Christi',
    'Plano',
    'Laredo',
    'Lubbock',
    'Garland',
    'Irving',
    'Amarillo',
    'Grand Prairie',
    'Brownsville',
    'Pasadena',
    'McKinney',
    'Mesquite',
    'McAllen',
    'Killeen',
    'Frisco',
    'Waco',
    'Carrollton',
    'Denton',
    'Midland',
    'Abilene',
    'Beaumont',
    'Round Rock',
    'Odessa',
    'Wichita Falls',
    'Richardson',
    'Lewisville',
    'Tyler',
    'College Station',
    'Pearland',
    'San Angelo',
    'Allen',
    'League City',
    'Sugar Land',
    'Longview',
    'Edinburg',
    'Mission',
    'Bryan',
    'Baytown',
    'Pharr',
    'Temple',
    'Missouri City',
    'Flower Mound',
    'Harlingen',
    'North Richland Hills',
    'Victoria',
    'Conroe',
    'New Braunfels',
    'Mansfield',
    'Cedar Park',
    'Rowlett',
    'Port Arthur',
    'Euless',
    'Georgetown',
    'Pflugerville',
    'DeSoto',
    'San Marcos',
    'Grapevine',
    'Bedford',
    'Galveston',
    'Cedar Hill',
    'Texas City',
    'Wylie',
    'Haltom City',
    'Keller',
    'Coppell',
    'Rockwall',
    'Huntsville',
    'Duncanville',
    'Sherman',
    'The Colony',
    'Burleson',
    'Hurst',
    'Lancaster',
    'Texarkana',
    'Friendswood',
    'Weslaco',
  ],
  Pennsylvania: [
    'Philadelphia',
    'Pittsburgh',
    'Allentown',
    'Erie',
    'Reading',
    'Scranton',
    'Bethlehem',
    'Lancaster',
    'Harrisburg',
    'Altoona',
    'York',
    'State College',
    'Wilkes-Barre',
  ],
  Arizona: [
    'Phoenix',
    'Tucson',
    'Mesa',
    'Chandler',
    'Glendale',
    'Scottsdale',
    'Gilbert',
    'Tempe',
    'Peoria',
    'Surprise',
    'Yuma',
    'Avondale',
    'Goodyear',
    'Flagstaff',
    'Buckeye',
    'Lake Havasu City',
    'Casa Grande',
    'Sierra Vista',
    'Maricopa',
    'Oro Valley',
    'Prescott',
    'Bullhead City',
    'Prescott Valley',
    'Marana',
    'Apache Junction',
  ],
  Florida: [
    'Jacksonville',
    'Miami',
    'Tampa',
    'Orlando',
    'St. Petersburg',
    'Hialeah',
    'Tallahassee',
    'Fort Lauderdale',
    'Port St. Lucie',
    'Cape Coral',
    'Pembroke Pines',
    'Hollywood',
    'Miramar',
    'Gainesville',
    'Coral Springs',
    'Miami Gardens',
    'Clearwater',
    'Palm Bay',
    'Pompano Beach',
    'West Palm Beach',
    'Lakeland',
    'Davie',
    'Miami Beach',
    'Sunrise',
    'Plantation',
    'Boca Raton',
    'Deltona',
    'Largo',
    'Deerfield Beach',
    'Palm Coast',
    'Melbourne',
    'Boynton Beach',
    'Lauderhill',
    'Weston',
    'Fort Myers',
    'Kissimmee',
    'Homestead',
    'Tamarac',
    'Delray Beach',
    'Daytona Beach',
    'North Miami',
    'Wellington',
    'North Port',
    'Jupiter',
    'Ocala',
    'Port Orange',
    'Margate',
    'Coconut Creek',
    'Sanford',
    'Sarasota',
    'Pensacola',
    'Bradenton',
    'Palm Beach Gardens',
    'Pinellas Park',
    'Coral Gables',
    'Doral',
    'Bonita Springs',
    'Apopka',
    'Titusville',
    'North Miami Beach',
    'Oakland Park',
    'Fort Pierce',
    'North Lauderdale',
    'Cutler Bay',
    'Altamonte Springs',
    'St. Cloud',
    'Greenacres',
    'Ormond Beach',
    'Ocoee',
    'Hallandale Beach',
    'Winter Garden',
    'Aventura',
  ],
  Indiana: [
    'Indianapolis',
    'Fort Wayne',
    'Evansville',
    'South Bend',
    'Carmel',
    'Bloomington',
    'Fishers',
    'Hammond',
    'Gary',
    'Muncie',
    'Lafayette',
    'Terre Haute',
    'Kokomo',
    'Anderson',
    'Noblesville',
    'Greenwood',
    'Elkhart',
    'Mishawaka',
    'Lawrence',
    'Jeffersonville',
    'Columbus',
    'Portage',
  ],
  Ohio: [
    'Columbus',
    'Cleveland',
    'Cincinnati',
    'Toledo',
    'Akron',
    'Dayton',
    'Parma',
    'Canton',
    'Youngstown',
    'Lorain',
    'Hamilton',
    'Springfield',
    'Kettering',
    'Elyria',
    'Lakewood',
    'Cuyahoga Falls',
    'Middletown',
    'Euclid',
    'Newark',
    'Mansfield',
    'Mentor',
    'Beavercreek',
    'Cleveland Heights',
    'Strongsville',
    'Dublin',
    'Fairfield',
    'Findlay',
    'Warren',
    'Lancaster',
    'Lima',
    'Huber Heights',
    'Westerville',
    'Marion',
    'Grove City',
  ],
  'North Carolina': [
    'Charlotte',
    'Raleigh',
    'Greensboro',
    'Durham',
    'Winston-Salem',
    'Fayetteville',
    'Cary',
    'Wilmington',
    'High Point',
    'Greenville',
    'Asheville',
    'Concord',
    'Gastonia',
    'Jacksonville',
    'Chapel Hill',
    'Rocky Mount',
    'Burlington',
    'Wilson',
    'Huntersville',
    'Kannapolis',
    'Apex',
    'Hickory',
    'Goldsboro',
  ],
  Michigan: [
    'Detroit',
    'Grand Rapids',
    'Warren',
    'Sterling Heights',
    'Ann Arbor',
    'Lansing',
    'Flint',
    'Dearborn',
    'Livonia',
    'Westland',
    'Troy',
    'Farmington Hills',
    'Kalamazoo',
    'Wyoming',
    'Southfield',
    'Rochester Hills',
    'Taylor',
    'Pontiac',
    'St. Clair Shores',
    'Royal Oak',
    'Novi',
    'Dearborn Heights',
    'Battle Creek',
    'Saginaw',
    'Kentwood',
    'East Lansing',
    'Roseville',
    'Portage',
    'Midland',
    'Lincoln Park',
    'Muskegon',
  ],
  Tennessee: [
    'Memphis',
    'Nashville-Davidson',
    'Knoxville',
    'Chattanooga',
    'Clarksville',
    'Murfreesboro',
    'Jackson',
    'Franklin',
    'Johnson City',
    'Bartlett',
    'Hendersonville',
    'Kingsport',
    'Collierville',
    'Cleveland',
    'Smyrna',
    'Germantown',
    'Brentwood',
  ],
  Massachusetts: [
    'Boston',
    'Worcester',
    'Springfield',
    'Lowell',
    'Cambridge',
    'New Bedford',
    'Brockton',
    'Quincy',
    'Lynn',
    'Fall River',
    'Newton',
    'Lawrence',
    'Somerville',
    'Waltham',
    'Haverhill',
    'Malden',
    'Medford',
    'Taunton',
    'Chicopee',
    'Weymouth Town',
    'Revere',
    'Peabody',
    'Methuen',
    'Barnstable Town',
    'Pittsfield',
    'Attleboro',
    'Everett',
    'Salem',
    'Westfield',
    'Leominster',
    'Fitchburg',
    'Beverly',
    'Holyoke',
    'Marlborough',
    'Woburn',
    'Chelsea',
  ],
  Washington: [
    'Seattle',
    'Spokane',
    'Tacoma',
    'Vancouver',
    'Bellevue',
    'Kent',
    'Everett',
    'Renton',
    'Yakima',
    'Federal Way',
    'Spokane Valley',
    'Bellingham',
    'Kennewick',
    'Auburn',
    'Pasco',
    'Marysville',
    'Lakewood',
    'Redmond',
    'Shoreline',
    'Richland',
    'Kirkland',
    'Burien',
    'Sammamish',
    'Olympia',
    'Lacey',
    'Edmonds',
    'Bremerton',
    'Puyallup',
  ],
  Colorado: [
    'Denver',
    'Colorado Springs',
    'Aurora',
    'Fort Collins',
    'Lakewood',
    'Thornton',
    'Arvada',
    'Westminster',
    'Pueblo',
    'Centennial',
    'Boulder',
    'Greeley',
    'Longmont',
    'Loveland',
    'Grand Junction',
    'Broomfield',
    'Castle Rock',
    'Commerce City',
    'Parker',
    'Littleton',
    'Northglenn',
  ],
  'District of Columbia': ['Washington'],
  Maryland: ['Baltimore', 'Frederick', 'Rockville', 'Gaithersburg', 'Bowie', 'Hagerstown', 'Annapolis'],
  Kentucky: ['Louisville/Jefferson County', 'Lexington-Fayette', 'Bowling Green', 'Owensboro', 'Covington'],
  Oregon: [
    'Portland',
    'Eugene',
    'Salem',
    'Gresham',
    'Hillsboro',
    'Beaverton',
    'Bend',
    'Medford',
    'Springfield',
    'Corvallis',
    'Albany',
    'Tigard',
    'Lake Oswego',
    'Keizer',
  ],
  Oklahoma: [
    'Oklahoma City',
    'Tulsa',
    'Norman',
    'Broken Arrow',
    'Lawton',
    'Edmond',
    'Moore',
    'Midwest City',
    'Enid',
    'Stillwater',
    'Muskogee',
  ],
  Wisconsin: [
    'Milwaukee',
    'Madison',
    'Green Bay',
    'Kenosha',
    'Racine',
    'Appleton',
    'Waukesha',
    'Eau Claire',
    'Oshkosh',
    'Janesville',
    'West Allis',
    'La Crosse',
    'Sheboygan',
    'Wauwatosa',
    'Fond du Lac',
    'New Berlin',
    'Wausau',
    'Brookfield',
    'Greenfield',
    'Beloit',
  ],
  Nevada: ['Las Vegas', 'Henderson', 'Reno', 'North Las Vegas', 'Sparks', 'Carson City'],
  'New Mexico': ['Albuquerque', 'Las Cruces', 'Rio Rancho', 'Santa Fe', 'Roswell', 'Farmington', 'Clovis'],
  Missouri: [
    'Kansas City',
    'St. Louis',
    'Springfield',
    'Independence',
    'Columbia',
    "Lee's Summit",
    "O'Fallon",
    'St. Joseph',
    'St. Charles',
    'St. Peters',
    'Blue Springs',
    'Florissant',
    'Joplin',
    'Chesterfield',
    'Jefferson City',
    'Cape Girardeau',
  ],
  Virginia: [
    'Virginia Beach',
    'Norfolk',
    'Chesapeake',
    'Richmond',
    'Newport News',
    'Alexandria',
    'Hampton',
    'Roanoke',
    'Portsmouth',
    'Suffolk',
    'Lynchburg',
    'Harrisonburg',
    'Leesburg',
    'Charlottesville',
    'Danville',
    'Blacksburg',
    'Manassas',
  ],
  Georgia: [
    'Atlanta',
    'Columbus',
    'Augusta-Richmond County',
    'Savannah',
    'Athens-Clarke County',
    'Sandy Springs',
    'Roswell',
    'Macon',
    'Johns Creek',
    'Albany',
    'Warner Robins',
    'Alpharetta',
    'Marietta',
    'Valdosta',
    'Smyrna',
    'Dunwoody',
  ],
  Nebraska: ['Omaha', 'Lincoln', 'Bellevue', 'Grand Island'],
  Minnesota: [
    'Minneapolis',
    'St. Paul',
    'Rochester',
    'Duluth',
    'Bloomington',
    'Brooklyn Park',
    'Plymouth',
    'St. Cloud',
    'Eagan',
    'Woodbury',
    'Maple Grove',
    'Eden Prairie',
    'Coon Rapids',
    'Burnsville',
    'Blaine',
    'Lakeville',
    'Minnetonka',
    'Apple Valley',
    'Edina',
    'St. Louis Park',
    'Mankato',
    'Maplewood',
    'Moorhead',
    'Shakopee',
  ],
  Kansas: [
    'Wichita',
    'Overland Park',
    'Kansas City',
    'Olathe',
    'Topeka',
    'Lawrence',
    'Shawnee',
    'Manhattan',
    'Lenexa',
    'Salina',
    'Hutchinson',
  ],
  Louisiana: [
    'New Orleans',
    'Baton Rouge',
    'Shreveport',
    'Lafayette',
    'Lake Charles',
    'Kenner',
    'Bossier City',
    'Monroe',
    'Alexandria',
  ],
  Hawaii: ['Honolulu'],
  Alaska: ['Anchorage'],
  'New Jersey': [
    'Newark',
    'Jersey City',
    'Paterson',
    'Elizabeth',
    'Clifton',
    'Trenton',
    'Camden',
    'Passaic',
    'Union City',
    'Bayonne',
    'East Orange',
    'Vineland',
    'New Brunswick',
    'Hoboken',
    'Perth Amboy',
    'West New York',
    'Plainfield',
    'Hackensack',
    'Sayreville',
    'Kearny',
    'Linden',
    'Atlantic City',
  ],
  Idaho: [
    'Boise City',
    'Nampa',
    'Meridian',
    'Idaho Falls',
    'Pocatello',
    'Caldwell',
    "Coeur d'Alene",
    'Twin Falls',
  ],
  Alabama: [
    'Birmingham',
    'Montgomery',
    'Mobile',
    'Huntsville',
    'Tuscaloosa',
    'Hoover',
    'Dothan',
    'Auburn',
    'Decatur',
    'Madison',
    'Florence',
    'Gadsden',
  ],
  Iowa: [
    'Des Moines',
    'Cedar Rapids',
    'Davenport',
    'Sioux City',
    'Iowa City',
    'Waterloo',
    'Council Bluffs',
    'Ames',
    'West Des Moines',
    'Dubuque',
    'Ankeny',
    'Urbandale',
    'Cedar Falls',
  ],
  Arkansas: [
    'Little Rock',
    'Fort Smith',
    'Fayetteville',
    'Springdale',
    'Jonesboro',
    'North Little Rock',
    'Conway',
    'Rogers',
    'Pine Bluff',
    'Bentonville',
  ],
  Utah: [
    'Salt Lake City',
    'West Valley City',
    'Provo',
    'West Jordan',
    'Orem',
    'Sandy',
    'Ogden',
    'St. George',
    'Layton',
    'Taylorsville',
    'South Jordan',
    'Lehi',
    'Logan',
    'Murray',
    'Draper',
    'Bountiful',
    'Riverton',
    'Roy',
  ],
  'Rhode Island': ['Providence', 'Warwick', 'Cranston', 'Pawtucket', 'East Providence', 'Woonsocket'],
  Mississippi: ['Jackson', 'Gulfport', 'Southaven', 'Hattiesburg', 'Biloxi', 'Meridian'],
  'South Dakota': ['Sioux Falls', 'Rapid City'],
  Connecticut: [
    'Bridgeport',
    'New Haven',
    'Stamford',
    'Hartford',
    'Waterbury',
    'Norwalk',
    'Danbury',
    'New Britain',
    'Meriden',
    'Bristol',
    'West Haven',
    'Milford',
    'Middletown',
    'Norwich',
    'Shelton',
  ],
  'South Carolina': [
    'Columbia',
    'Charleston',
    'North Charleston',
    'Mount Pleasant',
    'Rock Hill',
    'Greenville',
    'Summerville',
    'Sumter',
    'Goose Creek',
    'Hilton Head Island',
    'Florence',
    'Spartanburg',
  ],
  'New Hampshire': ['Manchester', 'Nashua', 'Concord'],
  'North Dakota': ['Fargo', 'Bismarck', 'Grand Forks', 'Minot'],
  Montana: ['Billings', 'Missoula', 'Great Falls', 'Bozeman'],
  Delaware: ['Wilmington', 'Dover'],
  Maine: ['Portland'],
  Wyoming: ['Cheyenne', 'Casper'],
  'West Virginia': ['Charleston', 'Huntington'],
  Vermont: ['Burlington'],
};

export const US_COUNTRY_CODE = '+1';
export const US_CURRENCY = 'USD';
export const MEXICAN_CURRENCY = 'MXN';

export const SevenPercentTaxInMiami = 7; // 7% tax in Miami

export const statusListByPage: { [key: string]: string[] } = {
  stock: ['stock', 'overhauling'],
  activos: ['active', 'activo', 'in-service', 'legal-process', 'awaiting-insurance'],
  discharged: ['discharged'],
  bajas: ['readmissions'],
  invoiced: ['invoiced'],
};

export const CountriesShortNames = Object.freeze({
  [Countries['United States']]: 'us',
  [Countries.Mexico]: 'mx',
});

export const US_DEFAULT_STATE_OPTIONS = [US_STATES_OBJ[USSTATES.Florida]];
export const US_STATES_DEFAULT_CITIES = {
  [USSTATES.Florida]: [US_CITIES_OPTIONS[USCITIESNAMES.Miami]],
};

export const getUSStatesOptions = () => {
  const US_STATES_OPTIONS_FOR_CUSTOMER_REGISTERATION = Object.values(USSTATES).reduce((acc, state) => {
    const usStateOption = {
      label: state,
      value: state,
    };
    acc.push(usStateOption);
    return acc;
  }, [] as Array<{ label: string; value: string }>);
  return US_STATES_OPTIONS_FOR_CUSTOMER_REGISTERATION;
};

export const getUSCitiesBasedOnState = (state: string) => {
  if (!state) {
    state = USSTATES.Florida;
  }
  const US_CITIES_OPTION_FOR_CUSTOMERS = US_STATES_CITIES[state as keyof typeof US_STATES_CITIES].reduce(
    (acc, city) => {
      const cityOption = {
        label: city,
        value: city,
      };
      acc.push(cityOption);
      return acc;
    },
    [] as Array<{ label: string; value: string }>
  );
  return US_CITIES_OPTION_FOR_CUSTOMERS;
};

export function getFullAddressString(associate: any) {
  const street = associate?.address.addressStreet || '';
  const exterior = associate?.address.exterior || '';
  const interior = associate?.address.interior || '';
  const delegation = associate?.address.delegation || '';
  const city = cities[associate?.address.city]?.value || '';
  const state = federalEntities[associate?.address.state]?.value || '';
  const postalCode = associate?.address.postalCode || '';
  const colony = associate?.address.colony || '';

  // return `${street} ${exterior} ${interior} ${colony} ${delegation} ${state} ${postalCode}`
  const fullAddress = `${street} ${exterior} ${interior} ${colony} ${delegation} ${city}, ${state} C.P. ${postalCode}`;

  // remove extra spaces or double spaces

  return fullAddress.replace(/\s+/g, ' ').trim();
}
export const usersPaymentActionsAllowed = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

export const combineSearchParamsAndFilters = ({
  searchParams,
  filters,
}: {
  searchParams: Record<string, any>;
  filters: Record<string, any>;
}) => {
  let definitiveFilters;
  const doesSearchParamsExist = Object.keys(searchParams).length > 0;
  if (doesSearchParamsExist && filters) {
    Object.keys(searchParams).forEach((key) => {
      filters[key] = searchParams[key];
    });
    definitiveFilters = filters;
  } else if (doesSearchParamsExist) {
    definitiveFilters = searchParams;
  }
  return definitiveFilters;
};

export const MexicoRegions = [
  { label: 'CDMX', value: 'CDMX' },
  { label: 'GDL', value: 'GDL' },
  { label: 'MTY', value: 'MTY' },
  { label: 'TIJ', value: 'TIJ' },
  { label: 'PBC', value: 'PBC' },
  { label: 'QRO', value: 'QRO' },
  { label: 'PBE', value: 'PBE' },
  { label: 'TOL', value: 'TOL' },
  { label: 'PTV', value: 'PTV' },
  { label: 'TEP', value: 'TEP' },
  { label: 'COL', value: 'COL' },
  { label: 'SAL', value: 'SAL' },
  { label: 'TORR', value: 'TORR' },
  { label: 'DUR', value: 'DUR' },
  { label: 'MXLI', value: 'MXLI' },
  { label: 'HER', value: 'HER' },
  { label: 'CHI', value: 'CHI' },
  { label: 'LEO', value: 'LEO' },
  { label: 'AGS', value: 'AGS' },
  { label: 'SLP', value: 'SLP' },
  { label: 'MER', value: 'MER' },
];

export const statusMap: Record<string, string> = {
  pending: 'Pendiente',
  completed: 'Completado',
}

export const typeServiceMap: Record<string, string> = {
  'preventive': 'Preventivo',
  'corrective': 'Correctivo',
  'other': 'Otro'
}

export const DAYS_OF_WEEK_FULL = [
  { label: 'Lunes', value: 'monday' },
  { label: 'Martes', value: 'tuesday' },
  { label: 'Miércoles', value: 'wednesday' },
  { label: 'Jueves', value: 'thursday' },
  { label: 'Viernes', value: 'friday' },
  { label: 'Sábado', value: 'saturday' },
  { label: 'Domingo', value: 'sunday' },
] as const;