const moment = require('moment');
// import moment from 'moment';

const paymentsArray = (date) => {
  let lunes = date;

  let pagos = [{ day: lunes.format('DD-MM-YYYY'), number: 1, block: false }];

  for (let i = 1; i <= 155; i++) {
    pagos.push({ day: lunes.add(1, 'week').format('DD-MM-YYYY'), number: i + 1, block: false });
  }

  return pagos;
};

const date = moment('2022-03-07');
console.log(date);
const res = paymentsArray(date);

console.log(res.slice(0, 50));
console.log(res.slice(50, 100));
console.log(res.slice(100, 150));
console.log(res.slice(150, 200));
