'use server';

import { URL_API } from '@/constants';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';

interface VehicleData {
  _id: string;
  brand: string;
  model: string;
  year: number;
  carNumber: string;
  carPlates: {
    plates: string;
  };
  vin?: string;
  color?: string;
  status: string;
  drivers?: Array<{
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    curp?: string;
    rfc?: string;
  }>;
  organization?: {
    _id: string;
    name: string;
  };
}

interface ApiResponse {
  success: boolean;
  data: VehicleData | null;
  message: string;
}

export async function getVehicleByPlate(plates: string): Promise<ApiResponse> {
  const user = await getCurrentUser();
  if (!user) {
    return {
      success: false,
      data: null,
      message: 'Usuario no autenticado',
    };
  }

  try {
    console.log('🔗 Getting vehicle by plates via vendor-platform:', plates);

    // Use the vendor-platform stock search endpoint
    const response = await axios.get(
      `${URL_API}/vendor-platform/stock/search/`,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
        params: {
          search: plates, // Plates will be matched exactly in the backend
        },
      }
    );

    console.log('✅ Vehicle search response:', response.data);

    // The search endpoint returns an array, we need the first match
    const vehicles = response.data.data || response.data;
    
    if (vehicles && vehicles.length > 0) {
      // Find the exact plates match (in case there are partial matches)
      const exactMatch = vehicles.find((vehicle: any) => 
        vehicle.carPlates?.plates && 
        vehicle.carPlates.plates.toUpperCase() === plates.toUpperCase()
      );
      
      if (exactMatch) {
        return {
          success: true,
          data: exactMatch,
          message: 'Vehículo encontrado exitosamente',
        };
      }
    }

    // No exact match found
    return {
      success: false,
      data: null,
      message: 'Vehículo no encontrado con estas placas',
    };

  } catch (error: any) {
    console.error('❌ Error getting vehicle by plates:', error.response?.data || error);
    
    if (error.response?.status === 404) {
      return {
        success: false,
        data: null,
        message: 'Vehículo no encontrado con estas placas',
      };
    }

    if (error.response?.status === 401 || error.response?.status === 403) {
      return {
        success: false,
        data: null,
        message: 'Sin autorización para buscar vehículos',
      };
    }

    return {
      success: false,
      data: null,
      message: error.response?.data?.message || 'Error al buscar el vehículo',
    };
  }
}