'use server';

import { URL_API } from '@/constants';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';

interface VehicleData {
  _id: string;
  brand: string;
  model: string;
  year: number;
  carNumber: string;
  carPlates: {
    plates: string;
  };
  vin?: string;
  color?: string;
  status: string;
  drivers?: Array<{
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    curp?: string;
    rfc?: string;
  }>;
  organization?: {
    _id: string;
    name: string;
  };
}

interface ApiResponse {
  success: boolean;
  data: VehicleData | null;
  message: string;
}

export async function getVehicleByVin(vin: string): Promise<ApiResponse> {
  const user = await getCurrentUser();
  if (!user) {
    return {
      success: false,
      data: null,
      message: 'Usuario no autenticado',
    };
  }

  try {
    console.log('🔗 Getting vehicle by VIN via vendor-platform:', vin);

    // Use the vendor-platform stock search endpoint
    console.log('URL_API', URL_API);
    console.log('accessToken', user.accessToken);
    console.log('search', `${URL_API}/vendor-platform/vehicles?q=${vin}`);
    const response = await axios.get(
      `${URL_API}/vendor-platform/vehicles`,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
        params: {
          q: vin, // VIN will be matched exactly in the backend
        },
      }
    );

    console.log('✅ Vehicle search response:', response.data);

    // The search endpoint returns an array, we need the first match
    const vehicles = response.data.data;

    console.log('🔍 Searching for VIN:', vin);
    console.log('🔍 Vehicles found:', vehicles?.length || 0);

    if (vehicles && vehicles.length > 0) {
      // Debug each vehicle
      vehicles.forEach((vehicle: any, index: number) => {
        console.log(`🔍 Vehicle ${index}:`, {
          vin: vehicle.vin,
          vinUpperCase: vehicle.vin?.toUpperCase(),
          searchVinUpperCase: vin.toUpperCase(),
          match: vehicle.vin && vehicle.vin.toUpperCase() === vin.toUpperCase()
        });
      });

      // Find the exact VIN match (in case there are partial matches)
      const exactMatch = vehicles.find((vehicle: any) =>
        vehicle.vin && vehicle.vin.toUpperCase() === vin.toUpperCase()
      );

      console.log('🔍 Exact match found:', !!exactMatch);

      if (exactMatch) {
        return {
          success: true,
          data: exactMatch,
          message: 'Vehículo encontrado exitosamente',
        };
      }
    }

    // No exact match found
    return {
      success: false,
      data: null,
      message: 'Vehículo no encontrado con este VIN',
    };

  } catch (error: any) {
    console.error('❌ Error getting vehicle by VIN:', error.response?.data || error);

    if (error.response?.status === 404) {
      return {
        success: false,
        data: null,
        message: 'Vehículo no encontrado con este VIN',
      };
    }

    if (error.response?.status === 401 || error.response?.status === 403) {
      return {
        success: false,
        data: null,
        message: 'Sin autorización para buscar vehículos',
      };
    }

    return {
      success: false,
      data: null,
      message: error.response?.data?.message || 'Error al buscar el vehículo',
    };
  }
}