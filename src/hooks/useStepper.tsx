import { useState, useCallback } from 'react';

interface StepperHook {
  currentStep: number;
  nextStep: () => void;
  prevStep: () => void;
  setStep: (step: number) => void;
}

const useStepper = (initialStep: number, totalSteps: number): StepperHook => {
  const [currentStep, setCurrentStep] = useState(initialStep);

  const nextStep = useCallback(() => {
    setCurrentStep((prevStep) => {
      if (prevStep === totalSteps) return totalSteps;
      return prevStep + 1;
    });
  }, [totalSteps]);

  const prevStep = useCallback(() => {
    setCurrentStep((prev) => {
      if (prev <= 1) return 1;
      return prev - 1;
    });
  }, []);

  const setStep = useCallback((step: number) => {
    setCurrentStep(step);
  }, []);

  return {
    currentStep,
    nextStep,
    prevStep,
    setStep,
  };
};

export default useStepper;
