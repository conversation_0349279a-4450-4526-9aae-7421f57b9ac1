import { Driver } from '@/actions/getVehicleData';
import { create } from 'zustand';

interface DriverData {
  isOpen: boolean;
  driver: Driver | null;
  setDriver: (driver: Driver) => void;
  onOpen: () => void;
  onClose: () => void;
  carNumber: string;
  setCarNumber: (carNumber: string) => void;
}

export const useHandleDriverData = create<DriverData>((set) => ({
  isOpen: false,
  driver: null,
  carNumber: '',
  setCarNumber: (carNumber: string) => set(() => ({ carNumber })),
  setDriver: (driver: Driver) => set(() => ({ driver })),
  onOpen: () => set(() => ({ isOpen: true })),
  onClose: () => set(() => ({ isOpen: false })),
}));
