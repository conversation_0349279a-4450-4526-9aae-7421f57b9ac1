import { VehicleResponse } from '@/actions/getVehicleData';
import { create } from 'zustand';

interface ReadmissionData {
  isOpen: boolean;
  readmission: VehicleResponse['readmissions'][number] | null;
  setReadmission: (eadmission: VehicleResponse['readmissions'][number]) => void;
  onOpen: () => void;
  onClose: () => void;
}

export const useHandleReadmissionData = create<ReadmissionData>((set) => ({
  isOpen: false,
  readmission: null,
  setReadmission: (readmission: VehicleResponse['readmissions'][number]) => set(() => ({ readmission })),
  onOpen: () => set(() => ({ isOpen: true })),
  onClose: () => set(() => ({ isOpen: false })),
}));
