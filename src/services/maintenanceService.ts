import { apiVendorPlatform } from "@/app/[locale]/dashboard/(workshop-user-routes)/appointments/_actions/appointmentService";
import {
  MaintenanceRequest,
  MaintenanceService,
  Quotation,
  Approval,
  DiagnosticReport,
  MaintenanceHistory,
  SLAMetrics,
  FailureType,
  VehicleCondition,
  ServiceStatus,
  ApprovalStatus,
  Evidence,
} from "@/types/maintenance";

export interface ApiResponse<T> {
  message: string;
  data: T;
  success: boolean;
}

export interface CreateMaintenanceRequestDto {
  vehicleId: string;
  clientId: string;
  workshopId: string;
  failureType: FailureType;
  vehicleCondition: VehicleCondition;
  description: string;
  scheduledDate: string;
  urgency?: "low" | "medium" | "high" | "critical";
}

export interface CreateQuotationDto {
  orderId: string;
  services: {
    name: string;
    description: string;
    cost: number;
    estimatedTime: number;
    requiredParts: {
      name: string;
      partNumber: string;
      quantity: number;
      unitCost: number;
      supplier: string;
      estimatedArrival: string;
    }[];
  }[];
  notes?: string;
}

export interface UpdateServiceStatusDto {
  serviceId: string;
  status: ServiceStatus;
  evidence?: {
    type: "photo" | "video" | "document";
    file: File;
    description: string;
  }[];
  notes?: string;
}

export interface ApproveServicesDto {
  quotationId: string;
  approvedServices: string[];
  rejectedServices?: string[];
  rejectionReason?: string;
  notes?: string;
}

export const maintenanceService = {
  // Crear solicitud de mantenimiento
  createMaintenanceRequest: async (
    data: CreateMaintenanceRequestDto
  ): Promise<ApiResponse<MaintenanceRequest>> => {
    const response = await apiVendorPlatform.post("/maintenance/requests", data);
    return response.data;
  },

  // Obtener solicitudes de mantenimiento
  getMaintenanceRequests: async (filters?: {
    status?: string;
    workshopId?: string;
    vehicleId?: string;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<ApiResponse<MaintenanceRequest[]>> => {
    const response = await apiVendorPlatform.get("/maintenance/requests", {
      params: filters,
    });
    return response.data;
  },

  // Obtener solicitud específica
  getMaintenanceRequest: async (
    id: string
  ): Promise<ApiResponse<MaintenanceRequest>> => {
    const response = await apiVendorPlatform.get(`/maintenance/requests/${id}`);
    return response.data;
  },

  // Actualizar solicitud
  updateMaintenanceRequest: async (
    id: string,
    data: Partial<MaintenanceRequest>
  ): Promise<ApiResponse<MaintenanceRequest>> => {
    const response = await apiVendorPlatform.patch(
      `/maintenance/requests/${id}`,
      data
    );
    return response.data;
  },

  // Crear diagnóstico
  createDiagnostic: async (
    orderId: string,
    data: {
      findings: {
        component: string;
        issue: string;
        severity: "minor" | "moderate" | "severe" | "critical";
        description: string;
      }[];
      recommendedServices: string[];
      urgencyLevel: "low" | "medium" | "high" | "critical";
      evidence?: File[];
    }
  ): Promise<ApiResponse<DiagnosticReport>> => {
    const formData = new FormData();
    formData.append("orderId", orderId);
    formData.append("data", JSON.stringify(data));

    if (data.evidence) {
      data.evidence.forEach((file, index) => {
        formData.append(`evidence_${index}`, file);
      });
    }

    const response = await apiVendorPlatform.post(
      "/maintenance/diagnostics",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
    return response.data;
  },

  // Crear cotización
  createQuotation: async (
    data: CreateQuotationDto
  ): Promise<ApiResponse<Quotation>> => {
    const response = await apiVendorPlatform.post("/maintenance/quotations", data);
    return response.data;
  },

  // Obtener cotización
  getQuotation: async (id: string): Promise<ApiResponse<Quotation>> => {
    const response = await apiVendorPlatform.get(`/maintenance/quotations/${id}`);
    return response.data;
  },

  // Aprobar servicios
  approveServices: async (
    data: ApproveServicesDto
  ): Promise<ApiResponse<Approval>> => {
    const response = await apiVendorPlatform.post(
      "/maintenance/approvals",
      data
    );
    return response.data;
  },

  // Actualizar estado de servicio
  updateServiceStatus: async (
    data: UpdateServiceStatusDto
  ): Promise<ApiResponse<MaintenanceService>> => {
    const formData = new FormData();
    formData.append("serviceId", data.serviceId);
    formData.append("status", data.status);

    if (data.notes) {
      formData.append("notes", data.notes);
    }

    if (data.evidence) {
      data.evidence.forEach((evidence, index) => {
        formData.append(`evidence_${index}`, evidence.file);
        formData.append(`evidence_${index}_type`, evidence.type);
        formData.append(`evidence_${index}_description`, evidence.description);
      });
    }

    const response = await apiVendorPlatform.patch(
      "/maintenance/services/status",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
    return response.data;
  },

  // Obtener historial de mantenimiento
  getMaintenanceHistory: async (
    vehicleId: string
  ): Promise<ApiResponse<MaintenanceHistory>> => {
    const response = await apiVendorPlatform.get(
      `/maintenance/history/${vehicleId}`
    );
    return response.data;
  },

  // Obtener métricas SLA
  getSLAMetrics: async (filters?: {
    orderId?: string;
    workshopId?: string;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<ApiResponse<SLAMetrics[]>> => {
    const response = await apiVendorPlatform.get("/maintenance/sla-metrics", {
      params: filters,
    });
    return response.data;
  },

  // Subir evidencia
  uploadEvidence: async (
    serviceId: string,
    evidence: {
      type: "photo" | "video" | "document";
      file: File;
      description: string;
    }[]
  ): Promise<ApiResponse<Evidence[]>> => {
    const formData = new FormData();
    formData.append("serviceId", serviceId);

    evidence.forEach((item, index) => {
      formData.append(`evidence_${index}`, item.file);
      formData.append(`evidence_${index}_type`, item.type);
      formData.append(`evidence_${index}_description`, item.description);
    });

    const response = await apiVendorPlatform.post(
      "/maintenance/evidence",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
    return response.data;
  },

  // Obtener talleres disponibles
  getAvailableWorkshops: async (filters?: {
    location?: string;
    serviceType?: string;
    date?: string;
  }): Promise<ApiResponse<any[]>> => {
    const response = await apiVendorPlatform.get("/workshops/available", {
      params: filters,
    });
    return response.data;
  },

  // Agendar cita
  scheduleAppointment: async (data: {
    workshopId: string;
    vehicleId: string;
    serviceType: string;
    preferredDate: string;
    preferredTime: string;
    notes?: string;
  }): Promise<ApiResponse<any>> => {
    const response = await apiVendorPlatform.post("/appointments", data);
    return response.data;
  },
};
