/* eslint-disable max-params */
/* eslint-disable @typescript-eslint/no-use-before-define */
'use server';

import getCurrentUser from '@/actions/getCurrentUser';
import axios from 'axios';
import FormData from 'form-data';
import { getFileFromUrl } from './lib/getFileFromUrl';
import { Countries, URL_API } from '@/constants';
import getCoordinates, { getCoordinatesOfContractUS } from './lib/coordinates';
import { sendMessages } from './lib/sendMessages';
// import fs from 'fs';
// import path from 'path';

const url = process.env.WEETRUST_URL!;
interface SendDocumentToSignParams {
  urlFile: string;
  filename: string;
  associateId: string;

  associate: {
    email: string;
    name: string;
    phone: string;
  };
  aval: {
    email: string;
    name: string;
    phone: string;
  };
  stockId: string;
  withAval: boolean;
  country: Countries;
}

export const sendDocumentToSign = async ({
  urlFile,
  filename,
  associateId,
  associate,
  aval,
  stockId,
  withAval,
  country,
}: SendDocumentToSignParams) => {
  const user = await getCurrentUser();

  if (!user) throw new Error('Not authorized');

  try {
    const token = await getToken();

    const docu = await getFileFromUrl(urlFile);

    let form = new FormData();

    form.append('document', docu, {
      filename,
      contentType: 'application/pdf',
    });

    const data = await sendDocumentStep(form, token, country);

    const { documentID } = data.responseData;

    const response3 = await fixSignatory(documentID, token, { associate, aval }, withAval, country);
    if (!response3) throw new Error('Error al fijar firmantes');

    console.log('ADD FIX SIGNATORY', response3.data.message);

    const signatoryResponse = await sendSignatory(documentID, token, { associate, aval }, withAval, country);
    if (!signatoryResponse.success) {
      return {
        success: false,
        error: {
          isExpired: false,
          status: 500,
          message: signatoryResponse.error?.message || 'Error occurred while sending the document to sign',
        },
      };
    }
    const { responseData } = signatoryResponse.response?.data;

    const participants = responseData.signatory.map((s: any) => {
      return {
        name: s.name,
        email: s.emailID,
        signed: false,
        urlSign: s.signing.url,
      };
    });

    await sendMessages({
      participants,
      sendMessageParams: { associate, aval },
      withAval,
      country,
    });

    const response = await axios.post(
      `${URL_API}/associate/signature`, // backendv2
      {
        associateId,
        documentID,
        stockId,
        participants,
      },
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      }
    );

    console.log('UPDATE MONGO DB', response.data);

    return {
      success: true,
      error: null,
    };
  } catch (error: any) {
    // console.error(error.response.data.message || error.message);

    console.log(
      '----------------------------------------------------------------------------------------------------------------',
      error.message
    );
    console.log('error.response', error.response.config);
    console.log(
      '----------------------------------------------------------------------------------------------------------------'
    );
    const requestUrl = error.response?.config?.url;

    return {
      success: false,
      error: {
        isExpired: requestUrl?.includes('amazonaws.com') || false,
        status: error.response?.status || 500,
        message: error.response?.data?.message || error.message || 'Something went wrong',
      },
    };
  }
};

const getToken = async () => {
  // try {
  const response = await axios.post(
    `${url}/access/token`,
    {},
    {
      headers: {
        'user-id': process.env.WEETRUST_USER_ID,
        'api-key': process.env.WEETRUST_API_KEY,
      },
    }
  );
  return response.data.responseData.accessToken;
  // } catch (error: any) {
  //   console.error(error.response || error.message);
  //   return null;
  // }
};

const sendDocumentStep = async (form: FormData, token: string, country: Countries = Countries.Mexico) => {
  // try {
  const language = country === Countries['United States'] ? 'en' : 'es';

  const { data } = await axios.post(`${url}/documents`, form, {
    headers: {
      'user-id': process.env.WEETRUST_USER_ID!,
      ...form.getHeaders(),
      documentSignType: 'ELECTRONIC_SIGNATURE',
      country: country,
      language: language,
      position: 'geolocation',
      splitPage: '17',
      token,
    },
  });
  // const json = JSON.stringify(data, null, 2);
  // const randomId = Math.floor(Math.random() * 1000);
  // const namefile = `receive-${randomId}.json`;
  // const rootFolder = path.join(process.cwd(), 'public');
  // const filepath = path.join(rootFolder, namefile);
  // fs.writeFileSync(filepath, json);
  // console.log('save json file', filepath);
  return data;
  // } catch (error: any) {
  //   console.error(error.response || error.message);
  //   return null;
  // }
};

interface SignatoryStep {
  associate: SendDocumentToSignParams['associate'];
  aval: SendDocumentToSignParams['aval'];
}

const fixSignatory = async (
  documentID: string,
  token: string,
  { associate, aval }: SignatoryStep,
  withAval: boolean,
  country: Countries = Countries.Mexico
) => {
  // try {
  const isContractUS = country === Countries['United States'];

  const staticSignPositions = isContractUS
    ? getCoordinatesOfContractUS({
        associateEmail: associate.email,
      })
    : getCoordinates({
        associateEmail: associate.email,
        avalEmail: aval.email,
        withAval,
      });

  const body = {
    documentID,
    staticSignPositions,
  };

  const response = await axios.put(`${url}/documents/fixed-signatory`, body, {
    headers: {
      'user-id': process.env.WEETRUST_USER_ID,
      token,
    },
  });

  return response;
  // } catch (error: any) {
  //   console.error(error.response || error.message);
  //   return null;
  // }
};

const sendSignatory = async (
  documentID: string,
  token: string,
  { associate, aval }: SignatoryStep,
  withAval: boolean,
  country: Countries = Countries.Mexico
) => {
  const isContractUS = country === Countries['United States'];
  const participants: any = [
    {
      emailID: associate.email,
      name: associate.name,
      identification: process.env.WEETRUST_ID === 'true' ? 'id' : undefined,
      // phone: associate.phone,
    },
  ];
  if (!isContractUS && withAval) {
    participants.push({
      emailID: aval.email,
      name: aval.name,
      identification: process.env.WEETRUST_ID === 'true' ? 'id' : undefined,
      // phone: aval.phone,
    });
  }

  if (process.env.WEETRUST_ID === 'true') {
    participants.push({
      emailID: '<EMAIL>',
      name: 'Mairon Esteban Sandoval Gómez',
      identification: undefined,
    });
  }
  const body = {
    documentID,
    title: isContractUS ? 'Contract signing' : 'Firma de contrato',
    message: isContractUS ? 'Lease' : 'Contrato de arrendamiento',
    signatory: participants,
  };
  try {
    const response = await axios.put(`${url}/documents/signatory`, body, {
      headers: {
        'user-id': process.env.WEETRUST_USER_ID,
        token,
      },
    });

    return {
      success: true,
      response,
      error: null,
    };
  } catch (error: any) {
    const errorMsg = error.response.data || error.message;
    return {
      success: false,
      error: errorMsg,
      response: null,
    };
  }
};
