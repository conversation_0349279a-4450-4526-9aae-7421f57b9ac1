import { Countries } from '@/constants';
import { sendContractToAval, sendContractToDriver } from '@/services/hilos';

interface SendMessagesParams {
  associate: {
    email: string;
    name: string;
    phone: string;
  };
  aval: {
    email: string;
    name: string;
    phone: string;
  };
}

interface ISendMessages {
  participants: any;
  sendMessageParams: SendMessagesParams;
  withAval: boolean;
  country: Countries;
}

export const sendMessages = async ({
  participants,
  sendMessageParams: { associate, aval },
  withAval,
  country = Countries.Mexico,
}: ISendMessages) => {
  const driver = participants.find((p: any) => p.email === associate.email);
  // console.log('driver', driver);
  const isContractUS = country === Countries['United States'];
  await sendContractToDriver({
    url: driver.urlSign,
    name: associate.name,
    phone: associate.phone,
  });

  const avalData = participants.find((p: any) => p.email === aval.email);

  if (!isContractUS && avalData && withAval) {
    console.log('avalData', avalData);
    await sendContractToAval({
      url: avalData.urlSign,
      name: aval.name,
      associateName: associate.name,
      phone: aval.phone,
    });
  }

  const manuelUrl = participants.find((p: any) => p.email !== associate.email && p.email !== aval.email);

  if (manuelUrl) {
    await sendContractToAval({
      url: manuelUrl?.urlSign,
      name: 'Manuel Cangas',
      associateName: associate.name,
      phone: '5548007452',
    });
  }
};
