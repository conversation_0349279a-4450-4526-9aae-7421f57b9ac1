export default function getCoordinates({
  associateEmail,
  avalEmail,
  withAval,
}: {
  associateEmail: string;
  avalEmail: string;
  withAval: boolean;
}) {
  const coordinates = [
    {
      user: {
        email: associateEmail,
      },
      coordinates: {
        x: 363.84203480589014,
        y: 517.257813573679,
      },
      page: 7,
      pageY: 517.257813573679,
      pageYv2: 517.257813573679,
      color: '#FFD247',
      imageSize: {
        width: 119.22920122773492,
        height: 60.20484418430179,
      },
      parentImageSize: {
        width: 595.280029,
        height: 841.890015,
      },
      viewport: {
        width: 595.280029,
        height: 841.890015,
      },
    },
    {
      user: {
        email: '<EMAIL>',
      },
      coordinates: {
        x: 123.9491298527443,
        y: 528.460579064588,
      },
      page: 7,
      pageY: 528.460579064588,
      pageYv2: 528.460579064588,
      color: '#FFD247',
      imageSize: {
        width: 101,
        height: 51,
      },
      parentImageSize: {
        width: 595.280029,
        height: 841.890015,
      },
      viewport: {
        width: 595.280029,
        height: 841.890015,
      },
    },
    {
      user: {
        email: associateEmail,
      },
      coordinates: {
        x: 358.84426595269963,
        y: 432.3461580428698,
      },
      page: 9,
      pageY: 432.3461580428698,
      pageYv2: 432.3461580428698,
      color: '#FFD247',
      imageSize: {
        width: 119.22920122773492,
        height: 60.20484418430179,
      },
      parentImageSize: {
        width: 595.280029,
        height: 841.890015,
      },
      viewport: {
        width: 595.280029,
        height: 841.890015,
      },
    },
    {
      user: {
        email: '<EMAIL>',
      },
      coordinates: {
        x: 127.94734493529674,
        y: 441.5510022271715,
      },
      page: 9,
      pageY: 441.5510022271715,
      pageYv2: 441.5510022271715,
      color: '#FFD247',
      imageSize: {
        width: 101,
        height: 51,
      },
      parentImageSize: {
        width: 595.280029,
        height: 841.890015,
      },
      viewport: {
        width: 595.280029,
        height: 841.890015,
      },
    },
    {
      user: {
        email: associateEmail,
      },
      coordinates: {
        x: 117.95180722891558,
        y: 545.2287118661809,
      },
      page: 10,
      pageY: 545.2287118661809,
      pageYv2: 545.2287118661809,
      color: '#FFD247',
      imageSize: {
        width: 119.22920122773492,
        height: 60.20484418430179,
      },
      parentImageSize: {
        width: 595.280029,
        height: 841.890015,
      },
      viewport: {
        width: 595.280029,
        height: 841.890015,
      },
    },
    {
      user: {
        email: associateEmail,
      },
      coordinates: {
        x: 353.84649709950907,
        y: 606.1653117177028,
      },
      page: 11,
      pageY: 606.1653117177028,
      pageYv2: 606.1653117177028,
      color: '#FFD247',
      imageSize: {
        width: 119.22920122773492,
        height: 60.20484418430179,
      },
      parentImageSize: {
        width: 595.280029,
        height: 841.890015,
      },
      viewport: {
        width: 595.280029,
        height: 841.890015,
      },
    },
    {
      user: {
        email: '<EMAIL>',
      },
      coordinates: {
        x: 125.94823739402051,
        y: 612.3732739420935,
      },
      page: 11,
      pageY: 612.3732739420935,
      pageYv2: 612.3732739420935,
      color: '#FFD247',
      imageSize: {
        width: 101,
        height: 51,
      },
      parentImageSize: {
        width: 595.280029,
        height: 841.890015,
      },
      viewport: {
        width: 595.280029,
        height: 841.890015,
      },
    },
    {
      user: {
        email: associateEmail,
      },
      coordinates: {
        x: 257.88933511825076,
        y: 492.72074057571274,
      },
      page: 12,
      pageY: 492.72074057571274,
      pageYv2: 492.72074057571274,
      color: '#FFD247',
      imageSize: {
        width: 88.68887443259536,
        height: 44.78349105012241,
      },
      parentImageSize: {
        width: 595.280029,
        height: 841.890015,
      },
      viewport: {
        width: 595.280029,
        height: 841.890015,
      },
    },
    {
      user: {
        email: associateEmail,
      },
      coordinates: {
        x: 245.89468987059348,
        y: 259.7401633259094,
      },
      page: 14,
      pageY: 259.7401633259094,
      pageYv2: 259.7401633259094,
      color: '#FFD247',
      imageSize: {
        width: 101,
        height: 51,
      },
      parentImageSize: {
        width: 595.280029,
        height: 841.890015,
      },
      viewport: {
        width: 595.280029,
        height: 841.890015,
      },
    },
    {
      user: {
        email: associateEmail,
      },
      coordinates: {
        x: 248.89335118250784,
        y: 665.3181885671863,
      },
      page: 15,
      pageY: 665.3181885671863,
      pageYv2: 665.3181885671863,
      color: '#FFD247',
      imageSize: {
        width: 101,
        height: 51,
      },
      parentImageSize: {
        width: 595.280029,
        height: 841.890015,
      },
      viewport: {
        width: 595.280029,
        height: 841.890015,
      },
    },
    {
      user: {
        email: associateEmail,
      },
      coordinates: {
        x: 240.89692101740297,
        y: 376.618559762435,
      },
      page: 16,
      pageY: 376.618559762435,
      pageYv2: 376.618559762435,
      color: '#FFD247',
      imageSize: {
        width: 105.90056488996666,
        height: 53.47454266721089,
      },
      parentImageSize: {
        width: 595.280029,
        height: 841.890015,
      },
      viewport: {
        width: 595.280029,
        height: 841.890015,
      },
    },
    // {
    //   user: {
    //     email: associateEmail,
    //   },
    //   coordinates: {
    //     x: 82.92356278550693,
    //     y: 441.0870668153016,
    //   },
    //   page: 18,
    //   pageY: 441.0870668153016,
    //   pageYv2: 441.0870668153016,
    //   color: '#FFD247',
    //   imageSize: {
    //     width: 94.94933588402245,
    //     height: 47.94471415925886,
    //   },
    //   parentImageSize: {
    //     width: 595.280029,
    //     height: 841.890015,
    //   },
    //   viewport: {
    //     width: 595.280029,
    //     height: 841.890015,
    //   },
    // },
    // {
    //   user: {
    //     email: avalEmail,
    //   },
    //   coordinates: {
    //     x: 368.7959411880058,
    //     y: 436.0922635487834,
    //   },
    //   page: 18,
    //   pageY: 436.0922635487834,
    //   pageYv2: 436.0922635487834,
    //   color: '#FFD247',
    //   imageSize: {
    //     width: 94.94933588402245,
    //     height: 47.94471415925886,
    //   },
    //   parentImageSize: {
    //     width: 595.280029,
    //     height: 841.890015,
    //   },
    //   viewport: {
    //     width: 595.280029,
    //     height: 841.890015,
    //   },
    // },
  ];

  if (withAval) {
    coordinates.push(
      ...[
        {
          user: {
            email: associateEmail,
          },
          coordinates: {
            x: 82.92356278550693,
            y: 441.0870668153016,
          },
          page: 18,
          pageY: 441.0870668153016,
          pageYv2: 441.0870668153016,
          color: '#FFD247',
          imageSize: {
            width: 94.94933588402245,
            height: 47.94471415925886,
          },
          parentImageSize: {
            width: 595.280029,
            height: 841.890015,
          },
          viewport: {
            width: 595.280029,
            height: 841.890015,
          },
        },
        {
          user: {
            email: avalEmail,
          },
          coordinates: {
            x: 368.7959411880058,
            y: 436.0922635487834,
          },
          page: 18,
          pageY: 436.0922635487834,
          pageYv2: 436.0922635487834,
          color: '#FFD247',
          imageSize: {
            width: 94.94933588402245,
            height: 47.94471415925886,
          },
          parentImageSize: {
            width: 595.280029,
            height: 841.890015,
          },
          viewport: {
            width: 595.280029,
            height: 841.890015,
          },
        },
      ]
    );
  } else {
    coordinates.push({
      user: {
        email: associateEmail,
      },
      coordinates: {
        x: 246.8942436412316,
        y: 421.5717891610987,
      },
      page: 18,
      pageY: 421.5717891610987,
      pageYv2: 421.5717891610987,
      color: '#FFD247',
      imageSize: {
        width: 101,
        height: 51,
      },
      parentImageSize: {
        width: 595.280029,
        height: 841.890015,
      },
      viewport: {
        width: 595.280029,
        height: 841.890015,
      },
    });
  }

  return coordinates;
}

export function getCoordinatesOfContractUS({ associateEmail }: { associateEmail: string }) {
  const coordinates = [
    {
      user: {
        email: associateEmail,
      },
      coordinates: {
        x: 400.82552431950035,
        y: 473.87337933278843,
      },
      page: 15,
      pageY: 473.87337933278843,
      pageYv2: 473.87337933278843,
      color: '#FFD247',
      imageSize: {
        width: 90.31931690639384,
        height: 45.606783784416685,
      },
      parentImageSize: {
        width: 595.280029,
        height: 841.890015,
      },
      viewport: {
        width: 595.280029,
        height: 841.890015,
      },
    },
  ];
  return coordinates;
}
