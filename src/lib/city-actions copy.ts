"use server"

import getUser<PERSON>yId, { getSession } from "@/actions/getUserById";
import { apiCompanyPlatform, CityData, companyService, CrewData, NeighborhoodData, type City, type Crew, type Neighborhood } from "@/constants/companyService"

async function setHeaders() {
  try {
    // Intentar obtener la sesión primero
    const session = await getSession();
    if (session?.user?.accessToken) {
      console.log("Setting Authorization header with token from session");
      apiCompanyPlatform.defaults.headers.common['Authorization'] = `Bearer ${session.user.accessToken}`;

      // Asegurarnos de que el Content-Type esté configurado correctamente
      apiCompanyPlatform.defaults.headers.common['Content-Type'] = 'application/json';
      console.log("Content-Type header set to application/json");
      return;
    }

    // Si no hay sesión, intentar obtener el usuario
    const user = await getUserById();
    if (user?.accessToken) {
      console.log("Setting Authorization header with token from user");
      apiCompanyPlatform.defaults.headers.common['Authorization'] = `Bearer ${user.accessToken}`;

      // Asegurarnos de que el Content-Type esté configurado correctamente
      apiCompanyPlatform.defaults.headers.common['Content-Type'] = 'application/json';
      console.log("Content-Type header set to application/json");
    } else {
      console.error("No access token found in session or user");
    }
  } catch (error) {
    console.error("Error setting headers:", error);
  }
}

// Get all cities
export async function getCities(): Promise<City[] | null> {
  await setHeaders();

  try {

    const response = await companyService.getAllCities()

    return response.data;

  } catch (error: any) {
    return null;
  }

}

// Get city by ID
export async function getCityById(id: string): Promise<City | null> {

  await setHeaders();
  try {
    const response = await companyService.getCityById(id)
    return response.data;
  } catch (error: any) {
    console.log('error', error.response.data);
    return null;
  }

}

// Create new city
export async function createCity(city: CityData): Promise<City> {
  await setHeaders();
  const response = await companyService.createCity(city)
  return response.data
}

// Update city
export async function updateCity(id: string, city: Partial<City>): Promise<City | undefined> {
  await setHeaders();
  const response = await companyService.updateCity(id, city)
  return response.data
}

// Get crews by city ID
export async function getCrewsByCity(cityId: string): Promise<Crew[]> {
  await setHeaders();
  const response = await companyService.getCrewsByCityId(cityId)
  return response.data
}

// Get crew by ID
export async function getCrewById(id: string): Promise<Crew | undefined> {
  await setHeaders();
  const response = await companyService.getCrewById(id)
  return response.data
}

// Create new crew
export async function createCrew(crew: Omit<CrewData, 'members'>): Promise<Crew> {
  await setHeaders();
  const response = await companyService.createCrew(crew)
  return response.data
}

// Update crew
export async function updateCrew(id: string, crew: Partial<CrewData>): Promise<Crew | undefined> {
  await setHeaders();
  const response = await companyService.updateCrew(id, crew)
  return response.data
}

// Get neighborhoods by crew ID
export async function getNeighborhoodsByCrew(crewId: string): Promise<Neighborhood[]> {
  await setHeaders();
  const response = await companyService.getAllNeighborhoods({ params: { crewId } })
  const result = response.data.filter((neighborhood: Neighborhood) => {
    return neighborhood.crewId === crewId
  })
  return result;
}

// Get neighborhood by ID
export async function getNeighborhoodById(id: string): Promise<Neighborhood | undefined> {
  await setHeaders();
  const response = await companyService.getNeighborhoodById(id)
  return response.data
}

// Create new neighborhood
export async function createNeighborhood(neighborhood: Omit<NeighborhoodData, "_id">): Promise<Neighborhood> {
  await setHeaders();
  const response = await companyService.createNeighborhood(neighborhood)
  return response.data
}

// Update neighborhood
export async function updateNeighborhood(
  id: string,
  neighborhood: Partial<Neighborhood>,
): Promise<Neighborhood | undefined> {
  await setHeaders();
  const response = await companyService.updateNeighborhood(id, neighborhood)
  return response.data
}
