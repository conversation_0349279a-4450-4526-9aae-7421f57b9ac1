import { NextAuthOptions } from 'next-auth';
import Credentials<PERSON>rovider from 'next-auth/providers/credentials';
import axios from 'axios';
import { URL_API } from '@/constants';

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'rredentials',
      credentials: {
        email: { label: 'email', type: 'string' },
        password: { label: 'password', type: 'password' },
      },
      async authorize(credentials) {
        let user;

        try {
          const res = await axios.post(URL_API + '/vendor-platform/auth/login', credentials);
          user = res.data.user;
          if (user) {
            user.accessToken = res.data.accessToken;
            const expires = res.headers.expires;
            user.expiration = expires;
            return user;
          }
          else throw new Error("something went wrong");
        } catch (error: any) {
          // console.log(error.response.data);
          console.log('[LOGIN ERROR]', error.response.data);
          throw new Error(error.response.data.message);
        }
      },
    }),
  ],
  callbacks: {
    async session({ session, token }) {
      token.exp = token.expiration;

      session.user = token as any;
      return session;
    },
    async jwt({ token, user }) {
      token.exp = token.expiration;
      return { ...token, ...user };
    },
  },
  debug: process.env.NODE_ENV === 'development',
};
