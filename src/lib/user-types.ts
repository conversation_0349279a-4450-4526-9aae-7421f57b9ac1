export enum CompanyUserRole {
    OWNER = "owner",
    ADMIN = "admin",
    MANAGER = "manager",
    SUPERVISOR = "supervisor",
    OPERATOR = "operator",
  }
  
export type CompanyUserRoleType = `${CompanyUserRole}`;

  export interface ICompanyUserPermissions {
    id: string
    userId: string
    companyId: string
    role: CompanyUserRole
    allowedCities: string[]
    allowedCrews: string[]
    status: "active" | "invited" | "suspended"
    created: Date
    updated: Date
  }
  
  
