"use server"

import { getSession } from "@/actions/getUserById"
import { apiCompanyPlatform } from "@/constants/companyService"

// Tipo para las citas de instalación
export type InstallationAppointment = {
  _id: string
  title: string
  startTime: string
  endTime: string
  status: string
  associate: {
    firstName: string
    lastName: string
  }
  address: string
  city: string
  state: string
  zipCode: string
  createdAt: string
}

// Tipo para la respuesta paginada
export type PaginatedResponse<T> = {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// Configurar headers para las peticiones
async function setHeaders() {
  try {
    const session = await getSession();
    if (session?.user?.accessToken) {
      apiCompanyPlatform.defaults.headers.common['Authorization'] = `Bearer ${session.user.accessToken}`;
      apiCompanyPlatform.defaults.headers.common['Content-Type'] = 'application/json';
    }
  } catch (error) {
    console.error("Error setting headers:", error);
  }
}

// Obtener citas de instalación paginadas
export async function getInstallationAppointments(
  page: number = 1,
  limit: number = 10,
  search?: string,
  status?: string
): Promise<PaginatedResponse<InstallationAppointment>> {
  await setHeaders();
  
  // MOCK DATA - Reemplazar con llamada a API real
  const mockData: InstallationAppointment[] = Array.from({ length: 50 }, (_, i) => ({
    _id: `appointment-${i + 1}`,
    title: `Instalación #${i + 1}`,
    startTime: new Date(Date.now() + (i * 86400000)).toISOString(),
    endTime: new Date(Date.now() + (i * 86400000) + 7200000).toISOString(),
    status: ['pending', 'completed', 'cancelled', 'in-progress'][Math.floor(Math.random() * 4)],
    associate: {
      firstName: `Nombre${i}`,
      lastName: `Apellido${i}`
    },
    address: `Calle Principal ${i + 100}`,
    city: `Ciudad${i % 5}`,
    state: `Estado${i % 3}`,
    zipCode: `${10000 + i}`,
    createdAt: new Date(Date.now() - (i * 86400000)).toISOString()
  }));
  
  // Filtrar por búsqueda si existe
  let filteredData = mockData;
  if (search) {
    const searchLower = search.toLowerCase();
    filteredData = mockData.filter(appointment => 
      appointment.title.toLowerCase().includes(searchLower) ||
      appointment.associate.firstName.toLowerCase().includes(searchLower) ||
      appointment.associate.lastName.toLowerCase().includes(searchLower) ||
      appointment.address.toLowerCase().includes(searchLower) ||
      appointment.city.toLowerCase().includes(searchLower)
    );
  }
  
  // Filtrar por estado si existe
  if (status) {
    filteredData = filteredData.filter(appointment => appointment.status === status);
  }
  
  // Calcular paginación
  const total = filteredData.length;
  const totalPages = Math.ceil(total / limit);
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedData = filteredData.slice(startIndex, endIndex);
  
  return {
    data: paginatedData,
    total,
    page,
    limit,
    totalPages
  };
}