
import { LanguageCode } from './config';
import /* * as  */ en from './locales/en';
import /* * as  */ es from './locales/es';

// type LocalesGeneric = typeof en;

type LocalesType = {
  [key in LanguageCode]: typeof en;
};

const locales: LocalesType
  = {
  en,
  es,
};

// export function getLocaleData(locale: LanguageCode) {

//   const e = obj[locale] || en;
//   return e;
// }

export async function getLocaleData(locale: LanguageCode) {

  const localData = locales[locale] || en;

  // const resolvedData: Partial<typeof localData> = {};
  const resolvedData: any = {};

  // Usamos `keyof` para que `key` sea estrictamente una clave de `localData`
  for (const key of Object.keys(localData) as Array<keyof typeof localData>) {
    // Resolviendo dinámicamente las promesas
    resolvedData[key] = await localData[key]();
  }

  // return resolvedData as typeof localData;

  // return without Promise type 
  return resolvedData as typeof localData;


}

async function main() {
  // const re = await getLocaleData('en');
  // const dashboard = re.dashboard;

  const dashboard = await getLocaleData('en');

  console.log(dashboard);
}

main();