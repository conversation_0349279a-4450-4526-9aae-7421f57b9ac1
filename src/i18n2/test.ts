import 'server-only'

const dictionaries = {
  en: () => import('./locales/en/dashboard/index.json').then((module) => module.default),
  es: () => import('./locales/es/dashboard/index.json').then((module) => module.default),
} as const

type Locale = keyof typeof dictionaries

export const getDictionary = async (locale: Locale) => dictionaries[locale]()

async function main() {
  const en = await getDictionary('en')
  console.log(en)
}

// import 'server-only';

// import en from './locales/en';