import axios from 'axios';
import getCurrentUser from './getCurrentUser';
import { URL_API } from '@/constants';

// interface DocumentData {
//   url: string;
//   docId: string;
//   originalName: string;
// }

export interface Address {
  exteriorNumber: number;
  interiorNumber: number;
  street: string;
  zip: number;
}

export interface Associate {
  _id: string;
  firstName: string;
  lastName: string;
  active: boolean;
  address: Address;
  city: string;
  documents: string[];
  email: string;
  image: string;
  isVerified: boolean;
  name: string;
  phone: number;
  vehiclesId: any[];
}

export default async function getDriverById(id: string) {
  try {
    const user = await getCurrentUser();
    if (!user) return null;
    const response = await axios.get(`${URL_API}/associate/getAssociate/${id}`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });
    return response.data as Associate;
  } catch (error: any) {
    return null;
  }
}
