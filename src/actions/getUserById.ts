import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/authOptions';
import axios from 'axios';
import getCurrentUser from './getCurrentUser';
import { URL_API } from '@/constants';
import { cache } from 'react';

export interface DocumentData {
  url: string;
  docId: string;
  originalName: string;
}

export interface UserResponse {
  name: string;
  role: string;
  image: DocumentData;
  email: string;
  _id: string;
  city: string;
  isVerified: boolean;
  settings: {
    allowedRegions: ('cdmx' | 'gdl' | 'mty' | 'tij' | 'pbc' | 'qro')[];
  };
  accessToken: string;
  organizationId: string;
  userType: 'workshop' | 'company' | 'gestor' | 'superAdmin' | 'company-gestor';
}

export async function getSession() {
  return getServerSession(authOptions);
}

const getUserById = cache(async (userId?: string) => {
  try {
    const user = await getCurrentUser();
    if (!user) return null;

    const id = userId || user?._id;

    const response = await axios.get(`${URL_API}/vendor-platform/users/${id}`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });

    const currentUserLoggedIn = {
      ...user,
      ...response.data.data,
    };

    return currentUserLoggedIn as UserResponse;
  } catch (error: any) {
    console.log('Error:', error.response?.data)
    return null;
  }
});

export default getUserById;