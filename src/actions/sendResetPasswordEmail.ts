import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/authOptions';
import axios from 'axios';
import { URL_API } from '@/constants';

export interface ResetPasswordForm {
  email: string;
}

export async function getSession() {
  return getServerSession(authOptions);
}

export default async function sendResetPassword(body: ResetPasswordForm) {
  try {
    const data = {
      ...body,
    };

    const response = await axios.post(`${URL_API}/auth/recoverPassword`, data);

    return response.data;
  } catch (error: any) {
    return new Error(error.response.data.message);
    /* if (error.response) return error.response.data.message;
    else return error.message; */
  }
}
