import { URL_API } from '@/constants';

interface GetGestorProceduresProps {
  email: string;
  token: string;
}

interface Document {
  name: string;
  format: string;
  _id?: string;
}

interface Tramite {
  _id: string;
  name: string;
  state: string;
  documents: Document[];
  description: string;
  id: string;
}

interface Gestor {
  _id: string;
  name: string;
  phone: string;
}

interface Procedure {
  _id: string;
  tramiteId: Tramite;
  gestorId: Gestor | null;
  vehicleId: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  scheduledDate?: string;
  notes?: string;
  cost?: number;
  __v: number;
}

interface GetGestorProceduresResponse {
  message: string;
  data: Procedure[];
}

export default async function getGestorProcedures({ email, token }: GetGestorProceduresProps): Promise<GetGestorProceduresResponse> {
  try {
    const res = await fetch(`${URL_API}/vendor-platform/gestores/procedimientos/email/${email}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    console.log('RESPONSE STATUS:', res.status);

    const response = await res.json();

    return {
      ...response,
      data: response.data || []
    };
  } catch (error) {
    console.error(error);
    return {
      message: 'Error fetching procedures',
      data: []
    };
  }
}
