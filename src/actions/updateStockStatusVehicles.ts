import { URL_API } from '@/constants';
import axios from 'axios';
import { UserResponse } from './getUserById';

export interface StockCarProps {
  carNumber: string;
  brand: string;
  model: string;
  status: string;
  newCar: string;
}

export default async function updateStatus(id: string, message: string, user: UserResponse) {
  try {
    const data = {
      status: message,
    };
    const res = await axios.patch(`${URL_API}/stock/update/${id}`, data, {
      headers: {
        Authorization: `bearer ${user.accessToken}`,
      },
    });
    return res;
  } catch (error) {
    return console.error(error);
  }
}
