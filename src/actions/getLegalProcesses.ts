import { URL_API } from '@/constants';
import axios from 'axios';
import getCurrentUser from './getCurrentUser';

export interface LegalProcess {
  __v: number;
  _id: string;
  createdAt: Date;
  date: string;
  stockId: string;
  updatedAt: Date;
}

export const getLegalProcesses = async (id: string) => {
  try {
    const user = await getCurrentUser();

    const response = await axios.get(`${URL_API}/stock/getLegalProcesses/${id}`, {
      headers: {
        Authorization: `Bearer ${user?.accessToken}`,
      },
    });
    return response.data.legalProcesses as LegalProcess[];
  } catch (error) {
    return [] as LegalProcess[];
  }
};
