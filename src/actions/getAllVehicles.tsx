'use server';
import axios from 'axios';
import getCurrentUser from './getCurrentUser';
import { URL_API } from '@/constants';
import { cache } from 'react';

export interface VehicleCard {
  _id: string;
  brand: string;
  carNumber: string;
  color: string;
  extensionCarNumber: string | undefined;
  model: string;
  vehicleState: string;
  newCar: boolean;
  // status: 'stock' | 'Vehiculo listo' | 'Activo';
  status:
    | 'stock'
    | 'active'
    | 'activo'
    | 'readmissions'
    | 'legal-process'
    | 'in-service'
    | 'discharged'
    | 'invoiced';
  step: {
    stepName: string;
    stepNumber: number;
  };
  dischargedData?: {
    reason: string;
  };
  isBlocked: boolean;
}

async function getAllVehicles() {
  const user = await getCurrentUser();
  if (!user) return null;
  try {
    const response = await axios.get(`${URL_API}/stock/get`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });
    return response.data as VehicleCard[];
  } catch (error: any) {
    return null;
  }
}

const getAllVehiclesCache = cache(async () => {
  const vehicleDetail = await getAllVehicles();
  return vehicleDetail;
});

export default getAllVehiclesCache;

export interface GetVehicleProps {
  page?: number;
  limit?: number;
  status?: string;
  listStatus?: string[];
  excludeStatus?: string[];
  searchParams?: Record<string, string>;
}
interface Response {
  stock: VehicleCard[];
  totalCount: number;
  message: string;
}

export const getStockVehicles = cache(
  async ({ page, limit, status, listStatus, searchParams, excludeStatus }: GetVehicleProps = {}) => {
    const user = await getCurrentUser();
    if (!user) return null;
    try {
      const url = new URL(`${URL_API}/stock/get`);

      if (page) url.searchParams.append('page', page.toString());
      if (limit) url.searchParams.append('limit', limit.toString());
      if (status) url.searchParams.append('status', status);
      if (listStatus) url.searchParams.append('listStatus', listStatus.toString());

      if (searchParams) {
        for (const [key, value] of Object.entries(searchParams)) {
          url.searchParams.append(key, value);
        }
      }

      if (excludeStatus) {
        url.searchParams.append('excludeStatus', excludeStatus.toString());
      }

      const response = await axios.get(url.toString(), {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      });
      return response.data as Response;
    } catch (error: any) {
      console.log('error', error.response.data);
      return null;
    }
  }
);
