import { URL_API } from '@/constants';
import axios from 'axios';
import getCurrentUser from './getCurrentUser';

export interface StockService {
  dateIn: string;
  dateOut: string;
  comments: string;
  dateFinished: string | null;
  _id: string;
}

export const getStockServices = async (id: string) => {
  try {
    const user = await getCurrentUser();

    const response = await axios.get(`${URL_API}/stock/getServices/${id}`, {
      headers: {
        Authorization: `Bearer ${user?.accessToken}`,
      },
    });
    return response.data.stockServices as StockService[];
  } catch (error) {
    return [] as StockService[];
  }
};
