import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/authOptions';
import { CompanyUser, WorkshopUser, GestorUser } from '@/types';

export type UserResponse = CompanyUser | WorkshopUser | GestorUser;

export type MyUser = any;

export async function getSession() {
  return getServerSession(authOptions);
}

type UserTypeMap = {
  workshop: WorkshopUser;
  company: CompanyUser;
  gestor: GestorUser;
};

export default async function getCurrentUser<T extends keyof UserTypeMap = 'workshop'>(): Promise<UserTypeMap[T] | null> {
  try {
    const session = await getSession();
    if (!session) return null;

    const user = session.user as unknown as UserResponse;
    const expectedType = (typeof user.userType === 'string' ? user.userType : 'workshop') as T;
    if (user.userType !== expectedType) {
      throw new Error(`User type mismatch. Expected ${expectedType} but got ${user.userType}`);
    }
    return user as UserTypeMap[T];
  } catch (error: any) {
    return null;
  }
}