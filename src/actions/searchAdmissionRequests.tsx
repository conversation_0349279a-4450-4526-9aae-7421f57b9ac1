import { URL_API } from '@/constants';
import getCurrentUser from './getCurrentUser';

interface PaginationOptions {
  page: number;
}

interface AdmissionRequestSearchFilter {
  country: string;
  q: string;
  options: PaginationOptions;
}

export default async function searchAdmissionRequests({
  country,
  search,
  page,
}: {
  country: string;
  search: string;
  page: number;
}) {
  const query: AdmissionRequestSearchFilter = {
    country: country,
    q: search,
    options: {
      page,
    },
  };

  try {
    const user = await getCurrentUser();
    if (!user) return null;

    const res = await fetch(`${URL_API}/admission/requests/search`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(query),
      cache: 'no-store',
    });

    const response = await res.json();

    return response;
  } catch (error) {
    console.error(error);
    return null;
  }
}
