import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/authOptions';
import axios from 'axios';
import { URL_API } from '@/constants';
import getCurrentUser from './getCurrentUser';

export interface MyUser {
  name: string;
  role: string;
  image: string;
  email: string;
  id: string;
  city: string;
  accessToken: string;
}
export interface InvitationForm {
  name: string;
  email: string;
  city: string;
  role: string;
}

export async function getSession() {
  return getServerSession(authOptions);
}

export default async function sendInvitation(body: InvitationForm) {
  try {
    const user = await getCurrentUser();
    if (!user) return null;

    const data = {
      ...body,
      adminId: user._id,
    };

    const response = await axios.post(`${URL_API}/auth/sendInvitation`, data, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });

    return response;
  } catch (error: any) {
    return new Error(error.response.data.message);
    /* if (error.response) return error.response.data.message;
    else return error.message; */
  }
}
