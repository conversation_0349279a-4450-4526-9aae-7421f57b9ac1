import { URL_API } from '@/constants';
import getCurrentUser from './getCurrentUser';

interface GetAdmissionRequestDocAnalysisProps {
  id: string;
}

export default async function getAdmissionRequestDocAnalysis({ id }: GetAdmissionRequestDocAnalysisProps) {
  try {
    const user = await getCurrentUser();
    if (!user) return null;

    const res = await fetch(`${URL_API}/admission/requests/${id}/documents-analysis`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });

    const response = await res.json();

    return response;
  } catch (error) {
    console.error(error);
    return null;
  }
}
