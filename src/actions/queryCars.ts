import { URL_API } from '@/constants';
import getCurrentUser from './getCurrentUser';

export default async function queryCars(carNumber: string) {
  try {
    const user = await getCurrentUser();
    if (!user) return null;
    const res = await fetch(`${URL_API}/stock/get?carNumber=${carNumber}`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });
    return await res.json();
  } catch (error) {
    console.error(error);
    return null;
  }
}
