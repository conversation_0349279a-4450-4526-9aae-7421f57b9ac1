// this function is for downloading files in client side

import axios from 'axios';

export const handleDownload = async (url: string, filename: string) => {
  // const response = await fetch('/api/download-file', {
  //   method: 'POST',
  //   headers: {
  //     'Content-Type': 'application/json',
  //   },
  //   body: JSON.stringify({
  //     url,
  //     filename,
  //   }),
  // });

  const response = await axios.post(
    '/api/download-file',
    {
      url,
      filename,
    },
    {
      responseType: 'blob',
    }
  );

  // if (!response.ok) {
  //   throw new Error('Error al descargar el archivo');
  // }

  const fileExtension = url.split('.').pop();

  // const blob = await data.blob();
  const blob = response.data;
  const downloadUrl = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = downloadUrl;
  link.download = filename || `descarga.${fileExtension}`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  window.URL.revokeObjectURL(downloadUrl);

  return true;
};
