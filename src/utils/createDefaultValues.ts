import { z } from "zod";

// export function createDefaultValuesBasedOnSchema<T extends z.ZodObject<any>>(schema: T): z.infer<T> {

//   const shape = schema._def.shape();
//   const result: Record<string, any> = {};

//   // let counter = 0; // for debugging
//   for (const key in shape) {

//     // counter++
//     const field = shape[key];
//     // if (counter === 2) {
//     //   // console.log('field', field);
//     // }

//     if (field instanceof z.ZodObject) {

//       // console.log('Nested field, name: ', key);
//       const nested = createDefaultValuesBasedOnSchema(field);
//       // console.log('nested', nested);
//       result[key] = nested;

//     }

//     else if (field instanceof z.ZodEffects) {

//       // console.log('field', field);
//       // const obj = field._def?.schema?._def?.schema;
//       const obj = field._def?.schema?._def?.schema || field._def?.schema;
//       if (obj) {
//         // console.log('the object exists', key)
//         const nested = createDefaultValuesBasedOnSchema(obj);
//         result[key] = nested;
//       } else {
//         result[key] = undefined;
//       }

//     } else if (field instanceof z.ZodArray) {
//       result[key] = [];
//     } else if (field instanceof z.ZodOptional || field instanceof z.ZodNullable) {
//       result[key] = undefined;
//     } else if (field instanceof z.ZodBoolean) {
//       result[key] = false;
//     } else if (field instanceof z.ZodNumber) {
//       result[key] = undefined;
//     } else if (field instanceof z.ZodString) {
//       result[key] = undefined;
//     } else {
//       result[key] = undefined;
//     }
//   }
//   // console.log('result', result);
//   return result as z.infer<T>;
// }


// import { z } from "zod";

export function createDefaultValuesBasedOnSchema<T extends z.ZodTypeAny>(
  schema: T
): z.infer<T> {
  // Si es un esquema refinado con ZodEffects, toma el esquema base
  if (schema instanceof z.ZodEffects) {
    return createDefaultValuesBasedOnSchema(schema._def.schema);
  }

  // Si es un objeto ZodObject, procesa su forma
  if (schema instanceof z.ZodObject) {
    const shape = schema._def.shape();
    const result: Record<string, any> = {};

    for (const key in shape) {
      const field = shape[key];
      result[key] = createDefaultValuesBasedOnSchema(field);
    }
    return result as z.infer<T>;
  }

  // Si es un array, retorna un array vacío
  if (schema instanceof z.ZodArray) {
    return [] as z.infer<T>;
  }

  // Si es opcional o nullable, retorna undefined
  if (schema instanceof z.ZodOptional || schema instanceof z.ZodNullable) {
    return undefined as z.infer<T>;
  }

  // Si es un booleano, retorna false
  if (schema instanceof z.ZodBoolean) {
    return false as z.infer<T>;
  }

  // Si es un número, retorna undefined
  if (schema instanceof z.ZodNumber) {
    return undefined as z.infer<T>;
  }

  // Si es un string, retorna undefined
  if (schema instanceof z.ZodString) {
    return undefined as z.infer<T>;
  }

  // Si no coincide con ningún caso, retorna undefined
  return undefined as z.infer<T>;
}