export const CONTRACT_REGIONS = [
  {
    value: 'cdmx',
    label: 'CDMX/EDOMEX',
    code: 'cdmx',
    number: 1,
  },
  {
    value: 'gdl',
    label: 'Guadalajara',
    code: 'gdl',
    number: 2,
  },
  {
    value: 'mty',
    label: 'Monterrey',
    code: 'mty',
    number: 3,
  },
  {
    value: 'qro',
    label: 'Querétaro',
    code: 'qro',
    number: 4,
  },
  {
    value: 'tij',
    label: 'Tijuana',
    code: 'tij',
    number: 5,
  },
  {
    value: 'moka',
    label: 'MOK<PERSON>',
    code: 'moka',
    number: 6,
  },
  {
    value: 'pbe',
    label: '<PERSON><PERSON><PERSON>',
    code: 'pbe',
    number: 7,
  },
  // {
  //   value: 'tol',
  //   label: 'Tolu<PERSON>',
  //   code: 'tol',
  //   number: 8,
  // },
  {
    value: 'ptv',
    label: 'Puerto Vallarta',
    code: 'ptv',
    number: 9,
  },
  {
    value: 'tep',
    label: 'Tepic',
    code: 'tep',
    number: 10,
  },
  {
    value: 'col',
    label: 'Colima',
    code: 'col',
    number: 11,
  },
  {
    value: 'sal',
    label: 'Saltillo',
    code: 'sal',
    number: 12,
  },
  {
    value: 'torr',
    label: 'Torreon',
    code: 'torr',
    number: 13,
  },
  {
    value: 'dur',
    label: 'Durango',
    code: 'dur',
    number: 14,
  },
  {
    value: 'mxli',
    label: 'Mexicali',
    code: 'mxli',
    number: 15,
  },
  {
    value: 'her',
    label: 'Hermosillo',
    code: 'her',
    number: 16,
  },
  {
    value: 'chi',
    label: 'Chihuahua',
    code: 'chi',
    number: 17,
  },
  {
    value: 'leo',
    label: 'León',
    code: 'leo',
    number: 18,
  },
  {
    value: 'ags',
    label: 'Aguas Calientes',
    code: 'ags',
    number: 19,
  },
  {
    value: 'slp',
    label: 'San Luis Potosí',
    code: 'slp',
    number: 20,
  },
  {
    value: 'mer',
    label: 'Mérida',
    code: 'mer',
    number: 21,
  },
];

export const DOCUMENTS = [
  {
    name: 'Tarjeta de circulación frontal',
    type: 'digital'
  },
  {
    name: 'Tarjeta de circulación trasera',
    type: 'digital'
  },
  {
    name: 'Placas',
    type: 'digital'
  },
  {
    name: 'Tenencia',
    type: 'digital'
  },
  {
    name: 'Poliza',
    type: 'digital'
  },
  {
    name: 'Identificación oficial frontal',
    type: 'digital'
  },
  {
    name: 'Identificación oficial trasera',
    type: 'digital'
  },
  {
    name: 'CURP',
    type: 'digital'
  },
  {
    name: 'Acta de nacimiento',
    type: 'digital'
  },
  {
    name: 'Constancía de situación fiscal',
    type: 'digital'
  },
  {
    name: 'Factura',
    type: 'digital'
  },
  {
    name: 'Carta factura',
    type: 'físico'
  }
];
