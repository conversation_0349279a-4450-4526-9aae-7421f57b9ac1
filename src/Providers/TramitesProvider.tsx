'use client';
import React, { createContext, useContext, useEffect, useState } from 'react';
import { useCurrentUser } from './CurrentUserProvider';
import getGestorProcedures from '@/actions/getGestorProcedures';

// Define the Procedure interface
interface Document {
  name: string;
  format: string;
  _id?: string;
  url?: string;
}

interface Tramite {
  [x: string]: any;
  _id: string;
  name: string;
  state: string;
  documents: Document[];
  description: string;
  id: string;
}

interface Gestor {
  _id: string;
  name: string;
  phone: string;
}

export interface Procedure {
  _id: string;
  tramiteId: Tramite;
  gestorId: Gestor | null;
  vehicleId: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  scheduledDate?: string;
  notes?: string;
  cost?: number;
  __v?: number;
}

// Define the context props
interface TramitesContextProps {
  procedures: Procedure[];
  setProcedures: React.Dispatch<React.SetStateAction<Procedure[]>>;
  loading: boolean;
  error: string | null;
  refreshProcedures: () => Promise<void>;
  proceduresByCity: Record<string, Procedure[]>;
}

// Create the context
const TramitesContext = createContext<TramitesContextProps | undefined>(undefined);

// Create a hook to use the context
export const useTramites = () => {
  const context = useContext(TramitesContext);
  if (!context) {
    throw new Error('useTramites must be used within a TramitesProvider');
  }
  return context;
};

// Create the provider component
export const TramitesProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useCurrentUser();
  const [procedures, setProcedures] = useState<Procedure[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Group procedures by city
  const proceduresByCity = procedures.reduce((acc, procedure) => {
    const city = procedure.tramiteId?.state || 'Sin ciudad';
    if (!acc[city]) {
      acc[city] = [];
    }
    acc[city].push(procedure);
    return acc;
  }, {} as Record<string, Procedure[]>);

  // Function to fetch procedures
  const fetchProcedures = async () => {
    try {
      setLoading(true);
      if (!user?.email) {
        setError('No user email found');
        return;
      }

      const response = await getGestorProcedures({ email: user.email, token: user.accessToken });

      if (response?.data) {
        setProcedures(response.data);
        // Also store in localStorage as a fallback
        localStorage.setItem('gestorProcedures', JSON.stringify(response.data));
      } else {
        setError('No data in response');
      }
    } catch (error) {
      console.error('Error fetching procedures:', error);
      setError('Error fetching procedures');
    } finally {
      setLoading(false);
    }
  };

  // Fetch procedures on mount and when user changes
  useEffect(() => {
    if (user?.email) {
      fetchProcedures();
    }
  }, [user?.email]);

  // Try to load from localStorage on mount if procedures are empty
  useEffect(() => {
    if (procedures.length === 0 && !loading) {
      try {
        const storedProcedures = localStorage.getItem('gestorProcedures');
        if (storedProcedures) {
          const parsedProcedures = JSON.parse(storedProcedures);
          if (Array.isArray(parsedProcedures) && parsedProcedures.length > 0) {
            setProcedures(parsedProcedures);
          }
        }
      } catch (error) {
        console.error('Error loading procedures from localStorage:', error);
      }
    }
  }, [loading, procedures.length]);

  return (
    <TramitesContext.Provider
      value={{
        procedures,
        setProcedures,
        loading,
        error,
        refreshProcedures: fetchProcedures,
        proceduresByCity,
      }}
    >
      {children}
    </TramitesContext.Provider>
  );
};

export default TramitesProvider;
