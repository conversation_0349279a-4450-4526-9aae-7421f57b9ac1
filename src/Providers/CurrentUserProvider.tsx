'use client';
import React, { createContext, useContext, useEffect, useState } from 'react';
import { apiVendorPlatform } from '@/app/[locale]/dashboard/(workshop-user-routes)/appointments/_actions/appointmentService';
import { CompanyUser, UserResponse, WorkshopUser, GestorUser } from '@/types';
import { apiCompanyPlatform } from '@/constants/companyService';

interface ContextProps {
  setUser: React.Dispatch<React.SetStateAction<UserResponse>>;
  user: UserResponse;
  isSuperAdminOrAdmin: boolean;
}

const fakeUser = {} as UserResponse;
const setFakeUser = () => { };

const CurrentUserContext = createContext<ContextProps>({
  user: fakeUser,
  setUser: setFakeUser,
  isSuperAdminOrAdmin: true,
});

interface ProviderProps {
  children: React.ReactNode;
  currentUser: UserResponse;
}

const throwError = (message: string): never => {
  throw new Error(message);
};

type UserTypes = {
  workshop: WorkshopUser,
  company: CompanyUser,
  gestor: GestorUser,
  superAdmin: UserResponse,
  'company-gestor': UserResponse,
}

export const useCurrentUser = <T extends keyof UserTypes = 'workshop'>() => {
  const context = useContext(CurrentUserContext);
  if (!context) throw new Error('There is not current user context');

  const userType = context.user.userType || 'workshop';

  return {
    ...context,
    user: {
      ...context.user,
      userType,
    } as UserTypes[T]
  };
};

export const CurrentUserProvider = ({ children, currentUser }: ProviderProps) => {
  const [user, setUser] = useState(currentUser);
  const isSuperAdminOrAdmin = currentUser && (currentUser.role === 'administrador' || currentUser.role === 'superadmin' || currentUser.userType === 'superAdmin');

  useEffect(() => {
    apiVendorPlatform.interceptors.request.use(
      (config) => {
        if (user.accessToken) {
          config.headers.Authorization = `Bearer ${user.accessToken}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    apiCompanyPlatform.interceptors.request.use(
      (config) => {
        if (user.accessToken) {
          config.headers.Authorization = `Bearer ${user.accessToken}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

  }, [user.accessToken]);

  return (
    <CurrentUserContext.Provider value={{ user, setUser, isSuperAdminOrAdmin }}>
      {children}
    </CurrentUserContext.Provider>
  );
};

export default CurrentUserProvider;
