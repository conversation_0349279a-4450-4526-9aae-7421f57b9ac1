import * as Yup from 'yup';
import { fileListValidator /* fileValidator */ } from './filesValidators';
import createSelectInputValidator from './selectInputValidator';

export const sendServiceSchema = Yup.object().shape({
  dateIn: Yup.string().min(9, 'Fecha incompleta').required('Fecha requerida'),
  dateOut: Yup.string().min(9, 'Fecha incompleta').required('Fecha requerida'),
  comments: Yup.string(),
});

export const finishServiceSchema = Yup.object().shape({
  dateFinished: Yup.mixed().when('cancelStatus', {
    is: (option: any) => option.value && option.value === 'No',
    then: Yup.string()
      .min(10, 'Fecha incompleta')
      .max(10, 'La fecha tiene un numero de más')
      .required('Fecha requerida'),
    otherwise: Yup.mixed(),
  }),
  serviceImgs: Yup.mixed().when('cancelStatus', {
    is: (option: any) => option.value && option.value === 'No',
    then: fileListValidator('Debe seleccionar al menos 5 imagenes', 'all-images', 5),
    otherwise: Yup.mixed(),
  }),
  // adendumDoc: Yup.mixed().when('cancelStatus', {
  //   is: (option: any) => option.value && option.value === 'No',
  //   then: fileValidator('Debe seleccionar un archivo', 'pdf'),
  //   otherwise: Yup.mixed(),
  // }),
  // cancelStatus: createSelectInputValidator('Selecciona una opción'),
});

export const sendAwaitingInsurance = Yup.object().shape({
  date: Yup.string().min(9, 'Fecha incompleta').required('Fecha requerida'),
  comments: Yup.string(),
});

export const finishAwaitingInsuranceSchema = Yup.object().shape({
  // dateFinished: Yup.string().min(9, 'Fecha incompleta').required('Fecha requerida'),
  dateFinished: Yup.mixed().when('cancelStatus', {
    is: (option: any) => option.value && option.value === 'No',
    then: Yup.string()
      .min(10, 'Fecha incompleta')
      .max(10, 'La fecha tiene un numero de más')
      .required('Fecha requerida'),
    otherwise: Yup.mixed(),
  }),
  // adendumDoc: fileValidator('Debe seleccionar un archivo', 'pdf'),
});

export const sendLegalProcess = Yup.object().shape({
  stopPayments: Yup.string().required('Campo requerido'),
  date: Yup.string().min(9, 'Fecha incompleta').required('Fecha requerida'),
});

export const finishLegalProcess = Yup.object().shape({
  // dateFinished: Yup.mixed().when('cancelStatus', {
  //   is: (option: any) => option.value && option.value === 'No',
  //   then: Yup.string()
  //     .min(10, 'Fecha incompleta')
  //     .max(10, 'La fecha tiene un numero de más')
  //     .required('Fecha requerida'),
  //   otherwise: Yup.mixed(),
  // }),
  // returnDriver: Yup.mixed().when('cancelStatus', {
  //   is: (option: any) => option.value && option.value === 'No',
  //   then: createSelectInputValidator('Selecciona una opción'),
  //   otherwise: Yup.mixed(),
  // }),
  dateFinished: Yup.string().min(9, 'Fecha incompleta').required('Fecha requerida'),
  returnDriver: createSelectInputValidator('Selecciona una opción'),
  // adendumDoc: Yup.mixed().when('returnDriver', {
  //   is: (option: any) => option.value && option.value === 'Si',
  //   then: fileValidator('Debe seleccionar un archivo', 'pdf'),
  //   otherwise: Yup.mixed(),
  // }),
});
