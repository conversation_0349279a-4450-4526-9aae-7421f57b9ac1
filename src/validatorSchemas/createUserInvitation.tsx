import * as Yup from 'yup';
import createSelectInputValidator from './selectInputValidator';

export const createUserInvitation = Yup.object().shape({
  name: Yup.string().min(6, 'Requiere minimo 6 caracteres').required('Nombre requerido'),
  email: Yup.string()
    .when('role', {
      is: (option: any) => option.value && option.value === 'auditor',
      then: Yup.string().email('Correo invalido'),
      otherwise: Yup.string().matches(
        /^[A-Za-z0-9._%+-]+@onecarnow\.com$/,
        'Debe ser un correo de @onecarnow.com'
      ),
    })
    .required('Correo requerido'),
  city: createSelectInputValidator('Ciudad requerida'),
  role: createSelectInputValidator('Rol requerido'),
  addRegions: createSelectInputValidator('Selecciona al menos uno'),
});
