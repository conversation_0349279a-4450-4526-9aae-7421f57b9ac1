import * as Yup from 'yup';
import { ssnRegExp } from './assignAssociateSchema';

const curpRegExp = /^[A-Z]{4}\d{6}[HM][A-Z]{5}[A-Z0-9]\d$/;
const rfcRegExp = /^[A-Z]{4}[0-9]{6}[A-Z0-9]{3}$/;
const postalCodeRegExp = /^[0-9]{5}$/;

export const editPersonalDataSchema = Yup.object().shape({
  firstName: Yup.string().optional(),
  lastName: Yup.string().optional(),
  phone: Yup.string().min(10).max(14).required('Por favor, ingresa tu número de teléfono').optional(),
  email: Yup.string().email('Por favor, ingresa un correo electrónico válido').optional(),
  birthdate: Yup.date().nullable().required('Por favor, ingresa tu fecha de nacimiento válida').optional(),
  taxId: Yup.string().nullable().matches(rfcRegExp, 'Por favor, ingresa un RFC válido').optional(),
  nationalId: Yup.string().nullable().matches(curpRegExp, 'Por favor, ingresa un CURP válido').optional(),
  postalCode: Yup.string()
    .nullable()
    .matches(postalCodeRegExp, 'Por favor, ingresa un código postal válido')
    .optional(),
  city: Yup.string().nullable().min(3, 'Por favor, ingresa una ciudad válida').optional(),
  state: Yup.string().nullable().optional(),
  neighborhood: Yup.string().nullable().min(3, 'Por favor, ingresa una colonia válida').optional(),
  street: Yup.string().nullable().min(3, 'Por favor, ingresa una calle válida').optional(),
  streetNumber: Yup.string().nullable().min(1, 'Por favor, ingresa un número exterior válido').optional(),
  department: Yup.string().nullable().min(1, 'Por favor, ingresa un número interior válido').optional(),
});

export const editPersonalDataSchemaUS = Yup.object().shape({
  firstName: Yup.string().required('first name is required'),
  lastName: Yup.string().required('last name is required'),
  phone: Yup.string().min(10).max(10).required('phone number is required'),
  email: Yup.string().email('Please enter a valid email').required('email is required'),
  birthdate: Yup.string()
    .nullable()
    .test({
      name: 'age-validation',
      message: 'Age must be greater than 21',
      test: (value) => {
        if (value === null) return false;
        const date = new Date(value as unknown as Date);
        const now = new Date();
        const diff = now.getFullYear() - date.getFullYear();
        return diff >= 21;
      },
    })
    .required('birthdate is required'),
  postalCode: Yup.string()
    .nullable()
    .matches(postalCodeRegExp, 'Please enter a valid zip code')
    .required('postal code is required'),
  state: Yup.string().required('Choose a state'),
  city: Yup.string().required('Choose a city'),
  neighborhood: Yup.string().nullable().min(3, 'Por favor, ingresa una colonia válida').optional(),
  street: Yup.string().required('Street address is required'),
  streetNumber: Yup.string().nullable().optional(),
  department: Yup.string().nullable().min(1, 'Street address line 2').optional(),
  ssn: Yup.string()
    .required('This field is required')
    .matches(ssnRegExp, 'Invalid SSN, format should be xxx-xx-xxxx'),
  rideShareTotalRides: Yup.number().required('Rideshare total rides required'),
  avgEarningPerWeek: Yup.number().required('Average earning per week required'),
});
