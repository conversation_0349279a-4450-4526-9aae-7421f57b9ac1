import * as Yup from 'yup';
import createSelectInputValidator from './selectInputValidator';
import { Countries } from '@/constants';

export const curpRegExp = /^[A-Z]{4}\d{6}[HM][A-Z]{5}[A-Z0-9]\d$/;
export const rfcRegExp = /^[A-Z]{4}[0-9]{6}[A-Z0-9]{3}$/;
export const ssnRegExp = /^\d{3}-\d{2}-\d{4}$/; // us field

export const associateDataSchema2 = Yup.object().shape({
  firstName: Yup.string().min(3, 'Minimo 3 caracteres').required('Nombres requeridos'),
  lastName: Yup.string().min(3, 'Minimo 3 caracteres').required('Apellidos requeridos'),
  email: Yup.string().email('Debe ser un email').required('Email requerido'),
  phone: Yup.string().required('No. de celular requerido'),
  birthDay: Yup.string().when('country', {
    is: (country: any) => country.value === Countries['United States'],
    then: Yup.string().test({
      name: 'age-validation',
      message: 'Age must be greater than 21',
      test: (value) => {
        const date = new Date(value as unknown as Date);
        const now = new Date();
        const diff = now.getFullYear() - date.getFullYear();
        return diff >= 21;
      },
    }),
    otherwise: Yup.string().required('Fecha de nacimiento requerida'),
  }),
  curp: Yup.string().when('country', {
    is: (country: any) => country.value === Countries.Mexico, // Replace with the country code you want
    then: Yup.string().matches(curpRegExp, 'CURP inválida').required('Este campo es requerido'),
    otherwise: Yup.string().notRequired(),
  }),

  rfc: Yup.string().when('country', {
    is: (country: any) => country.value === Countries.Mexico, // Replace with the country code you want
    then: Yup.string().matches(rfcRegExp, 'Ingresa un RFC válido').required('RFC requerido'),
    otherwise: Yup.string().notRequired(),
  }),
  /**
   * US fields
   */
  country: Yup.object().shape({
    value: Yup.string().min(4, 'Minimo 4 caracteres').required('Country Required'),
    label: Yup.string().min(4, 'Minimo 4 caracteres').required('Country Required'),
  }),
  ssn: Yup.string().when('country', {
    is: (country: any) => country.value === Countries['United States'],
    then: Yup.string()
      .matches(ssnRegExp, 'Invalid SSN, format should be xxx-xx-xxxx')
      .required('This field is required'),
    otherwise: Yup.string().notRequired(),
  }),
});

export const addressDataSchemaAssociate2 = Yup.object().shape({
  addressStreet: Yup.string().required('Se requiere la calle'),
  exterior: Yup.string().when('country', {
    is: (country: any) => country.value === Countries.Mexico,
    then: Yup.string().required('Agregar numero exterior'),
    otherwise: Yup.string().notRequired(),
  }),
  interior: Yup.string().optional(),
  colony: Yup.string().when('country', {
    is: (country: any) => country.value === Countries.Mexico,
    then: Yup.string().required('Agregar colonia'),
    otherwise: Yup.string().notRequired(),
  }),
  delegation: Yup.string().when('country', {
    is: (country: any) => country.value === Countries.Mexico,
    then: Yup.string().required('Agregar alcaldia'),
    otherwise: Yup.string().notRequired(),
  }),
  city: createSelectInputValidator('Agregar ciudad'),
  postalCode: Yup.string()
    .matches(/^[0-9]+$/, 'Ingresa solo números')
    .min(5, 'Agrega un codigo valido')
    .max(5, 'Agrega un codigo valido')
    .required('Codigo postal requerido'),
  state: createSelectInputValidator('Entidad federativa requerida'),
});

/**
 * @description Schema for the form of the associate registration
 * @param {string} errorMessage - Error message to display
 * @param {string} fileType - Type of file to validate
 * @param {number} maxSize - Maximum size of the file
 * @returns {Yup.mixed} - Returns a Yup object
 */

export function fileValidator(errorMessage: string, fileType: 'pdf' | 'all-images', maxSize?: number) {
  // return (
  const obj = Yup.mixed()
    // .test('fileSize', 'El archivo no debe pesar más de 2mb', (value) => {
    //   if (!value) return true;
    //   return value && (value as File).size <= 1024 * 1024 * 5; // Tamaño máximo de 1MB
    // })
    .test('fileType', `El archivo debe ser de tipo ${fileType === 'pdf' ? 'PDF' : 'imagen'}`, (value) => {
      if (!value) return true;
      if (fileType === 'pdf') {
        return value && (value as File).type === 'application/pdf';
      } else {
        const fileTypes = ['image/jpeg', 'image/jpg', 'image/png'];
        const type = (value as File).type;
        return fileTypes.includes(type);
      }
    })

    .required(errorMessage);

  if (maxSize) {
    obj.test('fileSize', `El archivo no debe pesar más de ${maxSize}mb`, (value) => {
      if (!value) return true;
      return value && (value as File).size <= 1024 * 1024 * maxSize; // Tamaño máximo de 1MB
    });
  }

  return obj;
  // );
}

export const associateFilesSchema = Yup.object().shape({
  ineFront: fileValidator('Debe seleccionar una imagen', 'all-images'),
  ineBack: fileValidator('Debe seleccionar una imagen', 'all-images'),
  picture: fileValidator('Debe seleccionar una imagen', 'all-images'),
  curpDoc: fileValidator('Debe seleccionar un archivo', 'pdf'),
  addressVerification: fileValidator('Debe seleccionar un archivo', 'pdf'),
  taxStatus: fileValidator('Debe seleccionar un archivo', 'pdf'),
  bankStatementsOne: fileValidator('Debe seleccionar un archivo', 'pdf'),
  bankStatementsTwo: fileValidator('Debe seleccionar un archivo', 'pdf'),
  bankStatementsThree: fileValidator('Debe seleccionar un archivo', 'pdf'),
  // bankStatementsFour: fileValidator('Debe seleccionar un archivo', 'pdf'),
  // bankStatementsFive: fileValidator('Debe seleccionar un archivo', 'pdf'),
  // bankStatementsSix: fileValidator('Debe seleccionar un archivo', 'pdf'),
  driverLicenseFront: fileValidator('Debe seleccionar una imagen', 'all-images'),
  driverLicenseBack: fileValidator('Debe seleccionar una imagen', 'all-images'),
});

// empty because the fields are optional
export const taxDataSchema = Yup.object().shape({
  // tax_system: createSelectInputValidator('Selecciona un régimen fiscal'),
  // use_cfdi: createSelectInputValidator('Selecciona un uso de CFDI'),
  // legal_name: Yup.string().required('Nombre fiscal requerido'),
});

export const avalSchema = Yup.object().shape({
  avalName: Yup.string().required('Nombre requerido'),
  avalEmail: Yup.string().email('Debe ser un email').required('Email requerido'),
  avalPhone: Yup.string()
    .min(10, 'Mínimo 10 caracteres')
    .min(10, 'Máximo 10 caracteres')
    .required('Teléfono requerido'),
  avalAddress: Yup.string().required('Dirección requerida'),
  avalINE: fileValidator('Debe seleccionar una imagen', 'all-images', 5),
});

export const contactsSchema = Yup.object({
  contacts: Yup.array()
    .of(
      Yup.object({
        contactName: Yup.string().required('Requerido'),
        contactKinship: Yup.string().required('Requerido'),
        contactPhone: Yup.string().required('Requerido'),
        contactAddress: Yup.string().required('Requerido'),
      })
    )
    .min(2, 'Debe agregar al menos dos contactos'),
});

export const driverRecordSchemaUS = Yup.object().shape({
  rideShareTotalRides: Yup.number().required('Rideshare total rides required'),
  avgEarningPerWeek: Yup.number().required('Average earning per week required'),
  mobilityPlatforms: Yup.array()
    .of(Yup.object({ label: Yup.string(), value: Yup.string() }))
    .min(1, 'Mobility platforms required'),
  // drivingRecord: fileValidator('You must select an image', 'pdf'),
  // rideShareDates: fileValidator('You must select an image', 'all-images'),
  // rideShareRideHistory: fileValidator('You must select an image', 'all-images'),
  // avgWeeklyIncomeOfLastTwelveWeeks: Yup.array()
  //   .min(12, 'You must upload at least 12 screenshots of WeeklyIncomeOfLastTwelveWeeks')
  //   .max(12, 'You can only upload exactly 12 screenshots of WeeklyIncomeOfLastTwelveWeeks')
  //   .required('You must upload 12 screenshots of WeeklyIncomeOfLastTwelveWeeks')
  //   .test('is-twelve-screenshots', 'You must upload exactly 12 screenshots', (value) => {
  //     return !!value && value.length === 12;
  //   }),
});

export const emergencyContactsSchemaUS = Yup.object({
  emergencyContactName: Yup.string().required('Emergency contact name required'),
  emergencyContactPhone: Yup.string()
    .min(10, 'Minimum 10 caracteres')
    .min(10, 'Maximum 10 caracteres')
    .required('Emergency contact phone required'),
  emergencyContactRelation: Yup.string().required('Emergency contact relation required'),
});

export const associateFilesSchemaUS = Yup.object().shape({
  picture: fileValidator('You must select an image', 'all-images'),
  addressVerification: fileValidator('You must select a file', 'pdf'),
  driverLicenseFront: fileValidator('You must select an image', 'all-images'),
  driverLicenseBack: fileValidator('You must select an image', 'all-images'),
  garage: fileValidator('You must select an image', 'all-images'),
  bankStatementsOne: fileValidator('You must select a file', 'pdf'),
  bankStatementsTwo: fileValidator('You must select a file', 'pdf'),
  bankStatementsThree: fileValidator('You must select a file', 'pdf'),
  bankStatementsFour: fileValidator('You must select a file', 'pdf'),
  bankStatementsFive: fileValidator('You must select a file', 'pdf'),
  bankStatementsSix: fileValidator('You must select a file', 'pdf'),
  // proofOfCompletionOfAnyRequiredSafetyCourses: fileValidator('You must select an image', 'all-images'),
});

export const associateConsentSchemaUS = Yup.object().shape({
  // signature: fileValidator('Signature is required', 'pdf'),
  termsAndConditions: Yup.boolean().oneOf([true], 'You must accept the terms and conditions'),
  dataPrivacyConsentForm: Yup.boolean().oneOf([true], 'You must accept the data privacy consent form'),
});
