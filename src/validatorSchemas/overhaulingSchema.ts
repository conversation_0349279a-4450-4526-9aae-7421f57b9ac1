import * as Yup from 'yup';
import { fileListValidator, fileValidator } from './filesValidators';

export const sendOverHaulingSchema = Yup.object().shape({
  dateIn: Yup.string().min(9, 'Fecha incompleta').required('<PERSON><PERSON> requerida'),
  inImgs: fileListValidator('Debe seleccionar al menos 5 imagenes', 'all-images', 5),
  quotationDoc: fileValidator('Debe seleccionar un archivo', 'pdf'),
  comments: Yup.string(),
});

export const finishOverHaulingSchema = Yup.object().shape({
  dateOut: Yup.string().min(9, 'Fecha incompleta').required('Fecha requerida'),
  outImgs: fileListValidator('Debe seleccionar al menos 5 imagenes', 'all-images', 5),
});
