import * as Yup from 'yup';
import createSelectInputValidator from './selectInputValidator';
import { fileValidator } from './assignAssociateSchema';

export const dischargeVehicleSchema = Yup.object().shape({
  reason: createSelectInputValidator('Debe seleccionar un motivo'),
  comments: Yup.string(),
  date: Yup.string().required('<PERSON>cha requerida'),
  platesDischargedDoc: fileValidator('Debe seleccionar un archivo', 'pdf'),
  dictamenDoc: Yup.mixed().when('reason', {
    is: (option: any) => option.value && option.value === 'Pérdida total',
    then: fileValidator('Debe seleccionar un archivo', 'pdf'),
    otherwise: Yup.string(),
  }),
  reportDoc: Yup.mixed().when('reason', {
    is: (option: any) => option.value && option.value === 'Robo',
    then: fileValidator('Debe seleccionar un archivo', 'pdf'),
    otherwise: Yup.string(),
  }),
});
