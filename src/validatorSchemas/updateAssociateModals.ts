import * as Yup from 'yup';
import { fileValidator } from './assignAssociateSchema';
import { fileListValidator } from './filesValidators';

export const ineFrontValidatorSchema = Yup.object().shape({
  ineFront: fileValidator('Debe seleccionar un archivo', 'all-images'),
});

export const ineBackValidatorSchema = Yup.object().shape({
  ineBack: fileValidator('Debe seleccionar un archivo', 'all-images'),
});

export const curpValidatorSchema = Yup.object().shape({
  curp: fileValidator('Debe seleccionar un archivo', 'pdf'),
});

export const taxStatusValidatorSchema = Yup.object().shape({
  taxStatus: fileValidator('Debe seleccionar un archivo', 'pdf'),
});
// taxStatusValidatorSchema

export const addressVerificationValidatorSchema = Yup.object().shape({
  addressVerification: fileValidator('Debe seleccionar un archivo', 'pdf'),
});

export const driverLicenseFrontValidatorSchema = Yup.object().shape({
  driverLicenseFront: fileValidator('Debe seleccionar un archivo', 'all-images'),
});
export const driverLicenseBackValidatorSchema = Yup.object().shape({
  driverLicenseBack: fileValidator('Debe seleccionar un archivo', 'all-images'),
});

export const garageImgValidatorSchema = Yup.object().shape({
  garage: fileValidator('Debe seleccionar un archivo', 'all-images'),
});

export const deliveredImagesValidatorSchema = Yup.object().shape({
  deliveredImages: fileListValidator('Debe seleccionar un archivo', 'all-images', 5),
});
