import * as Yup from 'yup';

export const addPlacasSchema = Yup.object().shape({
  plates: Yup.string().max(12, 'Maximo 12 caracteres').required('No. de placas requerido'),
  // frontImg: Yup.mixed()
  //   // .test('fileSize', 'El archivo no debe pesar más de 2mb', (value) => {
  //   //   if (!value) return true;
  //   //   return value && (value as File).size <= 1024 * 1024 * 2; // Tamaño máximo de 1MB
  //   // })
  //   .test('fileType', 'El archivo debe ser de tipo imagen', (value) => {
  //     if (!value) return true;
  //     /* return value && (value as File).type === 'application/pdf'; */
  //     const fileTypes = ['image/jpeg', 'image/jpg', 'image/png'];
  //     const fileType = (value as File).type;
  //     return fileTypes.includes(fileType);
  //   })
  //   .required('Debe seleccionar una imagen'),
  // backImg: Yup.mixed()
  //   // .test('fileSize', 'El archivo no debe pesar más de 2mb', (value) => {
  //   //   if (!value) return true;
  //   //   return value && (value as File).size <= 1024 * 1024 * 2; // Tamaño máximo de 1MB
  //   // })
  //   .test('fileType', 'El archivo debe ser de tipo imagen', (value) => {
  //     if (!value) return true;
  //     /* return value && (value as File).type === 'application/pdf'; */
  //     const fileTypes = ['image/jpeg', 'image/jpg', 'image/png'];
  //     const fileType = (value as File).type;
  //     return fileTypes.includes(fileType);
  //   })
  //   .required('Debe seleccionar una imagen'),
  // platesDocument: Yup.mixed()
  //   // .test('fileSize', 'El archivo no debe pesar más de 2mb', (value) => {
  //   //   if (!value) return true;
  //   //   return value && (value as File).size <= 1024 * 1024; // Tamaño máximo de 1MB
  //   // })
  //   .test('fileType', 'El archivo debe ser de tipo PDF', (value) => {
  //     if (!value) return true;
  //     return value && (value as File).type === 'application/pdf';
  //   })
  //   .required('Debe seleccionar un archivo'),
});

export const addPlacasSchemaNotAdmin = Yup.object().shape({
  plates: Yup.string().max(12, 'Maximo 12 caracteres').required('No. de placas requerido'),
  frontImg: Yup.mixed()
    // .test('fileSize', 'El archivo no debe pesar más de 2mb', (value) => {
    //   if (!value) return true;
    //   return value && (value as File).size <= 1024 * 1024 * 2; // Tamaño máximo de 1MB
    // })
    .test('fileType', 'El archivo debe ser de tipo imagen', (value) => {
      if (!value) return true;
      /* return value && (value as File).type === 'application/pdf'; */
      const fileTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      const fileType = (value as File).type;
      return fileTypes.includes(fileType);
    })
    .required('Debe seleccionar una imagen'),
  backImg: Yup.mixed()
    // .test('fileSize', 'El archivo no debe pesar más de 2mb', (value) => {
    //   if (!value) return true;
    //   return value && (value as File).size <= 1024 * 1024 * 2; // Tamaño máximo de 1MB
    // })
    .test('fileType', 'El archivo debe ser de tipo imagen', (value) => {
      if (!value) return true;
      /* return value && (value as File).type === 'application/pdf'; */
      const fileTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      const fileType = (value as File).type;
      return fileTypes.includes(fileType);
    })
    .required('Debe seleccionar una imagen'),
  platesDocument: Yup.mixed()
    // .test('fileSize', 'El archivo no debe pesar más de 2mb', (value) => {
    //   if (!value) return true;
    //   return value && (value as File).size <= 1024 * 1024; // Tamaño máximo de 1MB
    // })
    .test('fileType', 'El archivo debe ser de tipo PDF', (value) => {
      if (!value) return true;
      return value && (value as File).type === 'application/pdf';
    })
    .required('Debe seleccionar un archivo'),
});

export const addCirculationCard = Yup.object().shape({
  number: Yup.string().max(16, 'Maximo 16 caracteres').required('No. de placas requerido'),
  validity: Yup.string().max(10, 'Maximo 10 caracteres').required('Vigencia requerida'),
  frontImg: Yup.mixed()
    // .test('fileSize', 'El archivo no debe pesar más de 2mb', (value) => {
    //   if (!value) return true;
    //   return value && (value as File).size <= 1024 * 1024 * 2; // Tamaño máximo de 1MB
    // })
    .test('fileType', 'El archivo debe ser de tipo imagen', (value) => {
      if (!value) return true;
      /* return value && (value as File).type === 'application/pdf'; */
      const fileTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      const fileType = (value as File).type;
      return fileTypes.includes(fileType);
    })
    .required('Debe seleccionar una imagen'),
  backImg: Yup.mixed()
    // .test('fileSize', 'El archivo no debe pesar más de 2mb', (value) => {
    //   if (!value) return true;
    //   return value && (value as File).size <= 1024 * 1024 * 2; // Tamaño máximo de 1MB
    // })
    .test('fileType', 'El archivo debe ser de tipo imagen', (value) => {
      if (!value) return true;
      /* return value && (value as File).type === 'application/pdf'; */
      const fileTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      const fileType = (value as File).type;
      return fileTypes.includes(fileType);
    })
    .required('Debe seleccionar una imagen'),
});

export const addGPSSchema = Yup.object().shape({
  gpsNumber: Yup.string().max(16, 'Maximo 16 caracteres').required('Nombre del gps requerido'),
  gpsSerie: Yup.string().max(16, 'Maximo 16 caracteres').required('No. de serie requerido'),
});

export const addPolicySchema = Yup.object().shape({
  policyNumber: Yup.string().required('No. de póliza requerido'),
  insurer: Yup.string().required('Aseguradora requerida'),
  validity: Yup.string().required('Vigencia requerida'),
  broker: Yup.string().required('Broker requerido'),
  policyDocument: Yup.mixed()
    // .test('fileSize', 'El archivo no debe pesar más de 2mb', (value) => {
    //   if (!value) return true;
    //   return value && (value as File).size <= 1024 * 1024; // Tamaño máximo de 1MB
    // })
    .test('fileType', 'El archivo debe ser de tipo PDF', (value) => {
      if (!value) return true;
      return value && (value as File).type === 'application/pdf';
    })
    .required('Debe seleccionar un archivo'),
});

export const addTenancy = Yup.object().shape({
  payment: Yup.string().min(4, 'Minimo 4 caracteres').required('No. de placas requerido'),
  validity: Yup.string().max(10, 'Maximo 10 caracteres').required('Vigencia requerida'),
  tenancyDocument: Yup.mixed()
    // .test('fileSize', 'El archivo no debe pesar más de 2mb', (value) => {
    //   if (!value) return true;
    //   return value && (value as File).size <= 1024 * 1024; // Tamaño máximo de 1MB
    // })
    .test('fileType', 'El archivo debe ser de tipo PDF', (value) => {
      if (!value) return true;
      return value && (value as File).type === 'application/pdf';
    })
    .required('Debe seleccionar un archivo'),
});
