import * as Yup from 'yup';

export const createAdmissionRequestSchemaUS = Yup.object().shape({
  firstName: Yup.string().optional(),
  lastName: Yup.string().optional(),
  phone: Yup.string().min(10).max(14).required('Please enter a valid phone number').optional(),
  email: Yup.string().email('Please enter a valid email').optional(),
  state: Yup.string().required('Choose a state'),
  city: Yup.string().required('Choose a city'),
  postalCode: Yup.string().optional(),
});

export const createAdmissionRequestSchema = Yup.object().shape({
  firstName: Yup.string().optional(),
  lastName: Yup.string().optional(),
  phone: Yup.string().min(10).max(14).required('Por favor, ingresa tu número de teléfono').optional(),
  email: Yup.string().email('Por favor, ingresa un correo electrónico válido').optional(),
  postalCode: Yup.string().optional(),
});
