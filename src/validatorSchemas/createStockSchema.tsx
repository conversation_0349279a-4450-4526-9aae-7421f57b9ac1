import * as Yup from 'yup';
import createSelectInputValidator from './selectInputValidator';
import { Countries } from '@/constants';

export const createStockSchema = Yup.object().shape({
  vehicleState: createSelectInputValidator('Ciudad del vehículo requerido'),
  brand: Yup.string().required('Marca requerida'),
  model: Yup.string().required('Modelo requerido'),
  version: Yup.string().required('Versión requerido'),
  year: Yup.string().required('Año requerido'),
  // color: Yup.string().required('Color requerido'),
  color: createSelectInputValidator('Color requerido'),
  km: Yup.string().when('country', {
    is: (country: any) => country.value === Countries.Mexico,
    then: Yup.string().required('Kilometros requeridos'),
    otherwise: Yup.string().notRequired(),
  }),
  vin: Yup.string().required('Vin requerido'),
  owner: Yup.string().required('Dueño requerido'),
  billAmount: Yup.string().required('Valor de factura requerida'),
  billDate: Yup.string()
    .max(10, 'Fecha inválida')
    .min(10, 'Fecha inválida')
    .matches(
      /^(?:19|20)\d\d-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\d|2\d|3[01])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)$/,
      'Fecha inválida'
    )
    .test('year', 'Fecha inválida', (value) => {
      if (!value) return true;
      const year = parseInt(value.split('-')[0], 10);
      return year >= 1900; /* && year <= new Date().getFullYear(); */
    })
    .required('Fecha de factura requerida'),

  mi: Yup.string().when('country', {
    is: (country: any) => country.value === Countries['United States'],
    then: Yup.string().required('Miles is required'),
    otherwise: Yup.string().notRequired(),
  }),

  // vehiclePhoto: Yup.mixed()
  //   .test('fileSize', 'El archivo no debe pesar más de 2mb', (value) => {
  //     if (!value) return true;
  //     return value && (value as File).size <= 1024 * 1024 * 2; // Tamaño máximo de 1MB
  //   })
  //   .test('fileType', 'El archivo debe ser de tipo imagen', (value) => {
  //     if (!value) return true;
  //     /* return value && (value as File).type === 'application/pdf'; */
  //     const fileTypes = ['image/jpeg', 'image/jpg', 'image/png'];
  //     const fileType = (value as File).type;
  //     return fileTypes.includes(fileType);
  //   })
  //   .required('Debe seleccionar una imagen'),
  // bill: Yup.mixed()
  //   .test('fileSize', 'El archivo no debe pesar más de 2mb', (value) => {
  //     if (!value) return true;
  //     return value && (value as File).size <= 1024 * 1024 * 2; // Tamaño máximo de 1MB
  //   })
  //   .test('fileType', 'El archivo debe ser de tipo PDF', (value) => {
  //     if (!value) return true;
  //     return value && (value as File).type === 'application/pdf';
  //   })
  //   .required('Debe seleccionar un archivo'),
});

export const createFilterSchema = Yup.object().shape({
  region: createSelectInputValidator('Region requerida'),
});
