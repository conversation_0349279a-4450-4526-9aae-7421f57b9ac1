import * as Yup from 'yup';
import { fileListValidator, fileValidator } from './filesValidators';
import createSelectInputValidator from './selectInputValidator';

export const returnToStockSchemaStep1 = Yup.object().shape({
  km: Yup.string().required('Kilometraje es requerido'),
  kmImgs: fileListValidator('Debe seleccionar un archivo', 'all-images'),
  evidenceImgs: fileListValidator('Debe seleccionar un archivo', 'all-images'),
  isSignedDoc: createSelectInputValidator('Selecciona una opción'),
  promissoryNote: Yup.mixed().when('isSignedDoc', {
    is: (option: any) => option.value && option.value === 'Si',
    then: fileValidator('Debe seleccionar un archivo', 'pdf'),
    otherwise: Yup.mixed(),
  }),
  comments: Yup.mixed().when('isSignedDoc', {
    is: (option: any) => option.value && option.value === 'No',
    then: Yup.string().required('Comentario requerido'),
    otherwise: Yup.mixed(),
  }),
});

export const returnToStockSchemaStep2 = Yup.object().shape({
  readmissionDoc: fileValidator('Debe seleccionar un archivo', 'pdf'),
  contractCanceled: fileValidator('Debe seleccionar un archivo', 'pdf'),
});
