import * as Yup from 'yup';
import createSelectInputValidator from './selectInputValidator';
import moment from 'moment';
import { Countries } from '@/constants';
import { ssnRegExp } from './assignAssociateSchema';

export const CountryContractSchemaUS = Yup.object().shape({
  country: createSelectInputValidator('Country is required'),
});

export const cityContractSchema = Yup.object().shape({
  city: createSelectInputValidator('Ciudad requerida'),
});

export const associateDataSchema = Yup.object().shape({
  firstName: Yup.string().min(3, 'Minimo 3 caracteres').required('Nombres requeridos'),
  lastName: Yup.string().min(3, 'Minimo 3 caracteres').required('Apellidos requeridos'),
  phone: Yup.string().required('No. de celular requerido'),
  tel: Yup.string(),
  email: Yup.string().email('Debe ser un email').required('Email requerido'),
  contractNumber: Yup.string().required('Numero de contrato requerido'),
});

export const addressDataSchema = Yup.object().shape({
  street: Yup.string().required('Se requiere la calle'),
  exterior: Yup.string().required('Agregar numero exterior'),
  interior: Yup.string(),
  colony: Yup.string().required('Agregar colonia'),
  postalCode: Yup.string()
    .matches(/^[0-9]+$/, 'Ingresa solo números')
    .min(5, 'Agrega un codigo valido')
    .max(5, 'Agrega un codigo valido')
    .required('Codigo postal requerido'),
  delegation: Yup.string().required('Agregar alcaldia'),
  state: createSelectInputValidator('Entidad federativa requerida'),
});

export const addressDataSchemaUS = Yup.object().shape({
  street: Yup.string().required('Street is required'),
  interior: Yup.string(),
  postalCode: Yup.string()
    .matches(/^[0-9]+$/, 'Enter only numbers')
    .min(5, 'Add a valid code')
    .max(5, 'Add a valid code')
    .required('Codigo postal requerido'),
  state: createSelectInputValidator('Country is required'),
  city: createSelectInputValidator('City is required'),
});

export const facturationSchema = Yup.object().shape({
  rfc: Yup.string().when('country', {
    is: (country: any) => country.value === Countries.Mexico,
    then: Yup.string()
      .matches(/^[A-Z]{4}[0-9]{6}[A-Z0-9]{3}$/, 'Ingresa un RFC válido')
      .required('RFC requerido'),
    otherwise: Yup.string().notRequired(),
  }),
  ssn: Yup.string().when('country', {
    is: (option: any) => option.value === Countries['United States'],
    then: Yup.string().matches(ssnRegExp, 'SSN is required').required(),
    otherwise: Yup.string().notRequired(),
  }),
});

export const facturationAddressSchema = Yup.object().shape({
  streetDos: Yup.string().required('Se requiere la calle'),
  exteriorDos: Yup.string().required('Agregar numero exterior'),
  interiorDos: Yup.string(),
  colonyDos: Yup.string().required('Agregar colonia'),
  zipDos: Yup.string()
    .matches(/^[0-9]+$/, 'Ingresa solo números')
    .min(5, 'Agrega un codigo valido')
    .max(5, 'Agrega un codigo valido')
    .required('Codigo postal requerido'),
  townHallDos: Yup.string().required('Agregar alcaldia'),
  stateDos: createSelectInputValidator('Entidad federativa requerida'),
});

export const vehicleDataSchema = Yup.object().shape({
  // brand: Yup.string().required('Marca requerida'),
  // model: Yup.string().required('Agregar modelo'),
  // version: Yup.string(),
  // weeklyRent: Yup.string()
  //   .matches(/^[0-9]+$/, 'Ingresa solo números')
  //   .required('Pago semanal requerido'),
  // finalPrice: Yup.string()
  //   .matches(/^[0-9]+$/, 'Ingresa solo números')
  //   .required('Precio final requerido'),
  // // wroteFinalPrice: Yup.string().required('Agregar precio final escrito'),
  // insurance: createSelectInputValidator('Aseguradora requerida'),
  // addDownPayment: Yup.string(),
  rentingType: createSelectInputValidator('Tipo de renta requerido'),
});

export const appointmentSchema = Yup.object().shape({
  deliverDate: Yup.string().min(16, 'Fecha requerida').max(16, 'Fecha inválida').required('Fecha requerida'),
  // deliverer: createSelectInputValidator('Selecciona el responsable de entrega'),
  // validate if the date is monday, if not show an error, is required
  isSameDate: createSelectInputValidator('Selecciona una opción'),
  // startDate: Yup.string()
  //   .min(10, 'Fecha requerida')
  //   .max(10, 'Fecha inválida')
  //   .test('is-monday', 'La fecha de inicio debe ser un lunes', (value) => {
  //     console.log('value ', value, value?.length);
  //     if (!value) return false;
  //     const date = new Date(value!);
  //     const day = date.getDay();
  //     console.log('day ', day);
  //     return day === 1;
  //   }),
  startDate: Yup.string().when('isSameDate', {
    // is: 'Si',
    is: (option: any) => option.value && option.value === 'Si',
    then: Yup.string(), // if is yes, then is not required
    otherwise: Yup.string()
      .min(16, 'Fecha requerida')
      .max(16, 'Fecha inválida')
      .test('is-monday', 'La fecha de inicio debe ser un lunes', (value) => {
        // console.log('value ', value, value?.length);
        if (!value) return false;
        const selected = moment(value);
        // console.log('selected ', selected.toString());
        const daySelected = moment(selected).day();
        // console.log('daySelected ', daySelected);
        return daySelected === 1;
      })
      .required('Fecha requerida'),
  }),
});

// downPayment: Yup.string().when('addDownPayment', {
//   is: '1',
//   then: Yup.string().required('Enganche requerido'),
//   otherwise: Yup.string(),
// }),

export const appointmentSchemaUS = Yup.object().shape({
  deliverDate: Yup.string().min(16, 'Required date').max(16, 'Invalid date').required('Required date'),
  isSameDate: createSelectInputValidator('Selecciona una opción'),
  startDate: Yup.string().when('isSameDate', {
    is: (option: any) => option.value && option.value === 'Yes',
    then: Yup.string(),
    otherwise: Yup.string()
      .min(16, 'Required date')
      .max(16, 'Invalid date')
      .test('is-monday', 'Start date must be a Monday', (value) => {
        if (!value) return false;
        const selected = moment(value);
        const daySelected = moment(selected).day();
        return daySelected === 1;
      })
      .required('Required date'),
  }),
});
