@baseUrl = http://localhost:3000
@authToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2NGMzZmNjZWMwNjkwNWZiMTMxNjQ0YWUiLCJyb2xlIjoic3VwZXJhZG1pbiIsImlhdCI6MTc0MzE4NDIxNCwiZXhwIjoxNzQzMjIwMjE0fQ.MmaM0TVhlcOr6iniyv-RQJ5ExcRY36QrxYUFaRbaW6g
@vendorAuthToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************.FZsrK8dVMOfrgXmzHl6vYJC6XvWZfYLhr2Xw36LeJBM

@organizationId = 507f1f77bcf86cd799439011
@userId = 67e702b79d547db84ea03a09
@companyId = 67e6e91b5fc0062804cc33d0
@cityId = 67e6eb5a3d4b1f68feb580d6
@crewId = 67e6f1a6e03dc33e296536ec
@neighborhoodId = 67e7075a70b6522ea351be41

### ==================== USERS ====================

### Get all users
GET {{baseUrl}}/vendor-platform/users
Authorization: Bearer {{vendorAuthToken}}

### Get user by ID
GET {{baseUrl}}/vendor-platform/users/{{userId}}
Authorization: Bearer {{vendorAuthToken}}

### Get users by organization
GET {{baseUrl}}/vendor-platform/organizations/{{organizationId}}/users
Authorization: Bearer {{vendorAuthToken}}

### Get users by company
GET {{baseUrl}}/vendor-platform/companies/{{companyId}}/users
Authorization: Bearer {{vendorAuthToken}}

### ==================== COMPANY PERMISSIONS ====================

### Invitar usuario a una compañía
POST {{baseUrl}}/vendor-platform/companies/{{companyId}}/users/invite
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json
origin: http://localhost:5000

{
    "email": "<EMAIL>",
    "name": "Pedro Guest",
    "role": "operator"
}

### Obtener permisos de usuario en una compañía
GET {{baseUrl}}/vendor-platform/companies/{{companyId}}/users/{{userId}}/permissions
Authorization: Bearer {{vendorAuthToken}}

### Actualizar permisos de usuario en una compañía
PUT {{baseUrl}}/vendor-platform/companies/{{companyId}}/users/{{userId}}/permissions
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
    "role": "supervisor",
    "allowedCities": ["{{cityId}}"],
    "allowedCrews": ["{{crewId}}"]
}

### Eliminar permisos de usuario en una compañía
DELETE {{baseUrl}}/vendor-platform/companies/{{companyId}}/users/{{userId}}/permissions
Authorization: Bearer {{authToken}}

### ==================== COMPANIES ====================

### Crear nueva compañía
POST {{baseUrl}}/vendor-platform/companies
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "Empresa Ejemplo",
    "address": {
        "street": "Calle Principal",
        "number": "123",
        "colony": "Centro",
        "city": "Ciudad de México",
        "postalCode": "06000"
    },
    "contactInfo": {
        "email": "<EMAIL>",
        "phone": "+525512345678"
    }
}

### Obtener todas las compañías
GET {{baseUrl}}/vendor-platform/companies
Authorization: Bearer {{vendorAuthToken}}

### Obtener compañía por ID
GET {{baseUrl}}/vendor-platform/companies/{{companyId}}
Authorization: Bearer {{vendorAuthToken}}

### Actualizar compañía
PUT {{baseUrl}}/vendor-platform/companies/{{companyId}}
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "Empresa Ejemplo Actualizada",
    "address": {
        "street": "Nueva Calle",
        "number": "456",
        "colony": "Nueva Colonia",
        "city": "Ciudad de México",
        "postalCode": "06100"
    }
}

### Eliminar compañía
DELETE {{baseUrl}}/vendor-platform/companies/{{companyId}}
Authorization: Bearer {{vendorAuthToken}}

### ==================== CITIES ====================

### Crear nueva ciudad
POST {{baseUrl}}/vendor-platform/cities
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "Ciudad de México",
    "state": "cdmx",
    "country": "México",
    "timezone": "America/Mexico_City",
    "companyId": "{{companyId}}"
}

### Obtener todas las ciudades
GET {{baseUrl}}/vendor-platform/cities
Authorization: Bearer {{vendorAuthToken}}

### Obtener ciudad por ID
GET {{baseUrl}}/vendor-platform/cities/{{cityId}}
Authorization: Bearer {{vendorAuthToken}}

### Actualizar ciudad
PUT {{baseUrl}}/vendor-platform/cities/{{cityId}}
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "CDMX",
    "state": "Ciudad de México"
}

### Eliminar ciudad
DELETE {{baseUrl}}/vendor-platform/cities/{{cityId}}
Authorization: Bearer {{vendorAuthToken}}

### ==================== CREWS ====================

### Crear nueva cuadrilla
POST {{baseUrl}}/vendor-platform/crews
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "Cuadrilla 2",
    "cityId": "{{cityId}}"
}

### Obtener todas las cuadrillas
GET {{baseUrl}}/vendor-platform/crews
Authorization: Bearer {{vendorAuthToken}}

### Obtener cuadrillas por ciudad
GET {{baseUrl}}/vendor-platform/cities/{{cityId}}/crews
Authorization: Bearer {{vendorAuthToken}}

### Obtener cuadrilla por ID
GET {{baseUrl}}/vendor-platform/crews/{{crewId}}
Authorization: Bearer {{vendorAuthToken}}

### Actualizar cuadrilla
PUT {{baseUrl}}/vendor-platform/crews/{{crewId}}
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "Cuadrilla Norte Actualizada"
}

### Eliminar cuadrilla
DELETE {{baseUrl}}/vendor-platform/crews/{{crewId}}
Authorization: Bearer {{vendorAuthToken}}

### ==================== NEIGHBORHOODS ====================

### Crear nueva colonia
POST {{baseUrl}}/vendor-platform/neighborhoods
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "Del Valle",
    "crewId": "{{crewId}}",
    "scheduleConfig": {
        "maxSimultaneousInstallations": 2,
        "installationDuration": 180,
        "weeklySchedule": {
            "monday": {
                "start": "09:00",
                "end": "18:00"
            },
            "tuesday": {
                "start": "09:00",
                "end": "18:00"
            },
            "wednesday": {
                "start": "09:00",
                "end": "18:00"
            }
        }
    },
    "address": {
        "street": "Av. Principal",
        "number": "123",
        "colony": "Centro",
        "city": "Ciudad de México",
        "postalCode": "06000"
    }
}

### Obtener todas las colonias
GET {{baseUrl}}/vendor-platform/neighborhoods
Authorization: Bearer {{vendorAuthToken}}

### Obtener colonia por ID
GET {{baseUrl}}/vendor-platform/neighborhoods/{{neighborhoodId}}
Authorization: Bearer {{vendorAuthToken}}

### Actualizar colonia
PUT {{baseUrl}}/vendor-platform/neighborhoods/{{neighborhoodId}}
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "Del Valle Sur",
    "scheduleConfig": {
        "maxSimultaneousInstallations": 3
    }
}

### Eliminar colonia
DELETE {{baseUrl}}/vendor-platform/neighborhoods/{{neighborhoodId}}
Authorization: Bearer {{vendorAuthToken}}

### ==================== INSTALLATIONS ====================

### Obtener slots disponibles para instalación
GET {{baseUrl}}/vendor-platform/installation-slots/{{neighborhoodId}}/2024-01-20
Authorization: Bearer {{vendorAuthToken}}

### Crear cita de instalación
POST {{baseUrl}}/vendor-platform/installation-appointments
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "neighborhoodId": "{{neighborhoodId}}",
    "startTime": "{{startTime}}",
    "customerInfo": {
        "name": "Juan Pérez",
        "email": "<EMAIL>",
        "phone": "+525512345678"
    },
    "address": {
        "street": "Calle Cliente",
        "number": "789",
        "colony": "Del Valle",
        "city": "Ciudad de México",
        "postalCode": "03100"
    }
}

### ==================== INSTALLATION APPOINTMENTS ====================

@startTime = 2024-01-20T14:00:00.000Z
@today = 2025-04-02

### Crear una nueva cita de instalación
POST {{baseUrl}}/vendor-platform/installation-appointments
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
    "neighborhoodId": "{{neighborhoodId}}",
    "cityId": "{{cityId}}",
    "crewId": "{{crewId}}",
    "companyId": "{{companyId}}",
    "startTime": "{{startTime}}",
    "associateId": "67e6e91b5fc0062804cc33d0"
}

### Obtener citas programadas para un día específico
GET {{baseUrl}}/vendor-platform/installation-appointments/{{today}}
Authorization: Bearer {{authToken}}

### Obtener detalles de una cita específica
@appointmentId = 507f1f77bcf86cd799439012
GET {{baseUrl}}/vendor-platform/installation-appointments/{{appointmentId}}
Authorization: Bearer {{authToken}}

### Cancelar una cita
DELETE {{baseUrl}}/vendor-platform/installation-appointments/{{appointmentId}}
Authorization: Bearer {{authToken}}

### Actualizar estado de una cita
PATCH {{baseUrl}}/vendor-platform/installation-appointments/{{appointmentId}}/status
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
    "status": "completed",
    "notes": "Instalación finalizada exitosamente"
}

### ==================== VENDOR PLATFORM USERS ====================

### Invitar usuario owner (desde panel admin)
POST {{baseUrl}}/vendor-platform/vendor/invite-user
Authorization: Bearer {{authToken}}
Content-Type: application/json
origin: http://localhost:5000

{
    "email": "<EMAIL>",
    "name": "Cesar Casillas",
    "userType": "company"
}

### Invitar usuario a vendor platform (tipo workshop por defecto)
POST {{baseUrl}}/vendor-platform/vendor/invite-user
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
    "email": "<EMAIL>",
    "organizationId": "{{organizationId}}",
    "name": "Pedro Rodríguez"
}

### Reenviar invitación a usuario
POST {{baseUrl}}/vendor-platform/vendors/invite-user?resend=true
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
    "email": "<EMAIL>",
    "organizationId": "{{organizationId}}"
}

### Obtener lista de usuarios invitados por organización
GET {{baseUrl}}/vendor-platform/vendors/{{organizationId}}/invited-users
Authorization: Bearer {{authToken}}




